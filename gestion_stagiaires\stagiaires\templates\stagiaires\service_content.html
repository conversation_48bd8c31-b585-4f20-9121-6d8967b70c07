{% extends 'stagiaires/base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>
                        {{ title }}
                    </h3>
                    <p class="mb-0 mt-2 opacity-75">
                        Thématiques et sujets disponibles pour votre service
                    </p>
                </div>
                <div class="card-body">
                    <!-- Informations du service -->
                    <div class="alert alert-info mb-4">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Informations du service
                                </h5>
                                <p class="mb-1"><strong>Nom :</strong> {{ service.nom }}</p>
                                <p class="mb-1"><strong>Code :</strong> {{ service.code_service }}</p>
                                {% if service.description %}
                                <p class="mb-0"><strong>Description :</strong> {{ service.description }}</p>
                                {% endif %}
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="badge bg-success fs-6">
                                    <i class="fas fa-check-circle me-1"></i>
                                    Service actif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Thématiques du service -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-tags me-2"></i>
                                        Thématiques ({{ thematiques.count }})
                                    </h5>
                                    <a href="{% url 'add_thematique' %}" class="btn btn-light btn-sm">
                                        <i class="fas fa-plus me-1"></i>Ajouter
                                    </a>
                                </div>
                                <div class="card-body">
                                    {% if thematiques %}
                                        <div class="list-group list-group-flush">
                                            {% for thematique in thematiques %}
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">{{ thematique.titre }}</h6>
                                                    {% if thematique.description %}
                                                    <p class="mb-1 text-muted small">{{ thematique.description|truncatewords:15 }}</p>
                                                    {% endif %}
                                                    <small class="text-muted">
                                                        {% if thematique.service %}
                                                            <i class="fas fa-building me-1"></i>{{ thematique.service.nom }}
                                                        {% else %}
                                                            <i class="fas fa-globe me-1"></i>Thématique générale
                                                        {% endif %}
                                                    </small>
                                                </div>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'edit_thematique' thematique.id %}" class="btn btn-outline-primary" title="Modifier">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-outline-danger" title="Supprimer" onclick="confirmDelete('thematique', {{ thematique.id }}, '{{ thematique.titre }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <div class="text-center py-4">
                                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">Aucune thématique disponible pour ce service.</p>
                                            <a href="{% url 'add_thematique' %}" class="btn btn-primary">
                                                <i class="fas fa-plus me-1"></i>Créer la première thématique
                                            </a>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Sujets du service -->
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="fas fa-lightbulb me-2"></i>
                                        Sujets ({{ sujets.count }})
                                    </h5>
                                    <a href="{% url 'add_sujet' %}" class="btn btn-light btn-sm">
                                        <i class="fas fa-plus me-1"></i>Ajouter
                                    </a>
                                </div>
                                <div class="card-body">
                                    {% if sujets %}
                                        <div class="list-group list-group-flush">
                                            {% for sujet in sujets %}
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">{{ sujet.titre }}</h6>
                                                    {% if sujet.description %}
                                                    <p class="mb-1 text-muted small">{{ sujet.description|truncatewords:15 }}</p>
                                                    {% endif %}
                                                    <small class="text-muted">
                                                        <i class="fas fa-tag me-1"></i>{{ sujet.thematique.titre }}
                                                        {% if sujet.encadrant %}
                                                            | <i class="fas fa-user me-1"></i>{{ sujet.encadrant.get_full_name }}
                                                        {% endif %}
                                                    </small>
                                                </div>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'edit_sujet' sujet.id %}" class="btn btn-outline-primary" title="Modifier">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-outline-danger" title="Supprimer" onclick="confirmDelete('sujet', {{ sujet.id }}, '{{ sujet.titre }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <div class="text-center py-4">
                                            <i class="fas fa-lightbulb fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">Aucun sujet disponible pour ce service.</p>
                                            <a href="{% url 'add_sujet' %}" class="btn btn-success">
                                                <i class="fas fa-plus me-1"></i>Créer le premier sujet
                                            </a>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions rapides -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">
                                        <i class="fas fa-bolt me-2"></i>
                                        Actions rapides
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <a href="{% url 'add_thematique' %}" class="btn btn-outline-primary w-100 mb-2">
                                                <i class="fas fa-plus-circle me-1"></i>
                                                Nouvelle thématique
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{% url 'add_sujet' %}" class="btn btn-outline-success w-100 mb-2">
                                                <i class="fas fa-lightbulb me-1"></i>
                                                Nouveau sujet
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{% url 'thematiques_list' %}" class="btn btn-outline-info w-100 mb-2">
                                                <i class="fas fa-list me-1"></i>
                                                Toutes les thématiques
                                            </a>
                                        </div>
                                        <div class="col-md-3">
                                            <a href="{% url 'sujets_list' %}" class="btn btn-outline-secondary w-100 mb-2">
                                                <i class="fas fa-list-alt me-1"></i>
                                                Tous les sujets
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Retour au tableau de bord -->
                    <div class="text-center mt-4">
                        <a href="{% url 'dashboard' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour au tableau de bord
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(type, id, name) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer ${type === 'thematique' ? 'la thématique' : 'le sujet'} "${name}" ?`)) {
        // Rediriger vers l'URL de suppression appropriée
        if (type === 'thematique') {
            window.location.href = `/thematiques/${id}/delete/`;
        } else {
            window.location.href = `/sujets/${id}/delete/`;
        }
    }
}
</script>
{% endblock %}
