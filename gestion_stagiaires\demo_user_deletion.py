#!/usr/bin/env python
"""
Script de démonstration pour la suppression d'utilisateurs
Crée des données de test pour tester manuellement l'interface
"""

import os
import sys
import django
from datetime import date

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser, Stagiaire

def create_demo_users():
    """Créer des utilisateurs de démonstration"""
    print("🧪 Création d'utilisateurs de démonstration...")
    print("=" * 60)
    
    # 1. Créer un admin de test
    admin_user, created = CustomUser.objects.get_or_create(
        username='demo_admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Admin',
            'last_name': 'Demo',
            'role': 'ADMIN',
            'is_active': True,
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('demo123')
        admin_user.save()
        print(f"✅ Admin créé : {admin_user.get_full_name()}")
    else:
        print(f"✅ Admin existant : {admin_user.get_full_name()}")
    
    # 2. Créer un utilisateur sans dépendances (peut être supprimé)
    user_deletable, created = CustomUser.objects.get_or_create(
        username='user_deletable',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Jean',
            'last_name': 'Supprimable',
            'role': 'ENCADRANT',
            'is_active': True
        }
    )
    if created:
        user_deletable.set_password('test123')
        user_deletable.save()
        print(f"✅ Utilisateur supprimable créé : {user_deletable.get_full_name()}")
    else:
        print(f"✅ Utilisateur supprimable existant : {user_deletable.get_full_name()}")
    
    # 3. Créer un utilisateur inactif (pour tester l'activation)
    user_inactive, created = CustomUser.objects.get_or_create(
        username='user_inactive',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Marie',
            'last_name': 'Inactive',
            'role': 'RH',
            'is_active': False
        }
    )
    if created:
        user_inactive.set_password('test123')
        user_inactive.save()
        print(f"✅ Utilisateur inactif créé : {user_inactive.get_full_name()}")
    else:
        print(f"✅ Utilisateur inactif existant : {user_inactive.get_full_name()}")
    
    # 4. Créer un utilisateur avec dépendances (ne peut pas être supprimé)
    user_with_deps, created = CustomUser.objects.get_or_create(
        username='user_with_deps',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Pierre',
            'last_name': 'AvecDependances',
            'role': 'ENCADRANT',
            'is_active': True
        }
    )
    if created:
        user_with_deps.set_password('test123')
        user_with_deps.save()
        print(f"✅ Utilisateur avec dépendances créé : {user_with_deps.get_full_name()}")
        
        # Créer un stagiaire pour créer une dépendance
        stagiaire = Stagiaire.objects.create(
            nom='TestStagiaire',
            prenom='Dependance',
            email='<EMAIL>',
            date_naissance=date(2000, 1, 1),
            telephone='0123456789',
            departement='Informatique',
            encadrant=user_with_deps,
            date_debut=date.today(),
            date_fin=date.today(),
            etablissement='Université de Test',
            niveau_etude='Master',
            specialite='Développement Web',
            cree_par=user_with_deps
        )
        print(f"✅ Stagiaire de dépendance créé : {stagiaire.nom_complet}")
    else:
        print(f"✅ Utilisateur avec dépendances existant : {user_with_deps.get_full_name()}")
    
    return admin_user, user_deletable, user_inactive, user_with_deps

def show_demo_instructions():
    """Afficher les instructions de démonstration"""
    print("\n" + "=" * 60)
    print("🎯 INSTRUCTIONS DE DÉMONSTRATION")
    print("=" * 60)
    
    print("\n1. 🌐 ACCÉDER À L'INTERFACE :")
    print("   URL : http://127.0.0.1:8000/users/")
    print("   Login : demo_admin")
    print("   Mot de passe : demo123")
    
    print("\n2. 🧪 TESTS À EFFECTUER :")
    
    print("\n   📋 Test 1 - Suppression réussie :")
    print("   • Chercher l'utilisateur 'Jean Supprimable'")
    print("   • Cliquer sur le bouton rouge 🗑️ (Supprimer)")
    print("   • Confirmer la suppression")
    print("   • ✅ Résultat attendu : Message de succès + utilisateur supprimé")
    
    print("\n   📋 Test 2 - Suppression bloquée :")
    print("   • Chercher l'utilisateur 'Pierre AvecDependances'")
    print("   • Cliquer sur le bouton rouge 🗑️ (Supprimer)")
    print("   • Confirmer la suppression")
    print("   • ❌ Résultat attendu : Message d'erreur avec détails des dépendances")
    
    print("\n   📋 Test 3 - Activation d'utilisateur :")
    print("   • Chercher l'utilisateur 'Marie Inactive' (badge rouge 'Inactif')")
    print("   • Cliquer sur le bouton vert ✅ (Activer)")
    print("   • Confirmer l'activation")
    print("   • ✅ Résultat attendu : Badge devient vert 'Actif'")
    
    print("\n   📋 Test 4 - Désactivation d'utilisateur :")
    print("   • Chercher un utilisateur actif")
    print("   • Cliquer sur le bouton gris 🚫 (Désactiver)")
    print("   • Confirmer la désactivation")
    print("   • ✅ Résultat attendu : Badge devient rouge 'Inactif'")
    
    print("\n3. 🔒 TESTS DE SÉCURITÉ :")
    print("   • Essayer de supprimer votre propre compte (doit être bloqué)")
    print("   • Se connecter avec un compte RH et essayer d'accéder à /users/")
    print("   • Vérifier que seuls les admins peuvent supprimer")
    
    print("\n4. 🎨 ÉLÉMENTS D'INTERFACE À VÉRIFIER :")
    print("   • Messages d'alerte colorés (vert=succès, rouge=erreur)")
    print("   • Rechargement automatique de la page après action")
    print("   • Badges de statut colorés")
    print("   • Boutons d'action avec icônes")
    
    print("\n5. 📊 DONNÉES DE TEST CRÉÉES :")
    users = CustomUser.objects.filter(username__in=[
        'demo_admin', 'user_deletable', 'user_inactive', 'user_with_deps'
    ])
    for user in users:
        status = "🟢 Actif" if user.is_active else "🔴 Inactif"
        role_icon = {"ADMIN": "👑", "RH": "📋", "ENCADRANT": "👨‍🏫"}.get(user.role, "👤")
        print(f"   {role_icon} {user.get_full_name()} ({user.username}) - {status}")
    
    stagiaires = Stagiaire.objects.filter(nom='TestStagiaire')
    for stagiaire in stagiaires:
        print(f"   👨‍🎓 {stagiaire.nom_complet} (encadré par {stagiaire.encadrant.get_full_name()})")

def check_server_status():
    """Vérifier que le serveur Django fonctionne"""
    print("\n🔍 Vérification du serveur Django...")
    
    try:
        import requests
        response = requests.get('http://127.0.0.1:8000/', timeout=5)
        if response.status_code in [200, 302]:  # 302 = redirection vers login
            print("✅ Serveur Django accessible")
            return True
        else:
            print(f"⚠️ Serveur répond avec le code {response.status_code}")
            return False
    except ImportError:
        print("⚠️ Module 'requests' non installé, impossible de vérifier le serveur")
        print("   Vérifiez manuellement : http://127.0.0.1:8000/")
        return True
    except Exception as e:
        print(f"❌ Serveur non accessible : {e}")
        print("   Démarrez le serveur avec : python manage.py runserver")
        return False

def main():
    """Fonction principale"""
    print("🎭 DÉMONSTRATION - SUPPRESSION D'UTILISATEURS")
    print("🔧 Configuration de l'environnement de test")
    print("=" * 70)
    
    try:
        # Créer les utilisateurs de démonstration
        admin_user, user_deletable, user_inactive, user_with_deps = create_demo_users()
        
        # Vérifier le serveur
        server_ok = check_server_status()
        
        # Afficher les instructions
        show_demo_instructions()
        
        if server_ok:
            print("\n" + "=" * 60)
            print("🚀 PRÊT POUR LA DÉMONSTRATION !")
            print("=" * 60)
            print("   1. Ouvrez votre navigateur")
            print("   2. Allez sur : http://127.0.0.1:8000/users/")
            print("   3. Connectez-vous avec : demo_admin / demo123")
            print("   4. Testez les fonctionnalités de suppression")
        else:
            print("\n" + "=" * 60)
            print("⚠️ SERVEUR NON ACCESSIBLE")
            print("=" * 60)
            print("   Démarrez d'abord le serveur avec :")
            print("   python manage.py runserver")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la configuration : {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
