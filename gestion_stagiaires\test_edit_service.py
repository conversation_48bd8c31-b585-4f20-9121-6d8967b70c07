#!/usr/bin/env python
"""
Test de l'édition de services
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Service
from django.test import Client
from django.urls import reverse

User = get_user_model()

def test_edit_service():
    """Test de l'édition de services"""
    
    print("=== Test de l'édition de services ===")
    
    # 1. Vérifier les services existants
    print("\n1️⃣ Services existants:")
    services = Service.objects.all()
    for service in services:
        print(f"   • ID: {service.id} - {service.nom} ({service.code_service})")
    
    if not services.exists():
        print("❌ Aucun service trouvé")
        return
    
    # 2. Prendre le premier service pour le test
    service_test = services.first()
    print(f"\n2️⃣ Service de test: {service_test.nom} (ID: {service_test.id})")
    
    # 3. Vérifier les utilisateurs admin
    admin_users = User.objects.filter(is_superuser=True)
    print(f"\n3️⃣ Administrateurs disponibles: {admin_users.count()}")
    
    if not admin_users.exists():
        print("❌ Aucun administrateur trouvé")
        return
    
    admin_user = admin_users.first()
    print(f"   Admin de test: {admin_user.username}")
    
    # 4. Test d'accès à la page d'édition
    print(f"\n4️⃣ Test d'accès à la page d'édition:")
    
    client = Client()
    client.force_login(admin_user)
    
    # URL d'édition
    edit_url = reverse('edit_service', kwargs={'service_id': service_test.id})
    print(f"   URL: {edit_url}")
    
    try:
        response = client.get(edit_url)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Page d'édition accessible")
            
            # Vérifier le contenu de la page
            content = response.content.decode('utf-8')
            
            if 'Modifier le service' in content:
                print("   ✅ Titre correct trouvé")
            else:
                print("   ⚠️ Titre non trouvé")
            
            if service_test.nom in content:
                print(f"   ✅ Nom du service '{service_test.nom}' trouvé")
            else:
                print(f"   ⚠️ Nom du service '{service_test.nom}' non trouvé")
            
            # Vérifier la présence du formulaire
            if 'form' in content and 'csrf' in content:
                print("   ✅ Formulaire avec CSRF trouvé")
            else:
                print("   ⚠️ Formulaire ou CSRF manquant")
                
        elif response.status_code == 302:
            print(f"   ↗️ Redirection vers: {response.url}")
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
    
    # 5. Test de modification
    print(f"\n5️⃣ Test de modification du service:")
    
    try:
        # Données de modification
        nouveau_nom = f"{service_test.nom} - Modifié"
        nouvelle_description = "Description modifiée par le test"
        
        form_data = {
            'nom': nouveau_nom,
            'code_service': service_test.code_service,
            'description': nouvelle_description,
            'actif': True
        }
        
        # Soumettre le formulaire
        response = client.post(edit_url, data=form_data)
        print(f"   Status de soumission: {response.status_code}")
        
        if response.status_code == 302:
            print("   ✅ Redirection après modification (succès probable)")
            
            # Vérifier que le service a été modifié
            service_test.refresh_from_db()
            
            if service_test.nom == nouveau_nom:
                print(f"   ✅ Nom modifié avec succès: '{nouveau_nom}'")
            else:
                print(f"   ❌ Nom non modifié. Actuel: '{service_test.nom}'")
            
            if service_test.description == nouvelle_description:
                print(f"   ✅ Description modifiée avec succès")
            else:
                print(f"   ❌ Description non modifiée")
            
            # Restaurer les valeurs originales
            service_test.nom = service_test.nom.replace(' - Modifié', '')
            service_test.description = ''
            service_test.save()
            print("   🔄 Valeurs originales restaurées")
            
        else:
            print(f"   ❌ Erreur lors de la modification: {response.status_code}")
            
            if response.status_code == 200:
                # Analyser les erreurs du formulaire
                content = response.content.decode('utf-8')
                if 'errorlist' in content:
                    print("   📋 Erreurs de formulaire détectées")
                    import re
                    errors = re.findall(r'<ul class="errorlist[^>]*">(.*?)</ul>', content, re.DOTALL)
                    for i, error in enumerate(errors[:3]):
                        clean_error = re.sub(r'<[^>]+>', '', error).strip()
                        if clean_error:
                            print(f"      {i+1}. {clean_error}")
    
    except Exception as e:
        print(f"   ❌ Erreur lors du test de modification: {e}")
        import traceback
        traceback.print_exc()
    
    # 6. Test avec un encadrant
    print(f"\n6️⃣ Test avec un encadrant:")
    
    encadrants = User.objects.filter(role='ENCADRANT', is_active=True)
    if encadrants.exists():
        encadrant = encadrants.first()
        print(f"   Encadrant de test: {encadrant.username}")
        
        client_encadrant = Client()
        client_encadrant.force_login(encadrant)
        
        try:
            response = client_encadrant.get(edit_url)
            print(f"   Status pour encadrant: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ Encadrant peut accéder à l'édition")
            elif response.status_code == 302:
                print("   ↗️ Encadrant redirigé (permissions limitées)")
            else:
                print(f"   ❌ Erreur pour encadrant: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Erreur avec encadrant: {e}")
    else:
        print("   ⚠️ Aucun encadrant trouvé pour le test")
    
    print(f"\n{'='*50}")
    print("📊 RÉSUMÉ DU TEST:")
    print("✅ Si tous les tests passent, l'édition de services fonctionne")
    print("❌ Si des erreurs persistent, vérifiez:")
    print("   • Les permissions utilisateur")
    print("   • La configuration des URLs")
    print("   • Le template edit_service.html")
    print("   • Les imports dans views.py")
    print(f"{'='*50}")

if __name__ == '__main__':
    test_edit_service()
