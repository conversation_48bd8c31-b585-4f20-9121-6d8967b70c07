#!/usr/bin/env python
"""
Script pour tester la nouvelle colonne "Statut Période"
"""

import os
import sys
import django
from datetime import date, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service

User = get_user_model()

def test_statut_periode():
    """Test de la colonne Statut Période avec différents scénarios"""
    
    print("🧪 TEST DE LA COLONNE STATUT PÉRIODE")
    print("=" * 50)
    
    # Récupérer un service pour les tests
    try:
        service = Service.objects.first()
        if not service:
            print("❌ Aucun service trouvé")
            return
    except Exception as e:
        print(f"❌ Erreur service : {e}")
        return
    
    # Récupérer un encadrant pour les tests
    try:
        encadrant = User.objects.filter(role='ENCADRANT').first()
        if not encadrant:
            print("❌ Aucun encadrant trouvé")
            return
    except Exception as e:
        print(f"❌ Erreur encadrant : {e}")
        return
    
    # Créer des stagiaires de test avec différentes dates de fin
    scenarios = [
        {
            'nom': 'TestVert',
            'prenom': 'Stagiaire',
            'jours_restants': 15,  # > 10 jours = VERT
            'description': 'Plus de 10 jours restants'
        },
        {
            'nom': 'TestOrange',
            'prenom': 'Stagiaire',
            'jours_restants': 10,  # = 10 jours = ORANGE
            'description': 'Exactement 10 jours restants'
        },
        {
            'nom': 'TestRouge',
            'prenom': 'Stagiaire',
            'jours_restants': 5,   # < 10 jours = ROUGE
            'description': 'Moins de 10 jours restants'
        },
        {
            'nom': 'TestUrgent',
            'prenom': 'Stagiaire',
            'jours_restants': 1,   # 1 jour = ROUGE
            'description': '1 jour restant'
        },
        {
            'nom': 'TestTermine',
            'prenom': 'Stagiaire',
            'jours_restants': -5,  # Terminé = NOIR
            'description': 'Stage terminé'
        }
    ]
    
    stagiaires_crees = []
    
    for scenario in scenarios:
        # Calculer la date de fin
        date_fin = date.today() + timedelta(days=scenario['jours_restants'])
        
        # Supprimer le stagiaire s'il existe déjà
        Stagiaire.objects.filter(nom=scenario['nom'], prenom=scenario['prenom']).delete()
        
        # Créer le stagiaire
        stagiaire = Stagiaire.objects.create(
            nom=scenario['nom'],
            prenom=scenario['prenom'],
            email=f"{scenario['nom'].lower()}@test.com",
            telephone='0123456789',
            date_naissance=date(2000, 1, 1),
            departement='IT',
            service=service,
            encadrant=encadrant,
            date_debut=date.today() - timedelta(days=30),
            date_fin=date_fin,
            etablissement='Test University',
            niveau_etude='Master',
            specialite='Test',
            statut='EN_COURS'
        )
        
        stagiaires_crees.append(stagiaire)
        
        # Calculer les jours restants
        jours_restants = (date_fin - date.today()).days
        
        # Déterminer la couleur
        if jours_restants > 10:
            couleur = "🟢 VERT"
        elif jours_restants == 10:
            couleur = "🟠 ORANGE"
        elif jours_restants >= 0:
            couleur = "🔴 ROUGE"
        else:
            couleur = "⚫ NOIR"
        
        print(f"✅ {stagiaire.nom_complet}")
        print(f"   • Date fin : {date_fin.strftime('%d/%m/%Y')}")
        print(f"   • Jours restants : {jours_restants}")
        print(f"   • Couleur attendue : {couleur}")
        print(f"   • Description : {scenario['description']}")
        print()
    
    print(f"📊 RÉSUMÉ DES COULEURS :")
    print(f"   🟢 VERT   : Plus de 10 jours restants")
    print(f"   🟠 ORANGE : Exactement 10 jours restants")
    print(f"   🔴 ROUGE  : Moins de 10 jours restants (mais pas terminé)")
    print(f"   ⚫ NOIR   : Stage terminé (date dépassée)")
    
    print(f"\n🔗 URL pour voir le résultat :")
    print(f"   http://127.0.0.1:8000/stagiaires/?filtre=tous")
    
    print(f"\n📝 Stagiaires de test créés : {len(stagiaires_crees)}")
    for stagiaire in stagiaires_crees:
        print(f"   • {stagiaire.nom_complet} (ID: {stagiaire.id})")
    
    return stagiaires_crees

def nettoyer_stagiaires_test():
    """Nettoyer les stagiaires de test"""
    noms_test = ['TestVert', 'TestOrange', 'TestRouge', 'TestUrgent', 'TestTermine']
    
    for nom in noms_test:
        count = Stagiaire.objects.filter(nom=nom).count()
        if count > 0:
            Stagiaire.objects.filter(nom=nom).delete()
            print(f"🗑️ Supprimé {count} stagiaire(s) de test avec le nom '{nom}'")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'clean':
        print("🧹 NETTOYAGE DES STAGIAIRES DE TEST")
        print("=" * 40)
        nettoyer_stagiaires_test()
    else:
        test_statut_periode()
