{% extends 'stagiaires/base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-building me-2"></i>Services</h1>
        {% if can_add %}
        <a href="{% url 'add_service' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>Ajouter un service
        </a>
        {% endif %}
    </div>
    
    {% if messages %}
    <div class="messages">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <div class="card">
        <div class="card-body">
            {% if services %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Nom</th>
                            <th>Code</th>
                            <th>Responsable</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for service in services %}
                        <tr>
                            <td>{{ service.nom }}</td>
                            <td>{{ service.code_service }}</td>
                            <td>
                                {% if service.responsable %}
                                {{ service.responsable.get_full_name|default:service.responsable.username }}
                                {% else %}
                                <span class="text-muted">Non défini</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if service.actif %}
                                <span class="badge bg-success">Actif</span>
                                {% else %}
                                <span class="badge bg-danger">Inactif</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.role == 'ADMIN' or user.service.id == service.id %}
                                <a href="{% url 'edit_service' service.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.role == 'ADMIN' %}
                                <button class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ service.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                Aucun service n'a été trouvé.
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modals de suppression -->
{% for service in services %}
{% if user.role == 'ADMIN' %}
<div class="modal fade" id="deleteModal{{ service.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ service.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel{{ service.id }}">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir supprimer le service <strong>{{ service.nom }}</strong> ?
                <div class="alert alert-warning mt-2">
                    <i class="fas fa-exclamation-triangle me-2"></i>Cette action est irréversible.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <a href="{% url 'delete_service' service.id %}" class="btn btn-danger">Supprimer</a>
                <a href="{% url 'edit_service' service.id %}" class="btn btn-sm btn-warning">
    <i class="fas fa-edit"></i> Modifier
</a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}
{% endblock %}

