{% extends 'stagiaires/base.html' %}

{% block title %}{{ title }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>
                        Niveaux d'études
                    </h3>
                    {% if user.role == 'ADMIN' %}
                    <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addNiveauModal">
                        <i class="fas fa-plus me-1"></i>Ajouter un niveau
                    </button>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if niveaux %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Niveau d'étude</th>
                                    <th>Nombre de stagiaires</th>
                                    {% if user.role == 'ADMIN' %}
                                    <th>Actions</th>
                                    {% endif %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for niveau in niveaux %}
                                <tr>
                                    <td>{{ niveau.niveau_etude }}</td>
                                    <td>{{ niveau.stagiaires_count }}</td>
                                    {% if user.role == 'ADMIN' %}
                                    <td>
                                        <button class="btn btn-sm btn-danger" onclick="confirmDeleteNiveau('{{ niveau.niveau_etude }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                    {% endif %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Aucun niveau d'étude n'a été enregistré pour le moment.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour ajouter un niveau -->
{% if user.role == 'ADMIN' %}
<div class="modal fade" id="addNiveauModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Ajouter un niveau d'étude</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'add_niveau' %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="niveau_etude" class="form-label">Niveau d'étude</label>
                        <input type="text" class="form-control" id="niveau_etude" name="niveau_etude" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function confirmDeleteNiveau(niveau) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le niveau "${niveau}" ?`)) {
        // Créer un formulaire pour envoyer une requête POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = "{% url 'delete_niveau' %}";
        
        // Ajouter le token CSRF
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = '{{ csrf_token }}';
        form.appendChild(csrfToken);
        
        // Ajouter le niveau à supprimer
        const niveauInput = document.createElement('input');
        niveauInput.type = 'hidden';
        niveauInput.name = 'niveau_etude';
        niveauInput.value = niveau;
        form.appendChild(niveauInput);
        
        // Ajouter le formulaire au document et le soumettre
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endif %}
{% endblock %}

