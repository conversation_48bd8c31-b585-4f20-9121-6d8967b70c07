{% extends 'stagiaires/base.html' %}

{% block title %}Générer Attestation - {{ stagiaire.nom_complet }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-certificate me-2"></i>
                        Génération d'Attestation de Fin de Stage
                    </h4>
                    <small>{{ stagiaire.nom_complet }} - {{ stagiaire.get_departement_display }}</small>
                </div>
                <div class="card-body">
                    <!-- Vérifications préalables -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6><i class="fas fa-clipboard-check me-2"></i>Vérifications préalables</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card border-{% if stagiaire.stage_termine %}success{% else %}danger{% endif %}">
                                        <div class="card-body text-center">
                                            <i class="fas fa-calendar-check fa-2x {% if stagiaire.stage_termine %}text-success{% else %}text-danger{% endif %} mb-2"></i>
                                            <h6>Stage terminé</h6>
                                            <span class="badge bg-{% if stagiaire.stage_termine %}success{% else %}danger{% endif %}">
                                                {% if stagiaire.stage_termine %}✓ Oui{% else %}✗ Non{% endif %}
                                            </span>
                                            {% if not stagiaire.stage_termine %}
                                                <br><small class="text-muted">Fin prévue : {{ stagiaire.date_fin }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-{% if stagiaire.convention_validee %}success{% else %}danger{% endif %}">
                                        <div class="card-body text-center">
                                            <i class="fas fa-file-contract fa-2x {% if stagiaire.convention_validee %}text-success{% else %}text-danger{% endif %} mb-2"></i>
                                            <h6>Convention validée</h6>
                                            <span class="badge bg-{% if stagiaire.convention_validee %}success{% else %}danger{% endif %}">
                                                {% if stagiaire.convention_validee %}✓ Oui{% else %}✗ Non{% endif %}
                                            </span>
                                            <br><small class="text-muted">{{ stagiaire.get_statut_convention_display }}</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-{% if stagiaire.taches_accomplies %}success{% else %}danger{% endif %}">
                                        <div class="card-body text-center">
                                            <i class="fas fa-tasks fa-2x {% if stagiaire.taches_accomplies %}text-success{% else %}text-danger{% endif %} mb-2"></i>
                                            <h6>Tâches accomplies</h6>
                                            <span class="badge bg-{% if stagiaire.taches_accomplies %}success{% else %}danger{% endif %}">
                                                {% if stagiaire.taches_accomplies %}✓ Oui{% else %}✗ Non{% endif %}
                                            </span>
                                            <br><small class="text-muted">{{ stagiaire.get_statut_taches_display }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Résumé du stage -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Informations du stagiaire</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Nom complet :</strong> {{ stagiaire.nom_complet }}</p>
                                    <p><strong>Date de naissance :</strong> {{ stagiaire.date_naissance|date:"d/m/Y" }}</p>
                                    <p><strong>Email :</strong> {{ stagiaire.email }}</p>
                                    <p><strong>Établissement :</strong> {{ stagiaire.etablissement }}</p>
                                    <p><strong>Niveau d'étude :</strong> {{ stagiaire.niveau_etude }}</p>
                                    <p><strong>Spécialité :</strong> {{ stagiaire.specialite }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-briefcase me-2"></i>Détails du stage</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Département :</strong> {{ stagiaire.get_departement_display }}</p>
                                    <p><strong>Encadrant :</strong> {{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}</p>
                                    <p><strong>Période :</strong> {{ stagiaire.date_debut|date:"d/m/Y" }} - {{ stagiaire.date_fin|date:"d/m/Y" }}</p>
                                    <p><strong>Durée :</strong> {{ stagiaire.duree_stage }} jours</p>
                                    <p><strong>Note finale :</strong> 
                                        {% if stagiaire.note_finale %}
                                            <span class="badge bg-{% if stagiaire.note_finale >= 16 %}success{% elif stagiaire.note_finale >= 12 %}warning{% else %}danger{% endif %}">
                                                {{ stagiaire.note_finale }}/20
                                            </span>
                                        {% else %}
                                            <span class="text-muted">Non notée</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Évaluation -->
                    {% if stagiaire.evaluation_encadrant %}
                    <div class="card border-secondary mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-star me-2"></i>Évaluation de l'encadrant</h6>
                        </div>
                        <div class="card-body">
                            <p>{{ stagiaire.evaluation_encadrant|linebreaks }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Formulaire de génération -->
                    {% if stagiaire.peut_generer_attestation %}
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-cog me-2"></i>Paramètres de génération</h6>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.template_choice.id_for_label }}" class="form-label">
                                            <i class="fas fa-file-alt me-1"></i>Modèle d'attestation
                                        </label>
                                        {{ form.template_choice }}
                                        {% if form.template_choice.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.template_choice.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.commentaires_supplementaires.id_for_label }}" class="form-label">
                                        <i class="fas fa-comment me-1"></i>Commentaires supplémentaires
                                    </label>
                                    {{ form.commentaires_supplementaires }}
                                    {% if form.commentaires_supplementaires.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.commentaires_supplementaires.errors }}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">
                                        Ces commentaires seront inclus dans l'attestation (optionnel).
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'attestations_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>Retour
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-download me-1"></i>Générer et télécharger l'attestation
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Impossible de générer l'attestation</h6>
                        <p>Toutes les conditions ne sont pas remplies pour générer l'attestation. Vérifiez que :</p>
                        <ul>
                            <li>Le stage est terminé (date de fin dépassée)</li>
                            <li>La convention est validée</li>
                            <li>Toutes les tâches sont accomplies</li>
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card-body p {
    margin-bottom: 8px;
}
</style>
{% endblock %}
