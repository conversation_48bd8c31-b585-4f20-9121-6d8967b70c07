{% extends 'stagiaires/base.html' %}
{% load static %}
{% load calendar_extras %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 20px;
    }

    .calendar-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin: 0 auto;
        max-width: 1400px;
        overflow: hidden;
        width: 95%;
    }

    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 30px;
        background: white;
        border-bottom: 1px solid #e0e0e0;
    }

    .calendar-title {
        font-size: 28px;
        font-weight: 400;
        color: #666;
        margin: 0;
    }

    .calendar-nav {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .nav-btn {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 8px 12px;
        color: #6c757d;
        text-decoration: none;
        transition: all 0.2s;
        font-size: 14px;
    }

    .nav-btn:hover {
        background: #e9ecef;
        color: #495057;
        text-decoration: none;
    }

    .today-btn {
        background: #ff6b8a !important;
        color: white !important;
        border-color: #ff6b8a !important;
        font-weight: 500;
        border-radius: 4px;
    }

    .today-btn:hover {
        background: #ff5577 !important;
        color: white !important;
    }

    .calendar-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
    }

    .calendar-table th {
        padding: 15px 10px;
        text-align: center;
        font-weight: 600;
        color: #666;
        font-size: 14px;
        background: #fafafa;
        border-bottom: 1px solid #e0e0e0;
        border-right: 1px solid #e0e0e0;
    }

    .calendar-table th:last-child {
        border-right: none;
    }

    .calendar-table td {
        height: 150px;
        width: 14.28%;
        vertical-align: top;
        padding: 10px;
        border-bottom: 1px solid #e0e0e0;
        border-right: 1px solid #e0e0e0;
        position: relative;
        background: white;
    }

    .calendar-table td:last-child {
        border-right: none;
    }

    .day-number {
        font-size: 18px;
        color: #333;
        margin-bottom: 8px;
        font-weight: 600;
    }

    .day-number.other-month {
        color: #ccc;
    }

    .day-number.today {
        background: #ff6b8a;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 600;
    }

    .event {
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        margin-bottom: 3px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        transition: all 0.2s;
        line-height: 1.3;
        font-weight: 500;
        border: 1px solid rgba(255,255,255,0.3);
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .event:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .more-events {
        color: #007bff;
        font-size: 11px;
        cursor: pointer;
        text-decoration: underline;
        margin-top: 2px;
    }

    .more-events:hover {
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .calendar-container {
            width: 98%;
            margin: 10px auto;
        }

        .calendar-table td {
            height: 100px;
            padding: 6px;
        }

        .day-number {
            font-size: 14px;
        }

        .event {
            font-size: 9px;
            padding: 2px 4px;
            margin-bottom: 1px;
        }

        .calendar-header {
            flex-direction: column;
            gap: 10px;
            padding: 15px;
        }

        .calendar-title {
            font-size: 20px;
        }

        body {
            padding: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="calendar-container">
    <!-- En-tête du calendrier -->
    <div class="calendar-header">
        <h1 class="calendar-title">{{ month_name }} {{ year }}</h1>
        <div class="calendar-nav">
            <a href="?year={{ today.year }}&month={{ today.month }}" class="nav-btn today-btn">
                today
            </a>
            <a href="?year={{ prev_year }}&month={{ prev_month }}" class="nav-btn">
                &#8249;
            </a>
            <a href="?year={{ next_year }}&month={{ next_month }}" class="nav-btn">
                &#8250;
            </a>
        </div>
    </div>

    <!-- Calendrier -->
    <table class="calendar-table">
        <thead>
            <tr>
                <th>Dim</th>
                <th>Lun</th>
                <th>Mar</th>
                <th>Mer</th>
                <th>Jeu</th>
                <th>Ven</th>
                <th>Sam</th>
            </tr>
        </thead>
        <tbody>
            {% for week in calendar %}
            <tr>
                {% for day in week %}
                <td>
                    {% if day == 0 %}
                        <!-- Calculer les jours du mois précédent/suivant -->
                        {% if forloop.parentloop.counter == 1 %}
                            <!-- Première semaine - jours du mois précédent -->
                            {% if month == 1 %}
                                <div class="day-number other-month">{{ 31|add:forloop.counter0|add:-6 }}</div>
                            {% elif month == 3 %}
                                <div class="day-number other-month">{{ 29|add:forloop.counter0|add:-6 }}</div>
                            {% elif month == 5 or month == 7 or month == 10 or month == 12 %}
                                <div class="day-number other-month">{{ 30|add:forloop.counter0|add:-6 }}</div>
                            {% else %}
                                <div class="day-number other-month">{{ 31|add:forloop.counter0|add:-6 }}</div>
                            {% endif %}
                        {% else %}
                            <!-- Dernière semaine - jours du mois suivant -->
                            <div class="day-number other-month">{{ forloop.counter0|add:-6 }}</div>
                        {% endif %}
                    {% else %}
                        <div class="day-number {% if day == today.day and month == today.month and year == today.year %}today{% endif %}">
                            {{ day }}
                        </div>

                        <!-- Afficher les stagiaires pour ce jour -->
                        {% with current_date=year|add:0|stringformat:"s"|add:"-"|add:month|stringformat:"02d"|add:"-"|add:day|stringformat:"02d" %}
                            {% for data in stagiaires_data %}
                                {% with stagiaire=data.stagiaire color=data.color %}
                                    {% if stagiaire.date_debut|date:"Y-m-d" <= current_date and current_date <= stagiaire.date_fin|date:"Y-m-d" %}
                                        {% if year == stagiaire.date_debut.year and month == stagiaire.date_debut.month and day == stagiaire.date_debut.day %}
                                            <!-- Premier jour de stage -->
                                            <div class="event" style="background-color: {{ color }};" title="{{ stagiaire.prenom }} {{ stagiaire.nom }} - Début de stage ({{ stagiaire.date_debut|date:'d/m/Y' }} - {{ stagiaire.date_fin|date:'d/m/Y' }})">
                                                🟢 {{ stagiaire.prenom }} {{ stagiaire.nom }}
                                            </div>
                                        {% elif year == stagiaire.date_fin.year and month == stagiaire.date_fin.month and day == stagiaire.date_fin.day %}
                                            <!-- Dernier jour de stage -->
                                            <div class="event" style="background-color: {{ color }};" title="{{ stagiaire.prenom }} {{ stagiaire.nom }} - Fin de stage ({{ stagiaire.date_debut|date:'d/m/Y' }} - {{ stagiaire.date_fin|date:'d/m/Y' }})">
                                                🔴 {{ stagiaire.prenom }} {{ stagiaire.nom }}
                                            </div>
                                        {% else %}
                                            <!-- Jour normal de stage -->
                                            <div class="event" style="background-color: {{ color }};" title="{{ stagiaire.prenom }} {{ stagiaire.nom }} - En stage ({{ stagiaire.date_debut|date:'d/m/Y' }} - {{ stagiaire.date_fin|date:'d/m/Y' }})">
                                                {{ stagiaire.prenom }} {{ stagiaire.nom }}
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                {% endwith %}
                            {% endfor %}
                        {% endwith %}
                    {% endif %}
                </td>
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Légende des stagiaires -->
    {% if tous_stagiaires %}
    <div style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <div style="font-weight: 600; margin-bottom: 15px; color: #333; font-size: 16px;">
            📋 Tous les Stagiaires ({{ tous_stagiaires|length }})
        </div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
            {% for data in stagiaires_data %}
            <div style="display: flex; align-items: center; gap: 10px; font-size: 13px; color: #666; padding: 8px; background: white; border-radius: 6px; border-left: 4px solid {{ data.color }};">
                <div style="width: 16px; height: 16px; border-radius: 3px; background: {{ data.color }}; flex-shrink: 0;"></div>
                <div style="flex-grow: 1;">
                    <div style="font-weight: 600; color: #333;">{{ data.stagiaire.prenom }} {{ data.stagiaire.nom }}</div>
                    {% if data.stagiaire.service %}
                    <div style="color: #666; font-size: 12px;">Service: {{ data.stagiaire.service.nom }}</div>
                    {% endif %}
                    {% if data.stagiaire.date_debut and data.stagiaire.date_fin %}
                    <div style="color: #999; font-size: 12px;">
                        📅 {{ data.stagiaire.date_debut|date:"d/m/Y" }} → {{ data.stagiaire.date_fin|date:"d/m/Y" }}
                        ({{ data.stagiaire.date_fin|timeuntil:data.stagiaire.date_debut }})
                    </div>
                    {% endif %}
                    {% if data.stagiaire.encadrant %}
                    <div style="color: #666; font-size: 12px;">👨‍💼 Encadrant: {{ data.stagiaire.encadrant.get_full_name }}</div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal pour les détails des tâches -->
<div class="modal fade" id="tacheModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails de la tâche</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="tacheModalBody">
                <!-- Contenu chargé dynamiquement -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Gestion des clics sur les événements
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des clics sur les événements
    document.querySelectorAll('.event').forEach(function(event) {
        event.addEventListener('click', function() {
            // Ici on pourrait ouvrir un modal avec les détails de la tâche
            const title = this.getAttribute('title');
            alert('Détails: ' + title);
        });
    });

    // Gestion des clics sur "plus d'événements"
    document.querySelectorAll('.more-events').forEach(function(moreLink) {
        moreLink.addEventListener('click', function() {
            // Ici on pourrait afficher tous les événements du jour
            alert('Affichage de tous les événements du jour');
        });
    });
});
</script>
{% endblock %}
