{% extends 'stagiaires/base.html' %}
{% load static %}
{% load calendar_extras %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #e8e8e8;
        margin: 0;
        padding: 20px;
    }

    .calendar-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin: 0 auto;
        max-width: 2000px;
        overflow: hidden;
        width: 99%;
        min-height: 98vh;
    }

    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 30px;
        background: white;
        border-bottom: none;
    }

    .calendar-title {
        font-size: 28px;
        font-weight: 400;
        color: #666;
        margin: 0;
    }

    .calendar-nav {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .nav-btn {
        background: #ff6b8a;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        color: white;
        text-decoration: none;
        transition: all 0.2s;
        font-size: 14px;
        font-weight: 500;
    }

    .nav-btn:hover {
        background: #ff5577;
        color: white;
        text-decoration: none;
    }

    .today-btn {
        background: #ff6b8a !important;
        color: white !important;
        font-weight: 500;
    }

    .calendar-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
    }

    .calendar-table th {
        padding: 15px 10px;
        text-align: center;
        font-weight: 600;
        color: #666;
        font-size: 14px;
        background: #fafafa;
        border-bottom: 1px solid #e0e0e0;
        border-right: 1px solid #e0e0e0;
    }

    .calendar-table th:last-child {
        border-right: none;
    }

    .calendar-table td {
        height: 300px;
        width: 14.28%;
        vertical-align: top;
        padding: 15px;
        border-bottom: 1px solid #e0e0e0;
        border-right: 1px solid #e0e0e0;
        position: relative;
        background: white;
    }

    .calendar-table td:last-child {
        border-right: none;
    }

    .day-number {
        font-size: 24px;
        color: #333;
        margin-bottom: 12px;
        font-weight: 700;
    }

    .day-number.other-month {
        color: #ccc;
    }

    .day-number.today {
        background: #ff6b8a;
        color: white;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: 600;
    }

    .event {
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 14px;
        margin-bottom: 4px;
        white-space: normal;
        word-wrap: break-word;
        cursor: pointer;
        transition: all 0.2s;
        line-height: 1.3;
        font-weight: 700;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        border-left: 4px solid rgba(255,255,255,0.3);
        min-height: 20px;
        display: block;
    }

    .event:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .more-events {
        color: #007bff;
        font-size: 11px;
        cursor: pointer;
        text-decoration: underline;
        margin-top: 2px;
    }

    .more-events:hover {
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .calendar-table td {
            height: 100px;
            padding: 4px;
        }

        .day-number {
            font-size: 14px;
        }

        .event {
            font-size: 10px;
            padding: 2px 4px;
        }

        .calendar-header {
            flex-direction: column;
            gap: 10px;
            padding: 15px;
        }

        .calendar-title {
            font-size: 24px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'apparition des événements
    const events = document.querySelectorAll('.event');
    events.forEach((event, index) => {
        event.style.opacity = '0';
        event.style.transform = 'translateY(10px)';
        setTimeout(() => {
            event.style.transition = 'all 0.3s ease';
            event.style.opacity = '1';
            event.style.transform = 'translateY(0)';
        }, index * 50);
    });

    // Effet hover amélioré pour les événements
    events.forEach(event => {
        event.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
            this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.4)';
            this.style.zIndex = '10';
        });

        event.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3)';
            this.style.zIndex = '1';
        });

        // Click pour afficher plus d'infos
        event.addEventListener('click', function() {
            const title = this.getAttribute('title');
            showStagiaireModal(title, this.textContent.trim());
        });
    });

    // Animation pour les cellules du calendrier
    const cells = document.querySelectorAll('.calendar-table td');
    cells.forEach(cell => {
        cell.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
            this.style.transition = 'background-color 0.2s ease';
        });

        cell.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'white';
        });
    });

    // Effet de navigation fluide
    const navButtons = document.querySelectorAll('.nav-btn');
    navButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            // Animation de chargement
            this.style.transform = 'scale(0.95)';
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            setTimeout(() => {
                // Le navigateur suivra le lien normalement
            }, 200);
        });
    });

    // Recherche de stagiaire en temps réel
    createSearchBox();

    // Filtrage par service
    createServiceFilter();

    // Statistiques en temps réel
    updateStatistics();
});

// Fonction pour créer une boîte de recherche
function createSearchBox() {
    const header = document.querySelector('.calendar-header');
    const searchContainer = document.createElement('div');
    searchContainer.style.cssText = `
        position: relative;
        margin-left: 20px;
    `;

    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = '🔍 Rechercher un stagiaire...';
    searchInput.style.cssText = `
        padding: 8px 15px;
        border: 2px solid #e0e0e0;
        border-radius: 25px;
        font-size: 14px;
        width: 250px;
        outline: none;
        transition: all 0.3s ease;
    `;

    searchInput.addEventListener('focus', function() {
        this.style.borderColor = '#ff6b8a';
        this.style.boxShadow = '0 0 10px rgba(255, 107, 138, 0.3)';
    });

    searchInput.addEventListener('blur', function() {
        this.style.borderColor = '#e0e0e0';
        this.style.boxShadow = 'none';
    });

    searchInput.addEventListener('input', function() {
        filterStagiaires(this.value);
    });

    searchContainer.appendChild(searchInput);
    header.appendChild(searchContainer);
}

// Fonction de filtrage des stagiaires
function filterStagiaires(searchTerm) {
    const events = document.querySelectorAll('.event');
    const term = searchTerm.toLowerCase();

    events.forEach(event => {
        const text = event.textContent.toLowerCase();
        const title = event.getAttribute('title').toLowerCase();

        if (text.includes(term) || title.includes(term)) {
            event.style.display = 'block';
            event.style.animation = 'highlight 0.5s ease';
        } else if (searchTerm.length > 0) {
            event.style.display = 'none';
        } else {
            event.style.display = 'block';
        }
    });
}

// Fonction pour créer le filtre par service
function createServiceFilter() {
    const header = document.querySelector('.calendar-header');
    const filterContainer = document.createElement('div');
    filterContainer.style.cssText = `
        margin-left: 20px;
    `;

    const filterSelect = document.createElement('select');
    filterSelect.style.cssText = `
        padding: 8px 15px;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        font-size: 14px;
        background: white;
        cursor: pointer;
        outline: none;
    `;

    // Options du filtre
    const services = ['Tous les services', 'Communication', 'Informatique', 'Marketing', 'Production'];
    services.forEach(service => {
        const option = document.createElement('option');
        option.value = service;
        option.textContent = service;
        filterSelect.appendChild(option);
    });

    filterSelect.addEventListener('change', function() {
        filterByService(this.value);
    });

    filterContainer.appendChild(filterSelect);
    header.appendChild(filterContainer);
}

// Fonction de filtrage par service
function filterByService(service) {
    const events = document.querySelectorAll('.event');

    events.forEach(event => {
        const title = event.getAttribute('title');

        if (service === 'Tous les services' || title.includes(service)) {
            event.style.display = 'block';
            event.style.opacity = '1';
        } else {
            event.style.display = 'none';
        }
    });
}

// Modal pour afficher les détails du stagiaire
function showStagiaireModal(details, name) {
    // Créer le modal s'il n'existe pas
    let modal = document.getElementById('stagiaireModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'stagiaireModal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;

        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        `;

        modalContent.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 style="margin: 0; color: #333;">📋 Détails du Stagiaire</h3>
                <button onclick="closeStagiaireModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">×</button>
            </div>
            <div id="modalBody"></div>
        `;

        modal.appendChild(modalContent);
        document.body.appendChild(modal);

        // Fermer en cliquant à l'extérieur
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeStagiaireModal();
            }
        });
    }

    // Remplir le contenu
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <div style="font-size: 18px; font-weight: bold; color: #ff6b8a; margin-bottom: 15px;">
            👤 ${name}
        </div>
        <div style="line-height: 1.6; color: #666;">
            ${details.replace(/\s-\s/g, '<br>📍 ').replace(/\(/g, '<br>📅 (')}
        </div>
    `;

    // Afficher le modal
    modal.style.display = 'flex';
}

// Fermer le modal
function closeStagiaireModal() {
    const modal = document.getElementById('stagiaireModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Mettre à jour les statistiques
function updateStatistics() {
    const events = document.querySelectorAll('.event');
    const totalStagiaires = new Set();

    events.forEach(event => {
        totalStagiaires.add(event.textContent.trim());
    });

    // Ajouter un compteur en temps réel
    const title = document.querySelector('.calendar-title');
    const counter = document.createElement('span');
    counter.style.cssText = `
        background: #ff6b8a;
        color: white;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 14px;
        margin-left: 15px;
        animation: pulse 2s infinite;
    `;
    counter.textContent = `${totalStagiaires.size} stagiaires actifs`;
    title.appendChild(counter);
}

// Animations CSS supplémentaires
const style = document.createElement('style');
style.textContent = `
    @keyframes modalSlideIn {
        from { transform: translateY(-50px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    @keyframes highlight {
        0% { background-color: rgba(255, 107, 138, 0.3); }
        100% { background-color: transparent; }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .event {
        cursor: pointer !important;
    }

    .event:active {
        transform: scale(0.98) !important;
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}

{% block content %}
<div class="calendar-container">
    <!-- En-tête du calendrier -->
    <div class="calendar-header">
        <h1 class="calendar-title">{{ month_name }} {{ year }}</h1>
        <div class="calendar-nav">
            <a href="?year={{ today.year }}&month={{ today.month }}" class="nav-btn today-btn">
                aujourd'hui
            </a>
            <a href="?year={{ prev_year }}&month={{ prev_month }}" class="nav-btn">
                &#8249;
            </a>
            <a href="?year={{ next_year }}&month={{ next_month }}" class="nav-btn">
                &#8250;
            </a>
        </div>
    </div>

    <!-- Calendrier -->
    <table class="calendar-table">
        <thead>
            <tr>
                <th>Dim</th>
                <th>Lun</th>
                <th>Mar</th>
                <th>Mer</th>
                <th>Jeu</th>
                <th>Ven</th>
                <th>Sam</th>
            </tr>
        </thead>
        <tbody>
            {% for week in calendar %}
            <tr>
                {% for day in week %}
                <td>
                    {% if day == 0 %}
                        <!-- Calculer les jours du mois précédent/suivant -->
                        {% if forloop.parentloop.counter == 1 %}
                            <!-- Première semaine - jours du mois précédent -->
                            {% if month == 1 %}
                                <div class="day-number other-month">{{ 31|add:forloop.counter0|add:-6 }}</div>
                            {% elif month == 3 %}
                                <div class="day-number other-month">{{ 29|add:forloop.counter0|add:-6 }}</div>
                            {% elif month == 5 or month == 7 or month == 10 or month == 12 %}
                                <div class="day-number other-month">{{ 30|add:forloop.counter0|add:-6 }}</div>
                            {% else %}
                                <div class="day-number other-month">{{ 31|add:forloop.counter0|add:-6 }}</div>
                            {% endif %}
                        {% else %}
                            <!-- Dernière semaine - jours du mois suivant -->
                            <div class="day-number other-month">{{ forloop.counter0|add:-6 }}</div>
                        {% endif %}
                    {% else %}
                        <div class="day-number {% if day == today.day and month == today.month and year == today.year %}today{% endif %}">
                            {{ day }}
                        </div>

                        <!-- Afficher les stagiaires pour ce jour -->
                        {% with current_date=year|add:0|stringformat:"s"|add:"-"|add:month|stringformat:"02d"|add:"-"|add:day|stringformat:"02d" %}
                            {% for data in stagiaires_data %}
                                {% with stagiaire=data.stagiaire color=data.color %}
                                    {% if stagiaire.date_debut|date:"Y-m-d" <= current_date and current_date <= stagiaire.date_fin|date:"Y-m-d" %}
                                        <div class="event" style="background-color: {{ color }};" title="{{ stagiaire.prenom }} {{ stagiaire.nom }}{% if stagiaire.service %} - {{ stagiaire.service.nom }}{% endif %} ({{ stagiaire.date_debut|date:'d/m/Y' }} - {{ stagiaire.date_fin|date:'d/m/Y' }})">
                                            {{ stagiaire.prenom }} {{ stagiaire.nom }}
                                        </div>
                                    {% endif %}
                                {% endwith %}
                            {% endfor %}
                        {% endwith %}
                    {% endif %}
                </td>
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Légende des stagiaires -->
    {% if tous_stagiaires %}
    <div style="margin-top: 30px; padding: 30px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
        <div style="font-weight: 700; margin-bottom: 25px; color: #333; font-size: 24px; text-align: center;">
            📋 Tous les Stagiaires ({{ tous_stagiaires|length }})
        </div>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px;">
            {% for data in stagiaires_data %}
            <div class="stagiaire-card" style="display: flex; align-items: center; gap: 15px; font-size: 15px; color: #666; padding: 20px; background: white; border-radius: 12px; border-left: 6px solid {{ data.color }}; box-shadow: 0 3px 10px rgba(0,0,0,0.1); transition: all 0.3s; cursor: pointer;">
                <div style="width: 25px; height: 25px; border-radius: 6px; background: {{ data.color }}; flex-shrink: 0; box-shadow: 0 2px 8px rgba(0,0,0,0.2);"></div>
                <div style="flex-grow: 1;">
                    <div style="font-weight: 700; color: #333; font-size: 18px; margin-bottom: 5px;">{{ data.stagiaire.prenom }} {{ data.stagiaire.nom }}</div>
                    {% if data.stagiaire.service %}
                    <div style="color: #666; font-size: 14px; margin-bottom: 3px;">🏢 Service: {{ data.stagiaire.service.nom }}</div>
                    {% endif %}
                    {% if data.stagiaire.date_debut and data.stagiaire.date_fin %}
                    <div style="color: #999; font-size: 14px; margin-bottom: 3px;">
                        📅 {{ data.stagiaire.date_debut|date:"d/m/Y" }} → {{ data.stagiaire.date_fin|date:"d/m/Y" }}
                    </div>
                    {% endif %}
                    {% if data.stagiaire.encadrant %}
                    <div style="color: #666; font-size: 14px;">👨‍💼 Encadrant: {{ data.stagiaire.encadrant.get_full_name }}</div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal pour les détails des tâches -->
<div class="modal fade" id="tacheModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails de la tâche</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="tacheModalBody">
                <!-- Contenu chargé dynamiquement -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Gestion des clics sur les événements
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des clics sur les événements
    document.querySelectorAll('.event').forEach(function(event) {
        event.addEventListener('click', function() {
            // Ici on pourrait ouvrir un modal avec les détails de la tâche
            const title = this.getAttribute('title');
            alert('Détails: ' + title);
        });
    });

    // Gestion des clics sur "plus d'événements"
    document.querySelectorAll('.more-events').forEach(function(moreLink) {
        moreLink.addEventListener('click', function() {
            // Ici on pourrait afficher tous les événements du jour
            alert('Affichage de tous les événements du jour');
        });
    });
});
</script>
{% endblock %}
