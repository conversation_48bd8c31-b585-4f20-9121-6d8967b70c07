#!/usr/bin/env python
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.db import connection

# Supprimer toutes les migrations problématiques de la base de données
with connection.cursor() as cursor:
    cursor.execute("DELETE FROM django_migrations WHERE app='stagiaires' AND name IN ('0019_merge_migrations', '0020_merge_0019_merge_20250710_1552_0019_merge_migrations', '0021_add_date_modification_to_service', '0022_add_code_service_to_service', '0023_add_code_service_to_service', '0024_alter_tache_options_and_more', 'manual_add_service_column')")
    print("Migrations problématiques supprimées de la base de données.")

# Vérifier les migrations restantes
with connection.cursor() as cursor:
    cursor.execute("SELECT name FROM django_migrations WHERE app='stagiaires' ORDER BY id")
    rows = cursor.fetchall()
    print("\nMigrations restantes dans la base de données:")
    for row in rows:
        print(f"- {row[0]}")

print("\nScript terminé. Vous pouvez maintenant essayer d'appliquer la migration 0018.")
print("Exécutez: python manage.py migrate stagiaires 0018")

