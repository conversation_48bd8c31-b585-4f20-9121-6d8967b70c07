{% extends 'stagiaires/base.html' %}

{% block title %}Missions de {{ stagiaire.nom_complet }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-tasks me-2"></i>
                            Missions de {{ stagiaire.nom_complet }}
                        </h4>
                        <div>
                            {% if user.role == 'ENCADRANT' or user.role == 'RH' or user.role == 'ADMIN' %}
                            <a href="{% url 'planifier_mission' stagiaire.id %}" class="btn btn-light">
                                <i class="fas fa-plus me-1"></i>Planifier une mission
                            </a>
                            {% endif %}
                            <a href="{% url 'stagiaires_list' %}" class="btn btn-outline-light ms-2">
                                <i class="fas fa-arrow-left me-1"></i>Retour
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Informations du stagiaire -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info">Informations du stagiaire</h6>
                                    <p class="mb-1"><strong>Nom :</strong> {{ stagiaire.nom_complet }}</p>
                                    <p class="mb-1"><strong>Email :</strong> {{ stagiaire.email }}</p>
                                    <p class="mb-1"><strong>Période :</strong> {{ stagiaire.date_debut|date:"d/m/Y" }} - {{ stagiaire.date_fin|date:"d/m/Y" }}</p>
                                    <p class="mb-0"><strong>Encadrant :</strong> {{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h6 class="card-title text-success">Statistiques des missions</h6>
                                    <div class="row text-center">
                                        <div class="col-3">
                                            <div class="text-primary">
                                                <h4>{{ total_missions }}</h4>
                                                <small>Total</small>
                                            </div>
                                        </div>
                                        <div class="col-3">
                                            <div class="text-success">
                                                <h4>{{ missions_terminees }}</h4>
                                                <small>Terminées</small>
                                            </div>
                                        </div>
                                        <div class="col-3">
                                            <div class="text-warning">
                                                <h4>{{ missions_en_cours }}</h4>
                                                <small>En cours</small>
                                            </div>
                                        </div>
                                        <div class="col-3">
                                            <div class="text-danger">
                                                <h4>{{ missions_en_retard }}</h4>
                                                <small>En retard</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filtres et recherche -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchMissions" placeholder="Rechercher une mission...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterStatut">
                                <option value="">Tous les statuts</option>
                                <option value="PLANIFIEE">Planifiée</option>
                                <option value="EN_COURS">En cours</option>
                                <option value="TERMINEE">Terminée</option>
                                <option value="VALIDEE">Validée</option>
                                <option value="REJETEE">Rejetée</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterPriorite">
                                <option value="">Toutes les priorités</option>
                                <option value="1">Très haute</option>
                                <option value="2">Haute</option>
                                <option value="3">Moyenne</option>
                                <option value="4">Basse</option>
                                <option value="5">Très basse</option>
                            </select>
                        </div>
                    </div>

                    <!-- Liste des missions -->
                    {% if missions %}
                    <div class="table-responsive">
                        <table class="table table-hover" id="missionsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Titre</th>
                                    <th>Priorité</th>
                                    <th>Statut</th>
                                    <th>Dates prévues</th>
                                    <th>Avancement</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for mission in missions %}
                                <tr data-statut="{{ mission.statut }}" data-priorite="{{ mission.priorite }}">
                                    <td>
                                        <div>
                                            <strong>{{ mission.titre }}</strong>
                                            <br>
                                            <small class="text-muted">{{ mission.description|truncatechars:80 }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        {% if mission.priorite == 1 %}
                                            <span class="badge bg-danger">Très haute</span>
                                        {% elif mission.priorite == 2 %}
                                            <span class="badge bg-warning">Haute</span>
                                        {% elif mission.priorite == 3 %}
                                            <span class="badge bg-info">Moyenne</span>
                                        {% elif mission.priorite == 4 %}
                                            <span class="badge bg-secondary">Basse</span>
                                        {% else %}
                                            <span class="badge bg-light text-dark">Très basse</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if mission.statut == 'PLANIFIEE' %}
                                            <span class="badge bg-secondary">Planifiée</span>
                                        {% elif mission.statut == 'EN_COURS' %}
                                            <span class="badge bg-warning">En cours</span>
                                        {% elif mission.statut == 'TERMINEE' %}
                                            <span class="badge bg-success">Terminée</span>
                                        {% elif mission.statut == 'VALIDEE' %}
                                            <span class="badge bg-primary">Validée</span>
                                        {% elif mission.statut == 'REJETEE' %}
                                            <span class="badge bg-danger">Rejetée</span>
                                        {% endif %}
                                        
                                        {% if mission.en_retard %}
                                            <br><small class="text-danger"><i class="fas fa-exclamation-triangle"></i> En retard</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>
                                            <strong>Début :</strong> {{ mission.date_debut_prevue|date:"d/m/Y" }}<br>
                                            <strong>Fin :</strong> {{ mission.date_fin_prevue|date:"d/m/Y" }}<br>
                                            <span class="text-muted">Durée : {{ mission.duree_prevue }} jours</span>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="progress mb-1" style="height: 20px;">
                                            <div class="progress-bar 
                                                {% if mission.pourcentage_avancement < 30 %}bg-danger
                                                {% elif mission.pourcentage_avancement < 70 %}bg-warning
                                                {% else %}bg-success{% endif %}" 
                                                role="progressbar" 
                                                style="width: {{ mission.pourcentage_avancement }}%">
                                                {{ mission.pourcentage_avancement }}%
                                            </div>
                                        </div>
                                        <small class="text-muted">Mis à jour : {{ mission.derniere_mise_a_jour|date:"d/m/Y H:i" }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            {% if user.role == 'ENCADRANT' or user.role == 'RH' or user.role == 'ADMIN' %}
                                            <a href="{% url 'suivi_mission' mission.id %}" class="btn btn-sm btn-outline-primary" title="Suivi d'avancement">
                                                <i class="fas fa-chart-line"></i>
                                            </a>
                                            {% endif %}
                                            <button class="btn btn-sm btn-outline-info" onclick="showMissionDetails({{ mission.id }})" title="Détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune mission planifiée</h5>
                        <p class="text-muted">Commencez par planifier une mission pour ce stagiaire.</p>
                        {% if user.role == 'ENCADRANT' or user.role == 'RH' or user.role == 'ADMIN' %}
                        <a href="{% url 'planifier_mission' stagiaire.id %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Planifier la première mission
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour les détails de mission -->
<div class="modal fade" id="missionDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails de la mission</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="missionDetailsContent">
                <!-- Contenu chargé dynamiquement -->
            </div>
        </div>
    </div>
</div>

<script>
// Recherche et filtrage
document.getElementById('searchMissions').addEventListener('input', filterMissions);
document.getElementById('filterStatut').addEventListener('change', filterMissions);
document.getElementById('filterPriorite').addEventListener('change', filterMissions);

function filterMissions() {
    const searchTerm = document.getElementById('searchMissions').value.toLowerCase();
    const statutFilter = document.getElementById('filterStatut').value;
    const prioriteFilter = document.getElementById('filterPriorite').value;
    const rows = document.querySelectorAll('#missionsTable tbody tr');

    rows.forEach(row => {
        const titre = row.cells[0].textContent.toLowerCase();
        const statut = row.getAttribute('data-statut');
        const priorite = row.getAttribute('data-priorite');

        const matchesSearch = titre.includes(searchTerm);
        const matchesStatut = !statutFilter || statut === statutFilter;
        const matchesPriorite = !prioriteFilter || priorite === prioriteFilter;

        row.style.display = matchesSearch && matchesStatut && matchesPriorite ? '' : 'none';
    });
}

// Affichage des détails de mission
function showMissionDetails(missionId) {
    // Ici vous pouvez ajouter une requête AJAX pour charger les détails
    // Pour l'instant, on affiche un message simple
    document.getElementById('missionDetailsContent').innerHTML = `
        <p>Chargement des détails de la mission ${missionId}...</p>
        <p>Cette fonctionnalité peut être étendue avec une requête AJAX pour charger les détails complets.</p>
    `;
    new bootstrap.Modal(document.getElementById('missionDetailsModal')).show();
}
</script>
{% endblock %}
