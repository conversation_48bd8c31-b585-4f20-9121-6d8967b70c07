/**
 * Script pour charger dynamiquement les sujets en fonction de la thématique sélectionnée
 */
document.addEventListener('DOMContentLoaded', function() {
    // Identifier les éléments du formulaire
    const thematiqueSelect = document.querySelector('select[name="thematique"]');
    const sujetSelect = document.querySelector('select[name="sujet"]');
    const dureeInput = document.querySelector('input[name="duree_estimee"]');
    
    if (!thematiqueSelect || !sujetSelect) {
        return; // Sortir si les éléments n'existent pas
    }
    
    // Fonction pour charger les sujets
    function loadSujets() {
        const thematiqueId = thematiqueSelect.value;
        if (!thematiqueId) {
            // Vider la liste des sujets si aucune thématique n'est sélectionnée
            sujetSelect.innerHTML = '<option value="">Sélectionner un sujet</option>';
            return;
        }
        
        // Afficher un indicateur de chargement
        sujetSelect.innerHTML = '<option value="">Chargement...</option>';
        sujetSelect.disabled = true;
        
        // Appel AJAX pour récupérer les sujets
        fetch(`/api/sujets-par-thematique/?thematique_id=${thematiqueId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erreur réseau');
                }
                return response.json();
            })
            .then(data => {
                // Vider la liste actuelle
                sujetSelect.innerHTML = '<option value="">Sélectionner un sujet</option>';
                
                // Ajouter les nouveaux sujets
                if (data.length === 0) {
                    const option = document.createElement('option');
                    option.value = "";
                    option.textContent = "Aucun sujet disponible pour cette thématique";
                    sujetSelect.appendChild(option);
                } else {
                    data.forEach(sujet => {
                        const option = document.createElement('option');
                        option.value = sujet.id;
                        option.textContent = sujet.titre;
                        option.dataset.duree = sujet.duree_recommandee;
                        sujetSelect.appendChild(option);
                    });
                }
                
                // Réactiver le select
                sujetSelect.disabled = false;
            })
            .catch(error => {
                console.error('Erreur lors du chargement des sujets:', error);
                sujetSelect.innerHTML = '<option value="">Erreur de chargement</option>';
                sujetSelect.disabled = false;
            });
    }
    
    // Charger les sujets au chargement de la page
    if (thematiqueSelect.value) {
        loadSujets();
    }
    
    // Charger les sujets quand la thématique change
    thematiqueSelect.addEventListener('change', loadSujets);
    
    // Mettre à jour la durée estimée quand le sujet change
    if (dureeInput) {
        sujetSelect.addEventListener('change', function() {
            const selectedOption = sujetSelect.options[sujetSelect.selectedIndex];
            if (selectedOption && selectedOption.dataset.duree) {
                dureeInput.value = selectedOption.dataset.duree;
            }
        });
    }
});