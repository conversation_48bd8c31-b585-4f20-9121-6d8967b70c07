#!/usr/bin/env python
"""
Script de test pour vérifier la fonctionnalité de consultation des CV
"""

import os
import sys
import django
from django.core.files.base import ContentFile

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service
from datetime import date, timedelta

User = get_user_model()

def create_test_cv_file():
    """Créer un fichier PDF de test pour simuler un CV"""
    # Contenu PDF minimal (en-tête PDF basique)
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(CV de Test - Stagiaire) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000369 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
456
%%EOF"""
    
    return ContentFile(pdf_content, name='cv_test.pdf')

def test_cv_functionality():
    """Test de la fonctionnalité de consultation des CV"""
    
    print("🧪 TEST DE LA FONCTIONNALITÉ CONSULTATION CV")
    print("=" * 50)
    
    # 1. Récupérer l'encadrant de test
    try:
        encadrant = User.objects.get(username='encadrant_test')
        print(f"👤 Encadrant connecté : {encadrant.get_full_name()} ({encadrant.username})")
    except User.DoesNotExist:
        print("❌ Encadrant de test non trouvé. Exécutez d'abord create_test_user.py")
        return
    
    # 2. Récupérer ou créer un stagiaire de test
    stagiaires_service = Stagiaire.objects.filter(service=encadrant.service)
    
    if not stagiaires_service.exists():
        print("   Création d'un stagiaire de test...")
        stagiaire = Stagiaire.objects.create(
            nom='Benali',
            prenom='Youssef',
            email='<EMAIL>',
            telephone='0612345679',
            date_naissance=date(2000, 8, 20),
            departement='IT',
            service=encadrant.service,
            encadrant=encadrant,
            date_debut=date.today(),
            date_fin=date.today() + timedelta(days=90),
            etablissement='ENSA Rabat',
            niveau_etude='Ingénieur',
            specialite='Informatique et Réseaux',
            statut='EN_COURS'
        )
        print(f"   ✅ Stagiaire créé : {stagiaire.nom_complet}")
    else:
        stagiaire = stagiaires_service.first()
        print(f"   📝 Stagiaire sélectionné : {stagiaire.nom_complet}")
    
    # 3. Ajouter un CV de test si pas déjà présent
    if not stagiaire.cv:
        print(f"\n📄 Ajout d'un CV de test pour {stagiaire.nom_complet}")
        
        # Créer le fichier CV de test
        cv_file = create_test_cv_file()
        stagiaire.cv.save(f'cv_{stagiaire.nom}_{stagiaire.prenom}.pdf', cv_file)
        stagiaire.save()
        
        print(f"   ✅ CV ajouté : {stagiaire.cv.name}")
        print(f"   📁 Chemin : {stagiaire.cv.url}")
    else:
        print(f"\n📄 CV existant pour {stagiaire.nom_complet}")
        print(f"   📁 Fichier : {stagiaire.cv.name}")
        print(f"   🔗 URL : {stagiaire.cv.url}")
    
    # 4. Afficher les informations du stagiaire avec CV
    print(f"\n👩‍🎓 INFORMATIONS DU STAGIAIRE AVEC CV :")
    print(f"   • Nom complet : {stagiaire.nom_complet}")
    print(f"   • Email : {stagiaire.email}")
    print(f"   • Service : {stagiaire.service.nom}")
    print(f"   • Encadrant : {stagiaire.encadrant.get_full_name()}")
    print(f"   • Établissement : {stagiaire.etablissement}")
    print(f"   • Spécialité : {stagiaire.specialite}")
    print(f"   • CV disponible : {'✅ Oui' if stagiaire.cv else '❌ Non'}")
    
    if stagiaire.cv:
        # Vérifier si le fichier existe physiquement
        cv_path = stagiaire.cv.path
        file_exists = os.path.exists(cv_path)
        file_size = os.path.getsize(cv_path) if file_exists else 0
        
        print(f"   • Nom du fichier : {stagiaire.cv.name}")
        print(f"   • Taille : {file_size} octets")
        print(f"   • Fichier existe : {'✅ Oui' if file_exists else '❌ Non'}")
    
    # 5. URLs importantes pour tester
    print(f"\n🔗 URLS POUR TESTER LA CONSULTATION CV :")
    print(f"   • Connexion : http://127.0.0.1:8000/login/")
    print(f"     Username: {encadrant.username}")
    print(f"     Password: test123")
    print(f"   • Rencontre avec {stagiaire.prenom} : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/rencontre/")
    print(f"   • Consulter CV : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/cv/")
    print(f"   • Détail du stagiaire : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/")
    
    # 6. Test des permissions
    print(f"\n🔐 PERMISSIONS D'ACCÈS AU CV :")
    print(f"   • Encadrant ({encadrant.username}) : ✅ Peut consulter (même service)")
    print(f"   • Admin : ✅ Peut consulter (accès complet)")
    print(f"   • RH : ✅ Peut consulter (accès complet)")
    print(f"   • Autres encadrants : ❌ Ne peut pas consulter (service différent)")
    
    print(f"\n✅ TEST TERMINÉ AVEC SUCCÈS !")
    print(f"🎯 La fonctionnalité de consultation CV est maintenant opérationnelle.")
    
    return {
        'encadrant': encadrant,
        'stagiaire': stagiaire,
        'cv_disponible': bool(stagiaire.cv)
    }

if __name__ == '__main__':
    try:
        result = test_cv_functionality()
        print(f"\n🎯 RÉSUMÉ :")
        print(f"  • Encadrant : {result['encadrant'].username}")
        print(f"  • Stagiaire : {result['stagiaire'].nom_complet}")
        print(f"  • CV disponible : {'✅ Oui' if result['cv_disponible'] else '❌ Non'}")
        
    except Exception as e:
        print(f"❌ Erreur lors du test : {e}")
        import traceback
        traceback.print_exc()
