#!/usr/bin/env python
"""
Test spécifique pour le champ niveau_etude
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire
from stagiaires.admin import StagiaireAdmin
from django.contrib import admin
from django.test import RequestFactory
from datetime import date, timedelta

User = get_user_model()

def test_niveau_etude():
    """Test spécifique du champ niveau_etude"""
    
    print("=== Test du champ niveau_etude ===")
    
    # 1. Vérifier la définition du champ dans le modèle
    print("\n1️⃣ Définition du champ dans le modèle:")
    niveau_etude_field = Stagiaire._meta.get_field('niveau_etude')
    print(f"   Type: {type(niveau_etude_field).__name__}")
    print(f"   Max length: {niveau_etude_field.max_length}")
    print(f"   Blank: {niveau_etude_field.blank}")
    print(f"   Null: {niveau_etude_field.null}")
    print(f"   Choices: {niveau_etude_field.choices}")
    print(f"   Verbose name: {niveau_etude_field.verbose_name}")
    
    # 2. Test de création avec différentes valeurs
    print("\n2️⃣ Test de création avec différentes valeurs:")
    
    admin_user = User.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ Aucun admin trouvé")
        return
    
    test_values = [
        "Master",
        "Master 2",
        "Licence",
        "Doctorat",
        "BTS",
        "DUT",
        "Ingénieur"
    ]
    
    for i, niveau in enumerate(test_values):
        try:
            # Supprimer le stagiaire de test s'il existe
            test_email = f"test.niveau.{i}@example.com"
            Stagiaire.objects.filter(email=test_email).delete()
            
            # Créer le stagiaire
            stagiaire = Stagiaire.objects.create(
                nom=f'TestNiveau{i}',
                prenom='Stagiaire',
                email=test_email,
                date_naissance=date(2000, 1, 1),
                departement='IT',
                date_debut=date.today(),
                date_fin=date.today() + timedelta(days=90),
                etablissement='Université Test',
                niveau_etude=niveau,  # Test de ce champ
                specialite='Informatique',
                cree_par=admin_user
            )
            
            print(f"   ✅ '{niveau}' - Créé avec succès (ID: {stagiaire.id})")
            
            # Nettoyer
            stagiaire.delete()
            
        except Exception as e:
            print(f"   ❌ '{niveau}' - Erreur: {e}")
    
    # 3. Test du formulaire d'administration
    print("\n3️⃣ Test du formulaire d'administration:")
    
    try:
        stagiaire_admin = StagiaireAdmin(Stagiaire, admin.site)
        factory = RequestFactory()
        request = factory.get('/admin/stagiaires/stagiaire/add/')
        request.user = admin_user
        
        # Récupérer le formulaire
        form_class = stagiaire_admin.get_form(request)
        
        # Vérifier le champ niveau_etude dans le formulaire
        if 'niveau_etude' in form_class.base_fields:
            niveau_field = form_class.base_fields['niveau_etude']
            print(f"   Type de champ: {type(niveau_field).__name__}")
            print(f"   Widget: {type(niveau_field.widget).__name__}")
            print(f"   Required: {niveau_field.required}")
            
            # Vérifier s'il y a des choix
            if hasattr(niveau_field, 'choices'):
                print(f"   Choices: {niveau_field.choices}")
            else:
                print("   Pas de choices - champ libre")
                
            # Vérifier les attributs du widget
            if hasattr(niveau_field.widget, 'attrs'):
                print(f"   Attributs du widget: {niveau_field.widget.attrs}")
        else:
            print("   ❌ Champ niveau_etude non trouvé dans le formulaire")
    
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. Suggestions de correction
    print("\n4️⃣ Suggestions de correction:")
    print("   Si le champ n'est pas éditable dans l'admin:")
    print("   • Vérifiez s'il y a du CSS qui bloque le champ")
    print("   • Vérifiez s'il y a du JavaScript qui interfère")
    print("   • Essayez de rafraîchir la page (Ctrl+F5)")
    print("   • Essayez avec un autre navigateur")
    print("   • Vérifiez s'il y a des extensions de navigateur actives")
    
    print("\n5️⃣ Valeurs recommandées pour le test:")
    print("   Utilisez une de ces valeurs dans l'interface:")
    for niveau in test_values:
        print(f"   • {niveau}")

if __name__ == '__main__':
    test_niveau_etude()
