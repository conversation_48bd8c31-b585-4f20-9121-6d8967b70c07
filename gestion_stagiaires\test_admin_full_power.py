#!/usr/bin/env python
"""
Script de test pour vérifier que l'admin peut supprimer n'importe quel utilisateur
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser

def test_admin_full_access():
    """Tester que l'admin peut voir et gérer tous les utilisateurs"""
    print("🔑 TEST DES POUVOIRS COMPLETS DE L'ADMIN")
    print("=" * 60)
    
    # Récupérer l'admin principal
    admin_user = CustomUser.objects.filter(role='ADMIN').first()
    if not admin_user:
        print("❌ Aucun administrateur trouvé")
        return False
    
    print(f"👑 Admin connecté : {admin_user.get_full_name()} ({admin_user.username})")
    print()
    
    # Créer des utilisateurs de test avec différents créateurs
    print("🧪 Création d'utilisateurs de test...")
    
    # Utilisateur créé par l'admin
    user_by_admin = CustomUser.objects.create(
        username='created_by_admin',
        email='<EMAIL>',
        first_name='Créé',
        last_name='ParAdmin',
        role='ENCADRANT',
        cree_par=admin_user
    )
    user_by_admin.set_password('test123')
    user_by_admin.save()
    print(f"   ✅ Utilisateur créé par admin : {user_by_admin.get_full_name()}")
    
    # Utilisateur créé par quelqu'un d'autre (simulé)
    other_admin = CustomUser.objects.create(
        username='other_admin',
        email='<EMAIL>',
        first_name='Autre',
        last_name='Admin',
        role='ADMIN'
    )
    other_admin.set_password('test123')
    other_admin.save()
    
    user_by_other = CustomUser.objects.create(
        username='created_by_other',
        email='<EMAIL>',
        first_name='Créé',
        last_name='ParAutre',
        role='RH',
        cree_par=other_admin
    )
    user_by_other.set_password('test123')
    user_by_other.save()
    print(f"   ✅ Utilisateur créé par autre admin : {user_by_other.get_full_name()}")
    
    # Utilisateur sans créateur (ancien)
    user_legacy = CustomUser.objects.create(
        username='legacy_user',
        email='<EMAIL>',
        first_name='Utilisateur',
        last_name='Ancien',
        role='ENCADRANT',
        cree_par=None
    )
    user_legacy.set_password('test123')
    user_legacy.save()
    print(f"   ✅ Utilisateur ancien (sans créateur) : {user_legacy.get_full_name()}")
    
    print()
    
    # Tester la vue user_management_view
    print("🔍 Test de la vue de gestion des utilisateurs...")
    from stagiaires.views import user_management_view
    from django.test import RequestFactory
    from django.contrib.auth.models import AnonymousUser
    
    factory = RequestFactory()
    request = factory.get('/users/')
    request.user = admin_user
    
    # Simuler la logique de la vue
    users_list = list(CustomUser.objects.all().order_by('date_creation', 'id'))
    
    print(f"   📋 Utilisateurs visibles par l'admin : {len(users_list)}")
    
    # Vérifier que tous les types d'utilisateurs sont visibles
    test_users = [user_by_admin, user_by_other, user_legacy]
    all_visible = True
    
    for test_user in test_users:
        if test_user in users_list:
            creator = test_user.cree_par.get_full_name() if test_user.cree_par else "Aucun"
            print(f"   ✅ {test_user.get_full_name()} (créé par: {creator}) - VISIBLE")
        else:
            print(f"   ❌ {test_user.get_full_name()} - NON VISIBLE")
            all_visible = False
    
    print()
    
    # Test des permissions de suppression
    print("🗑️ Test des permissions de suppression...")
    
    # Simuler les vérifications de la vue delete_user_view
    def can_delete_user(admin, target_user):
        # Vérifier le rôle admin
        if not hasattr(admin, 'role') or admin.role != 'ADMIN':
            return False, "Pas admin"
        
        # Empêcher l'auto-suppression
        if target_user == admin:
            return False, "Auto-suppression"
        
        # Avec les nouvelles règles : admin peut supprimer n'importe qui
        return True, "Autorisé"
    
    for test_user in test_users:
        can_delete, reason = can_delete_user(admin_user, test_user)
        status = "✅ PEUT SUPPRIMER" if can_delete else f"❌ NE PEUT PAS SUPPRIMER ({reason})"
        creator = test_user.cree_par.get_full_name() if test_user.cree_par else "Aucun"
        print(f"   {status} : {test_user.get_full_name()} (créé par: {creator})")
    
    # Test de l'auto-suppression (doit être bloquée)
    can_delete_self, reason = can_delete_user(admin_user, admin_user)
    status = "❌ BLOQUÉ (correct)" if not can_delete_self else "⚠️ AUTORISÉ (problème)"
    print(f"   {status} : Auto-suppression de {admin_user.get_full_name()}")
    
    print()
    
    # Nettoyer les données de test
    print("🧹 Nettoyage des données de test...")
    user_by_admin.delete()
    user_by_other.delete()
    user_legacy.delete()
    other_admin.delete()
    print("   ✅ Données de test supprimées")
    
    return all_visible

def test_statistics():
    """Tester les statistiques pour l'admin"""
    print("\n" + "=" * 60)
    print("📊 TEST DES STATISTIQUES ADMIN")
    print("=" * 60)
    
    all_users = list(CustomUser.objects.all())
    
    users_by_role = {
        'ADMIN': len([u for u in all_users if u.role == 'ADMIN']),
        'RH': len([u for u in all_users if u.role == 'RH']),
        'ENCADRANT': len([u for u in all_users if u.role == 'ENCADRANT'])
    }
    
    print(f"📋 Total des utilisateurs : {len(all_users)}")
    print(f"   👑 Administrateurs : {users_by_role['ADMIN']}")
    print(f"   📋 Gestionnaires RH : {users_by_role['RH']}")
    print(f"   👨‍🏫 Encadrants : {users_by_role['ENCADRANT']}")
    
    print("\n📝 Liste détaillée :")
    for user in all_users:
        creator = user.cree_par.get_full_name() if user.cree_par else "Aucun (ancien)"
        status = "🟢 Actif" if user.is_active else "🔴 Inactif"
        role_icon = {"ADMIN": "👑", "RH": "📋", "ENCADRANT": "👨‍🏫"}.get(user.role, "👤")
        print(f"   {role_icon} {user.get_full_name():20} ({user.username:15}) - {status} - Créé par: {creator}")
    
    return True

def main():
    """Fonction principale"""
    print("🎯 VÉRIFICATION DES POUVOIRS COMPLETS DE L'ADMIN")
    print("🔓 L'admin doit pouvoir supprimer N'IMPORTE QUEL utilisateur")
    print("=" * 70)
    
    try:
        success1 = test_admin_full_access()
        success2 = test_statistics()
        
        print("\n" + "=" * 60)
        if success1 and success2:
            print("🎉 TOUS LES TESTS RÉUSSIS !")
            print("✅ L'admin peut voir TOUS les utilisateurs")
            print("✅ L'admin peut supprimer N'IMPORTE QUEL utilisateur")
            print("✅ Seule l'auto-suppression est bloquée (sécurité)")
        else:
            print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
            print("⚠️ Vérifiez la configuration")
        
        print("\n📋 INSTRUCTIONS :")
        print("1. Connectez-vous à l'interface : http://127.0.0.1:8000/users/")
        print("2. Utilisez votre compte admin")
        print("3. Vous pouvez maintenant supprimer N'IMPORTE QUEL utilisateur")
        print("4. Seule exception : vous ne pouvez pas supprimer votre propre compte")
        
        return success1 and success2
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests : {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
