# Generated by Django 5.1.6 on 2025-07-10 16:25

import django.contrib.auth.models
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
import stagiaires.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.Char<PERSON>ield(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('role', models.CharField(choices=[('ADMIN', 'Administrateur'), ('RH', 'Ressources Humaines'), ('ENCADRANT', 'Encadrant'), ('STAGIAIRE', 'Stagiaire')], default='STAGIAIRE', max_length=10, verbose_name='Rôle')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'Utilisateur personnalisé',
                'verbose_name_plural': 'Utilisateurs personnalisés',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='DureeEstimee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('duree', models.IntegerField(verbose_name='Durée en jours')),
                ('commentaire', models.TextField(blank=True, null=True)),
                ('date_creation', models.DateTimeField(auto_now_add=True)),
                ('cree_par', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='durees_estimees_creees', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Durée estimée',
                'verbose_name_plural': 'Durées estimées',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.CreateModel(
            name='Mission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(max_length=200, verbose_name='Titre de la mission')),
                ('description', models.TextField(verbose_name='Description détaillée')),
                ('objectifs', models.TextField(verbose_name='Objectifs à atteindre')),
                ('livrables_attendus', models.TextField(verbose_name='Livrables attendus')),
                ('date_debut_prevue', models.DateField(verbose_name='Date de début prévue')),
                ('date_fin_prevue', models.DateField(verbose_name='Date de fin prévue')),
                ('date_debut_reelle', models.DateField(blank=True, null=True, verbose_name='Date de début réelle')),
                ('date_fin_reelle', models.DateField(blank=True, null=True, verbose_name='Date de fin réelle')),
                ('priorite', models.IntegerField(choices=[(1, 'Très haute'), (2, 'Haute'), (3, 'Moyenne'), (4, 'Basse'), (5, 'Très basse')], default=3, verbose_name='Priorité')),
                ('statut', models.CharField(choices=[('PLANIFIEE', 'Planifiée'), ('EN_COURS', 'En cours'), ('TERMINEE', 'Terminée'), ('VALIDEE', 'Validée'), ('REJETEE', 'Rejetée')], default='PLANIFIEE', max_length=20, verbose_name='Statut')),
                ('pourcentage_avancement', models.IntegerField(default=0, verbose_name="Pourcentage d'avancement")),
                ('commentaire_avancement', models.TextField(blank=True, verbose_name="Commentaire sur l'avancement")),
                ('derniere_mise_a_jour', models.DateTimeField(auto_now=True, verbose_name='Dernière mise à jour')),
                ('date_creation', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('creee_par', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='missions_creees', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Mission',
                'verbose_name_plural': 'Missions',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100, verbose_name='Nom du service')),
                ('code_service', models.CharField(max_length=10, unique=True, verbose_name='Code du service')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('actif', models.BooleanField(default=True, verbose_name='Service actif')),
                ('date_creation', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('date_modification', models.DateTimeField(auto_now=True, null=True, verbose_name='Date de modification')),
                ('cree_par', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='services_crees', to=settings.AUTH_USER_MODEL, verbose_name='Créé par')),
                ('modifie_par', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='services_modifies', to=settings.AUTH_USER_MODEL, verbose_name='Modifié par')),
                ('responsable', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='services_responsable', to=settings.AUTH_USER_MODEL, verbose_name='Responsable')),
            ],
            options={
                'verbose_name': 'Service',
                'verbose_name_plural': 'Services',
                'ordering': ['nom'],
            },
        ),
        migrations.AddField(
            model_name='customuser',
            name='service',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='utilisateurs', to='stagiaires.service', verbose_name='Service'),
        ),
        migrations.CreateModel(
            name='Stagiaire',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nom', models.CharField(max_length=100, verbose_name='Nom')),
                ('prenom', models.CharField(max_length=100, verbose_name='Prénom')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='Email')),
                ('telephone', models.CharField(blank=True, max_length=20, verbose_name='Téléphone')),
                ('date_naissance', models.DateField(verbose_name='Date de naissance')),
                ('departement', models.CharField(choices=[('IT', 'Informatique'), ('MARKETING', 'Marketing'), ('RH', 'Ressources Humaines'), ('FINANCE', 'Finance'), ('COMMERCIAL', 'Commercial'), ('PRODUCTION', 'Production')], max_length=20, verbose_name='Département')),
                ('date_debut', models.DateField(verbose_name='Date de début')),
                ('date_fin', models.DateField(verbose_name='Date de fin')),
                ('statut', models.CharField(choices=[('EN_COURS', 'En cours'), ('TERMINE', 'Terminé'), ('SUSPENDU', 'Suspendu'), ('ANNULE', 'Annulé')], default='EN_COURS', max_length=20, verbose_name='Statut')),
                ('etablissement', models.CharField(max_length=200, verbose_name='Établissement')),
                ('niveau_etude', models.CharField(max_length=100, verbose_name="Niveau d'étude")),
                ('specialite', models.CharField(max_length=100, verbose_name='Spécialité')),
                ('cv', models.FileField(blank=True, null=True, upload_to=stagiaires.models.cv_upload_path, verbose_name='CV du stagiaire')),
                ('assurance', models.FileField(blank=True, null=True, upload_to=stagiaires.models.assurance_upload_path, verbose_name='Assurance responsabilité civile')),
                ('convention_stage', models.FileField(blank=True, null=True, upload_to=stagiaires.models.convention_upload_path, verbose_name='Convention de stage')),
                ('statut_convention', models.CharField(choices=[('EN_ATTENTE', 'En attente'), ('VALIDEE', 'Validée'), ('REJETEE', 'Rejetée'), ('MODIFIEE', 'À modifier')], default='EN_ATTENTE', max_length=20, verbose_name='Statut de la convention')),
                ('date_validation_convention', models.DateTimeField(blank=True, null=True, verbose_name='Date de validation de la convention')),
                ('commentaire_convention', models.TextField(blank=True, verbose_name='Commentaire sur la convention')),
                ('description_taches', models.TextField(blank=True, verbose_name='Description des tâches à accomplir')),
                ('statut_taches', models.CharField(choices=[('NON_COMMENCEES', 'Non commencées'), ('EN_COURS', 'En cours'), ('PARTIELLEMENT_ACCOMPLIES', 'Partiellement accomplies'), ('ACCOMPLIES', 'Accomplies')], default='NON_COMMENCEES', max_length=30, verbose_name='Statut des tâches')),
                ('evaluation_encadrant', models.TextField(blank=True, verbose_name="Évaluation de l'encadrant")),
                ('note_finale', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True, verbose_name='Note finale (/20)')),
                ('attestation_fin_stage', models.FileField(blank=True, null=True, upload_to=stagiaires.models.attestation_upload_path, verbose_name='Attestation de fin de stage')),
                ('date_generation_attestation', models.DateTimeField(blank=True, null=True, verbose_name="Date de génération de l'attestation")),
                ('date_creation', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Date de création')),
                ('duree_estimee', models.PositiveIntegerField(default=0, help_text='Durée estimée du stage en jours', verbose_name='Durée estimée (jours)')),
                ('technologies', models.TextField(blank=True, help_text='Technologies, langages ou outils que le stagiaire utilisera', null=True, verbose_name='Technologies utilisées')),
                ('attestation_generee_par', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='attestations_generees', to=settings.AUTH_USER_MODEL, verbose_name='Attestation générée par')),
                ('cree_par', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stagiaires_crees', to=settings.AUTH_USER_MODEL, verbose_name='Créé par')),
                ('encadrant', models.ForeignKey(blank=True, limit_choices_to={'role': 'ENCADRANT'}, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='Encadrant')),
                ('service', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stagiaires_service', to='stagiaires.service', verbose_name='Service')),
                ('validee_par', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='conventions_validees', to=settings.AUTH_USER_MODEL, verbose_name='Validée par')),
            ],
            options={
                'verbose_name': 'Stagiaire',
                'verbose_name_plural': 'Stagiaires',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.CreateModel(
            name='RapportStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(max_length=200, verbose_name='Titre du rapport')),
                ('fichier_rapport', models.FileField(upload_to=stagiaires.models.rapport_upload_path, verbose_name='Fichier du rapport')),
                ('description', models.TextField(verbose_name='Description du contenu')),
                ('statut', models.CharField(choices=[('BROUILLON', 'Brouillon'), ('SOUMIS', 'Soumis'), ('EN_REVISION', 'En révision'), ('VALIDE', 'Validé'), ('REJETE', 'Rejeté')], default='BROUILLON', max_length=20, verbose_name='Statut')),
                ('date_soumission', models.DateTimeField(blank=True, null=True, verbose_name='Date de soumission')),
                ('date_validation', models.DateTimeField(blank=True, null=True, verbose_name='Date de validation')),
                ('commentaires_encadrant', models.TextField(blank=True, verbose_name="Commentaires de l'encadrant")),
                ('note_rapport', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True, verbose_name='Note du rapport')),
                ('date_creation', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('date_modification', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('mission', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='rapports', to='stagiaires.mission')),
                ('valide_par', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rapports_valides', to=settings.AUTH_USER_MODEL)),
                ('stagiaire', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rapports', to='stagiaires.stagiaire')),
            ],
            options={
                'verbose_name': 'Rapport de stage',
                'verbose_name_plural': 'Rapports de stage',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.AddField(
            model_name='mission',
            name='stagiaire',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='missions', to='stagiaires.stagiaire'),
        ),
        migrations.CreateModel(
            name='ContratStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference', models.CharField(max_length=50, unique=True, verbose_name='Référence du contrat')),
                ('type_contrat', models.CharField(choices=[('STAGE_OBLIGATOIRE', 'Stage obligatoire'), ('STAGE_VOLONTAIRE', 'Stage volontaire'), ('STAGE_DECOUVERTE', 'Stage de découverte'), ('STAGE_PERFECTIONNEMENT', 'Stage de perfectionnement')], default='STAGE_OBLIGATOIRE', max_length=30, verbose_name='Type de contrat')),
                ('titre_stage', models.CharField(max_length=200, verbose_name='Titre du stage')),
                ('description_missions', models.TextField(verbose_name='Description des missions')),
                ('objectifs_pedagogiques', models.TextField(verbose_name='Objectifs pédagogiques')),
                ('competences_acquises', models.TextField(blank=True, verbose_name='Compétences à acquérir')),
                ('duree_hebdomadaire', models.IntegerField(default=35, verbose_name='Durée hebdomadaire (heures)')),
                ('gratification_mensuelle', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='Gratification mensuelle (€)')),
                ('avantages', models.TextField(blank=True, verbose_name='Avantages (tickets restaurant, etc.)')),
                ('tuteur_pedagogique', models.CharField(blank=True, max_length=200, verbose_name='Tuteur pédagogique (établissement)')),
                ('statut', models.CharField(choices=[('BROUILLON', 'Brouillon'), ('EN_ATTENTE_SIGNATURE', 'En attente de signature'), ('PARTIELLEMENT_SIGNE', 'Partiellement signé'), ('ENTIEREMENT_SIGNE', 'Entièrement signé'), ('EXPIRE', 'Expiré'), ('ANNULE', 'Annulé')], default='BROUILLON', max_length=30, verbose_name='Statut')),
                ('signature_rh', models.BooleanField(default=False, verbose_name='Signé par RH')),
                ('date_signature_rh', models.DateTimeField(blank=True, null=True, verbose_name='Date signature RH')),
                ('document_contrat', models.FileField(blank=True, null=True, upload_to=stagiaires.models.contrat_upload_path, verbose_name='Document du contrat')),
                ('document_signe', models.FileField(blank=True, null=True, upload_to=stagiaires.models.contrat_upload_path, verbose_name='Document signé')),
                ('date_creation', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Date de création')),
                ('date_modification', models.DateTimeField(auto_now=True, verbose_name='Dernière modification')),
                ('date_expiration', models.DateField(blank=True, null=True, verbose_name="Date d'expiration")),
                ('commentaires_admin', models.TextField(blank=True, verbose_name='Commentaires administrateur')),
                ('notes_internes', models.TextField(blank=True, verbose_name='Notes internes')),
                ('cree_par', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='contrats_crees', to=settings.AUTH_USER_MODEL, verbose_name='Créé par')),
                ('encadrant_entreprise', models.ForeignKey(limit_choices_to={'role': 'ENCADRANT'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='contrats_encadres', to=settings.AUTH_USER_MODEL, verbose_name='Encadrant entreprise')),
                ('signature_rh_par', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='contrats_signes_rh', to=settings.AUTH_USER_MODEL, verbose_name='Signé RH par')),
                ('stagiaire', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contrats', to='stagiaires.stagiaire', verbose_name='Stagiaire')),
            ],
            options={
                'verbose_name': 'Contrat de stage',
                'verbose_name_plural': 'Contrats de stage',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.CreateModel(
            name='Sujet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(max_length=200, verbose_name='Titre')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('niveau_difficulte', models.CharField(choices=[('FACILE', 'Facile'), ('MOYEN', 'Moyen'), ('DIFFICILE', 'Difficile'), ('EXPERT', 'Expert')], default='MOYEN', max_length=20, verbose_name='Niveau de difficulté')),
                ('actif', models.BooleanField(default=True, verbose_name='Actif')),
                ('date_creation', models.DateTimeField(auto_now_add=True, verbose_name='Créé le')),
                ('date_modification', models.DateTimeField(auto_now=True, verbose_name='Modifié le')),
                ('cree_par', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sujets_crees', to=settings.AUTH_USER_MODEL, verbose_name='Créé par')),
                ('encadrant', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sujets_encadres', to=settings.AUTH_USER_MODEL, verbose_name='Encadrant')),
                ('service', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sujets', to='stagiaires.service', verbose_name='Service associé')),
            ],
            options={
                'verbose_name': 'Sujet',
                'verbose_name_plural': 'Sujets',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='sujet',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stagiaires', to='stagiaires.sujet', verbose_name='Sujet'),
        ),
        migrations.CreateModel(
            name='Tache',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(max_length=200, verbose_name='Titre')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('statut', models.CharField(choices=[('A_FAIRE', 'À faire'), ('EN_COURS', 'En cours'), ('TERMINEE', 'Terminée'), ('ANNULEE', 'Annulée')], default='A_FAIRE', max_length=20, verbose_name='Statut')),
                ('priorite', models.CharField(choices=[('BASSE', 'Basse'), ('NORMALE', 'Normale'), ('HAUTE', 'Haute'), ('URGENTE', 'Urgente')], default='NORMALE', max_length=20, verbose_name='Priorité')),
                ('date_debut', models.DateField(blank=True, null=True, verbose_name='Date de début')),
                ('date_fin_prevue', models.DateField(blank=True, null=True, verbose_name='Date de fin prévue')),
                ('date_debut_reelle', models.DateField(blank=True, null=True, verbose_name='Date de début réelle')),
                ('date_fin_reelle', models.DateField(blank=True, null=True, verbose_name='Date de fin réelle')),
                ('date_creation', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('date_modification', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('creee_par', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='taches_creees', to=settings.AUTH_USER_MODEL, verbose_name='Créée par')),
                ('stagiaire', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='taches', to='stagiaires.stagiaire', verbose_name='Stagiaire')),
            ],
            options={
                'verbose_name': 'Tâche',
                'verbose_name_plural': 'Tâches',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.CreateModel(
            name='Thematique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(max_length=200, verbose_name='Titre')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('active', models.BooleanField(default=True, verbose_name='Active')),
                ('date_creation', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('date_modification', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('cree_par', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='thematiques_creees', to=settings.AUTH_USER_MODEL, verbose_name='Créée par')),
                ('service', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='thematiques', to='stagiaires.service', verbose_name='Service')),
            ],
            options={
                'verbose_name': 'Thématique',
                'verbose_name_plural': 'Thématiques',
                'ordering': ['-date_creation'],
            },
        ),
        migrations.AddField(
            model_name='sujet',
            name='thematique',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sujets', to='stagiaires.thematique', verbose_name='Thématique'),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='thematique',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stagiaires', to='stagiaires.thematique', verbose_name='Thématique'),
        ),
    ]
