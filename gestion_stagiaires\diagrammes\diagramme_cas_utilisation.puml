@startuml Diagramme_Cas_Utilisation_Gestion_Stagiaires

!theme plain
skinparam actorStyle awesome
skinparam packageStyle rectangle
skinparam usecase {
    BackgroundColor LightBlue
    BorderColor DarkBlue
    ArrowColor Black
}

title Diagramme de Cas d'Utilisation - Système de Gestion des Stagiaires MEF

' Acteurs du système
actor "Administrateur" as Admin
actor "Gestionnaire RH" as RH  
actor "<PERSON><PERSON><PERSON><PERSON>" as Encadrant

' Système
rectangle "Système de Gestion des Stagiaires" {

    ' Package Authentification et Sécurité
    package "Authentification et Sécurité" {
        usecase "Se connecter" as UC01
        usecase "Se déconnecter" as UC02
        usecase "Gérer son profil" as UC03
        usecase "Changer mot de passe" as UC04
    }

    ' Package Gestion des Utilisateurs (Admin uniquement)
    package "Gestion des Utilisateurs" {
        usecase "Créer utilisateur" as UC05
        usecase "Modifier utilisateur" as UC06
        usecase "Supprimer utilisateur" as UC07
        usecase "Gérer les rôles" as UC08
        usecase "Gérer les permissions" as UC09
    }

    ' Package Gestion des Services
    package "Gestion des Services" {
        usecase "Créer service" as UC10
        usecase "Modifier service" as UC11
        usecase "Consulter services" as UC12
        usecase "Gérer thématiques" as UC13
        usecase "Gérer sujets de stage" as UC14
    }

    ' Package Gestion des Stagiaires
    package "Gestion des Stagiaires" {
        usecase "Ajouter stagiaire" as UC15
        usecase "Modifier stagiaire" as UC16
        usecase "Consulter stagiaire" as UC17
        usecase "Supprimer stagiaire" as UC18
        usecase "Filtrer stagiaires" as UC19
        usecase "Rechercher stagiaire" as UC20
        usecase "Exporter liste stagiaires" as UC21
    }

    ' Package Gestion Documentaire
    package "Gestion Documentaire" {
        usecase "Uploader CV" as UC22
        usecase "Consulter CV" as UC23
        usecase "Télécharger CV" as UC24
        usecase "Gérer convention stage" as UC25
        usecase "Gérer assurance" as UC26
        usecase "Valider documents" as UC27
    }

    ' Package Rencontres et Suivi
    package "Rencontres et Suivi" {
        usecase "Organiser rencontre" as UC28
        usecase "Consulter CV en rencontre" as UC29
        usecase "Ajouter tâches" as UC30
        usecase "Modifier tâches" as UC31
        usecase "Suivre progression" as UC32
        usecase "Envoyer récapitulatif email" as UC33
    }

    ' Package Gestion des Tâches
    package "Gestion des Tâches" {
        usecase "Créer tâche" as UC34
        usecase "Assigner tâche" as UC35
        usecase "Modifier statut tâche" as UC36
        usecase "Définir priorité" as UC37
        usecase "Fixer échéance" as UC38
        usecase "Commenter tâche" as UC39
        usecase "Valider tâche" as UC40
    }

    ' Package Gestion des Missions
    package "Gestion des Missions" {
        usecase "Planifier mission" as UC41
        usecase "Assigner mission" as UC42
        usecase "Suivre mission" as UC43
        usecase "Valider mission" as UC44
        usecase "Archiver mission" as UC45
    }

    ' Package Contrats et Documents Officiels
    package "Contrats et Documents Officiels" {
        usecase "Créer contrat stage" as UC46
        usecase "Signer contrat" as UC47
        usecase "Valider contrat" as UC48
        usecase "Générer attestation" as UC49
        usecase "Archiver contrat" as UC50
    }

    ' Package Rapports et Évaluations
    package "Rapports et Évaluations" {
        usecase "Soumettre rapport" as UC51
        usecase "Évaluer rapport" as UC52
        usecase "Valider rapport" as UC53
        usecase "Noter stagiaire" as UC54
        usecase "Générer bilan" as UC55
    }

    ' Package Tableaux de Bord et Statistiques
    package "Tableaux de Bord et Statistiques" {
        usecase "Consulter tableau de bord" as UC56
        usecase "Voir statistiques" as UC57
        usecase "Générer rapports" as UC58
        usecase "Exporter données" as UC59
        usecase "Analyser tendances" as UC60
    }

    ' Package Communication
    package "Communication" {
        usecase "Envoyer notification" as UC61
        usecase "Envoyer email tâches" as UC62
        usecase "Communiquer avec stagiaire" as UC63
        usecase "Alerter échéances" as UC64
    }
}

' Relations Administrateur (accès complet)
Admin --> UC01
Admin --> UC02
Admin --> UC03
Admin --> UC04
Admin --> UC05
Admin --> UC06
Admin --> UC07
Admin --> UC08
Admin --> UC09
Admin --> UC10
Admin --> UC11
Admin --> UC12
Admin --> UC15
Admin --> UC16
Admin --> UC17
Admin --> UC18
Admin --> UC19
Admin --> UC20
Admin --> UC21
Admin --> UC22
Admin --> UC23
Admin --> UC24
Admin --> UC25
Admin --> UC26
Admin --> UC27
Admin --> UC28
Admin --> UC29
Admin --> UC30
Admin --> UC31
Admin --> UC32
Admin --> UC33
Admin --> UC34
Admin --> UC35
Admin --> UC36
Admin --> UC37
Admin --> UC38
Admin --> UC39
Admin --> UC40
Admin --> UC41
Admin --> UC42
Admin --> UC43
Admin --> UC44
Admin --> UC45
Admin --> UC46
Admin --> UC47
Admin --> UC48
Admin --> UC49
Admin --> UC50
Admin --> UC51
Admin --> UC52
Admin --> UC53
Admin --> UC54
Admin --> UC55
Admin --> UC56
Admin --> UC57
Admin --> UC58
Admin --> UC59
Admin --> UC60
Admin --> UC61
Admin --> UC62
Admin --> UC63
Admin --> UC64

' Relations Gestionnaire RH
RH --> UC01
RH --> UC02
RH --> UC03
RH --> UC04
RH --> UC10
RH --> UC11
RH --> UC12
RH --> UC13
RH --> UC14
RH --> UC15
RH --> UC16
RH --> UC17
RH --> UC19
RH --> UC20
RH --> UC21
RH --> UC22
RH --> UC23
RH --> UC24
RH --> UC25
RH --> UC26
RH --> UC27
RH --> UC28
RH --> UC29
RH --> UC30
RH --> UC31
RH --> UC32
RH --> UC33
RH --> UC34
RH --> UC35
RH --> UC36
RH --> UC37
RH --> UC38
RH --> UC39
RH --> UC40
RH --> UC41
RH --> UC42
RH --> UC43
RH --> UC44
RH --> UC45
RH --> UC46
RH --> UC47
RH --> UC48
RH --> UC49
RH --> UC50
RH --> UC51
RH --> UC52
RH --> UC53
RH --> UC54
RH --> UC55
RH --> UC56
RH --> UC57
RH --> UC58
RH --> UC59
RH --> UC61
RH --> UC62
RH --> UC63
RH --> UC64

' Relations Encadrant (limité à son service)
Encadrant --> UC01
Encadrant --> UC02
Encadrant --> UC03
Encadrant --> UC04
Encadrant --> UC12
Encadrant --> UC15
Encadrant --> UC16
Encadrant --> UC17
Encadrant --> UC19
Encadrant --> UC20
Encadrant --> UC23
Encadrant --> UC24
Encadrant --> UC28
Encadrant --> UC29
Encadrant --> UC30
Encadrant --> UC31
Encadrant --> UC32
Encadrant --> UC33
Encadrant --> UC34
Encadrant --> UC35
Encadrant --> UC36
Encadrant --> UC37
Encadrant --> UC38
Encadrant --> UC39
Encadrant --> UC40
Encadrant --> UC41
Encadrant --> UC42
Encadrant --> UC43
Encadrant --> UC44
Encadrant --> UC51
Encadrant --> UC52
Encadrant --> UC53
Encadrant --> UC54
Encadrant --> UC56
Encadrant --> UC62
Encadrant --> UC63

' Relations d'inclusion et d'extension
UC28 ..> UC29 : <<include>>
UC28 ..> UC30 : <<include>>
UC28 ..> UC33 : <<include>>
UC30 ..> UC34 : <<include>>
UC30 ..> UC35 : <<include>>
UC15 ..> UC22 : <<include>>
UC46 ..> UC47 : <<include>>
UC47 ..> UC48 : <<include>>
UC32 ..> UC36 : <<include>>
UC43 ..> UC44 : <<extend>>
UC53 ..> UC54 : <<extend>>
UC64 ..> UC61 : <<include>>

' Notes explicatives
note right of Admin : Accès complet à toutes\nles fonctionnalités du système
note right of RH : Gestion administrative\net contractuelle des stages
note right of Encadrant : Supervision pédagogique\nlimitée à son service

note top of UC28 : Fonctionnalité centrale\npour le suivi des stagiaires
note top of UC15 : Point d'entrée principal\npour l'ajout de nouveaux stagiaires
note top of UC46 : Processus de contractualisation\navec signatures numériques

@enduml
