{% extends 'stagiaires/base.html' %}

{% block title %}Rapports et Statistiques - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">{{ title }}</h1>
    
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">Total des stagiaires</h5>
                    <p class="card-text display-4">{{ total_stagiaires }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Stagiaires actifs</h5>
                    <p class="card-text display-4">{{ stagiaires_actifs }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">Stages terminés</h5>
                    <p class="card-text display-4">{{ stagiaires_termines }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Répartition par département</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Département</th>
                                <th>Nombre de stagiaires</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stat in stats_departement %}
                            <tr>
                                <td>{{ stat.departement }}</td>
                                <td>{{ stat.total }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="2" class="text-center">Aucune donnée disponible</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Répartition par encadrant</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Encadrant</th>
                                <th>Nombre de stagiaires</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stat in stats_encadrant %}
                            <tr>
                                <td>{{ stat.encadrant__first_name }} {{ stat.encadrant__last_name }}</td>
                                <td>{{ stat.total }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="2" class="text-center">Aucune donnée disponible</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Exporter les données</h5>
                </div>
                <div class="card-body">
                    <a href="{% url 'export_durees_csv' %}" class="btn btn-primary me-2">
                        <i class="bi bi-file-earmark-text"></i> Exporter en CSV
                    </a>
                    <a href="{% url 'export_durees_excel' %}" class="btn btn-success">
                        <i class="bi bi-file-earmark-excel"></i> Exporter en Excel
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

