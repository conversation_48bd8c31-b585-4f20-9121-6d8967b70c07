{% extends 'stagiaires/base.html' %}

{% block title %}{{ title }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        Modifier le sujet
                    </h3>
                    <a href="{% url 'sujets_list' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                    </a>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.titre.id_for_label }}" class="form-label">{{ form.titre.label }}</label>
                            {{ form.titre }}
                            {% if form.titre.errors %}
                                <div class="text-danger small mt-1">{{ form.titre.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">{{ form.description.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.thematique.id_for_label }}" class="form-label">{{ form.thematique.label }}</label>
                            {{ form.thematique }}
                            {% if form.thematique.errors %}
                                <div class="text-danger small mt-1">{{ form.thematique.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.niveau_difficulte.id_for_label }}" class="form-label">{{ form.niveau_difficulte.label }}</label>
                            {{ form.niveau_difficulte }}
                            {% if form.niveau_difficulte.errors %}
                                <div class="text-danger small mt-1">{{ form.niveau_difficulte.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3 form-check">
                            {{ form.actif }}
                            <label class="form-check-label" for="{{ form.actif.id_for_label }}">{{ form.actif.label }}</label>
                            {% if form.actif.errors %}
                                <div class="text-danger small mt-1">{{ form.actif.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Enregistrer les modifications
                            </button>
                            <a href="{% url 'sujets_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
