#!/usr/bin/env python
"""
Script de test pour vérifier la fonctionnalité de la case à cocher Admin
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Service
from stagiaires.forms import CustomUserCreationForm, CustomUserForm

User = get_user_model()

def test_admin_checkbox():
    """Test de la case à cocher Admin"""
    
    print("=== Test de la case à cocher Admin ===")
    
    # 1. Test du formulaire de création
    print("\n🔧 Test du formulaire de création:")
    
    # Créer un service pour les tests
    service_test, created = Service.objects.get_or_create(
        code_service='TEST',
        defaults={
            'nom': 'Service Test',
            'description': 'Service pour les tests',
            'actif': True
        }
    )
    
    # Test 1: Créer un utilisateur RH avec privilèges admin
    form_data = {
        'username': 'rh_admin_test',
        'email': '<EMAIL>',
        'first_name': 'RH',
        'last_name': 'Admin',
        'role': 'RH',
        'service': '',  # Pas de service pour RH
        'is_admin': True,  # Privilèges admin
        'password1': 'testpassword123',
        'password2': 'testpassword123'
    }
    
    form = CustomUserCreationForm(data=form_data)
    if form.is_valid():
        user = form.save()
        print(f"✅ Utilisateur RH avec admin créé: {user.username}")
        print(f"   Role: {user.role}")
        print(f"   is_superuser: {user.is_superuser}")
        print(f"   is_staff: {user.is_staff}")
        print(f"   is_admin property: {user.is_admin}")
    else:
        print(f"❌ Erreur dans le formulaire: {form.errors}")
    
    # Test 2: Créer un encadrant avec privilèges admin
    form_data2 = {
        'username': 'encadrant_admin_test',
        'email': '<EMAIL>',
        'first_name': 'Encadrant',
        'last_name': 'Admin',
        'role': 'ENCADRANT',
        'service': service_test.id,  # Service obligatoire pour encadrant
        'is_admin': True,  # Privilèges admin
        'password1': 'testpassword123',
        'password2': 'testpassword123'
    }
    
    form2 = CustomUserCreationForm(data=form_data2)
    if form2.is_valid():
        user2 = form2.save()
        print(f"✅ Encadrant avec admin créé: {user2.username}")
        print(f"   Role: {user2.role}")
        print(f"   Service: {user2.service.nom if user2.service else 'Aucun'}")
        print(f"   is_superuser: {user2.is_superuser}")
        print(f"   is_staff: {user2.is_staff}")
        print(f"   is_admin property: {user2.is_admin}")
    else:
        print(f"❌ Erreur dans le formulaire: {form2.errors}")
    
    # Test 3: Créer un utilisateur normal sans privilèges admin
    form_data3 = {
        'username': 'rh_normal_test',
        'email': '<EMAIL>',
        'first_name': 'RH',
        'last_name': 'Normal',
        'role': 'RH',
        'service': '',
        'is_admin': False,  # Pas de privilèges admin
        'password1': 'testpassword123',
        'password2': 'testpassword123'
    }
    
    form3 = CustomUserCreationForm(data=form_data3)
    if form3.is_valid():
        user3 = form3.save()
        print(f"✅ Utilisateur RH normal créé: {user3.username}")
        print(f"   Role: {user3.role}")
        print(f"   is_superuser: {user3.is_superuser}")
        print(f"   is_staff: {user3.is_staff}")
        print(f"   is_admin property: {user3.is_admin}")
    else:
        print(f"❌ Erreur dans le formulaire: {form3.errors}")
    
    # 2. Test du formulaire d'édition
    print("\n✏️  Test du formulaire d'édition:")
    
    if 'user3' in locals():
        # Modifier l'utilisateur normal pour lui donner des privilèges admin
        edit_form_data = {
            'username': user3.username,
            'email': user3.email,
            'first_name': user3.first_name,
            'last_name': user3.last_name,
            'role': user3.role,
            'service': user3.service.id if user3.service else '',
            'is_admin': True,  # Donner les privilèges admin
            'is_active': True
        }
        
        edit_form = CustomUserForm(data=edit_form_data, instance=user3)
        if edit_form.is_valid():
            updated_user = edit_form.save()
            print(f"✅ Utilisateur modifié: {updated_user.username}")
            print(f"   is_superuser après modification: {updated_user.is_superuser}")
            print(f"   is_staff après modification: {updated_user.is_staff}")
            print(f"   is_admin property après modification: {updated_user.is_admin}")
        else:
            print(f"❌ Erreur dans le formulaire d'édition: {edit_form.errors}")
    
    # 3. Test des conditions d'affichage du menu
    print("\n🎨 Test des conditions d'affichage:")
    
    all_test_users = User.objects.filter(username__contains='_test')
    
    for user in all_test_users:
        menu_visible = user.is_superuser  # Nouvelle condition
        print(f"\n👤 {user.username} ({user.get_role_display()}):")
        print(f"   Role: {user.role}")
        print(f"   is_superuser: {user.is_superuser}")
        print(f"   Menu Admin visible: {menu_visible}")
        print(f"   Propriété is_admin: {user.is_admin}")
    
    # 4. Nettoyer les utilisateurs de test
    print(f"\n🧹 Nettoyage des utilisateurs de test:")
    deleted_count = all_test_users.count()
    all_test_users.delete()
    print(f"✅ {deleted_count} utilisateurs de test supprimés")
    
    # Nettoyer le service de test
    if created:
        service_test.delete()
        print(f"✅ Service de test supprimé")
    
    print(f"\n=== Test terminé avec succès ===")

if __name__ == '__main__':
    test_admin_checkbox()
