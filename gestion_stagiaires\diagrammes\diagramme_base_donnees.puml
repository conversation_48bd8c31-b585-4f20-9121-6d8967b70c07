@startuml Diagramme_Base_Donnees_MEF
!theme plain
skinparam linetype ortho
skinparam packageStyle rectangle
skinparam classAttributeIconSize 0

title Diagramme de Base de Données - Système de Gestion des Stagiaires MEF

package "Authentification et Services" {
    entity CustomUser {
        * id : INTEGER <<PK>>
        --
        * username : VA<PERSON>HA<PERSON>(150) <<UK>>
        * email : VARCHAR(254) <<UK>>
        first_name : VARCHA<PERSON>(150)
        last_name : VARCHAR(150)
        password : VARCHAR(128)
        * role : VARCHAR(10) <<ADMIN,RH,ENCADRANT,STAGIAIRE>>
        service_id : INTEGER <<FK>>
        * is_active : BOOLEAN
        * is_staff : BOOLEAN
        * date_joined : DATETIME
        last_login : DATETIME
        * date_creation : DATETIME
    }

    entity Service {
        * id : INTEGER <<PK>>
        --
        * nom : VARCHAR(100)
        * code_service : VARCHAR(10) <<UK>>
        description : TEXT
        responsable_id : INTEGER <<FK>>
        * actif : BOOLEAN
        * date_creation : DATETIME
        date_modification : DATETIME
        cree_par_id : INTEGER <<FK>>
        modifie_par_id : INTEGER <<FK>>
    }
}

package "Gestion Académique" {
    entity Thematique {
        * id : INTEGER <<PK>>
        --
        * titre : VARCHAR(200)
        description : TEXT
        * active : BOOLEAN
        service_id : INTEGER <<FK>>
        cree_par_id : INTEGER <<FK>>
        * date_creation : DATETIME
    }

    entity Sujet {
        * id : INTEGER <<PK>>
        --
        * titre : VARCHAR(200)
        * description : TEXT
        * thematique_id : INTEGER <<FK>>
        service_id : INTEGER <<FK>>
        encadrant_id : INTEGER <<FK>>
        * duree_recommandee : INTEGER
        competences_requises : TEXT
        niveau_difficulte : VARCHAR(20)
        technologies_utilisees : TEXT
        objectifs_pedagogiques : TEXT
        livrables_attendus : TEXT
        * actif : BOOLEAN
        * date_creation : DATETIME
        cree_par_id : INTEGER <<FK>>
    }
}

package "Gestion des Stagiaires" {
    entity Stagiaire {
        * id : INTEGER <<PK>>
        --
        * nom : VARCHAR(100)
        * prenom : VARCHAR(100)
        * email : VARCHAR(254) <<UK>>
        telephone : VARCHAR(20)
        * date_naissance : DATE
        lieu_naissance : VARCHAR(100)
        adresse : TEXT
        * departement : VARCHAR(20)
        service_id : INTEGER <<FK>>
        encadrant_id : INTEGER <<FK>>
        * date_debut : DATE
        * date_fin : DATE
        * statut : VARCHAR(20) <<EN_COURS,TERMINE,SUSPENDU,ANNULE>>
        * etablissement : VARCHAR(200)
        * niveau_etude : VARCHAR(100)
        * specialite : VARCHAR(100)
        thematique_id : INTEGER <<FK>>
        sujet_id : INTEGER <<FK>>
        * duree_estimee : INTEGER
        technologies : TEXT
        cv : VARCHAR(100)
        convention_stage : VARCHAR(100)
        assurance : VARCHAR(100)
        rapport_final : VARCHAR(100)
        * statut_convention : VARCHAR(20)
        date_validation_convention : DATETIME
        validee_par_id : INTEGER <<FK>>
        commentaire_convention : TEXT
        description_taches : TEXT
        * statut_taches : VARCHAR(30)
        * date_creation : DATETIME
        date_modification : DATETIME
        cree_par_id : INTEGER <<FK>>
    }
}

package "Gestion des Tâches et Missions" {
    entity Tache {
        * id : INTEGER <<PK>>
        --
        * titre : VARCHAR(200)
        description : TEXT
        * stagiaire_id : INTEGER <<FK>>
        * statut : VARCHAR(20) <<A_FAIRE,EN_COURS,TERMINEE,ANNULEE>>
        * priorite : VARCHAR(20) <<BASSE,NORMALE,HAUTE,URGENTE>>
        date_debut : DATE
        date_fin_prevue : DATE
        date_debut_reelle : DATE
        date_fin_reelle : DATE
        creee_par_id : INTEGER <<FK>>
        * date_creation : DATETIME
        * date_modification : DATETIME
    }

    entity Mission {
        * id : INTEGER <<PK>>
        --
        * stagiaire_id : INTEGER <<FK>>
        * titre : VARCHAR(200)
        * description : TEXT
        * objectifs : TEXT
        * livrables_attendus : TEXT
        * date_debut_prevue : DATE
        * date_fin_prevue : DATE
        date_debut_reelle : DATE
        date_fin_reelle : DATE
        * priorite : INTEGER
        * statut : VARCHAR(20) <<PLANIFIEE,EN_COURS,TERMINEE,VALIDEE,REJETEE>>
        * pourcentage_avancement : INTEGER
        commentaire_avancement : TEXT
        * derniere_mise_a_jour : DATETIME
        creee_par_id : INTEGER <<FK>>
        * date_creation : DATETIME
    }
}

package "Gestion Documentaire" {
    entity RapportStage {
        * id : INTEGER <<PK>>
        --
        * stagiaire_id : INTEGER <<FK>>
        mission_id : INTEGER <<FK>>
        * titre : VARCHAR(200)
        * fichier_rapport : VARCHAR(100)
        * description : TEXT
        * statut : VARCHAR(20) <<BROUILLON,SOUMIS,EN_REVISION,VALIDE,REJETE>>
        date_soumission : DATETIME
        date_validation : DATETIME
        valide_par_id : INTEGER <<FK>>
        commentaires_encadrant : TEXT
        note_rapport : DECIMAL(4,2)
        * date_creation : DATETIME
        * date_modification : DATETIME
    }

    entity ContratStage {
        * id : INTEGER <<PK>>
        --
        * reference : VARCHAR(50) <<UK>>
        * stagiaire_id : INTEGER <<FK>>
        * type_contrat : VARCHAR(30)
        * titre_stage : VARCHAR(200)
        * description_missions : TEXT
        * objectifs_pedagogiques : TEXT
        competences_acquises : TEXT
        * duree_hebdomadaire : INTEGER
        gratification_mensuelle : DECIMAL(8,2)
        avantages : TEXT
        encadrant_entreprise_id : INTEGER <<FK>>
        tuteur_pedagogique : VARCHAR(200)
        * statut : VARCHAR(30)
        * signature_rh : BOOLEAN
        date_signature_rh : DATETIME
        signature_rh_par_id : INTEGER <<FK>>
        document_contrat : VARCHAR(100)
        document_signe : VARCHAR(100)
        * date_creation : DATETIME
        cree_par_id : INTEGER <<FK>>
        * date_modification : DATETIME
        date_expiration : DATE
        commentaires_admin : TEXT
        notes_internes : TEXT
    }
}

package "Utilitaires" {
    entity DureeEstimee {
        * id : INTEGER <<PK>>
        --
        * duree : INTEGER
        commentaire : TEXT
        cree_par_id : INTEGER <<FK>>
        * date_creation : DATETIME
    }
}

' Relations principales
CustomUser ||--o{ Service : "responsable_id"
CustomUser ||--o{ Service : "cree_par_id"
CustomUser ||--o{ Service : "modifie_par_id"
Service ||--o{ CustomUser : "service_id"

Service ||--o{ Thematique : "service_id"
Service ||--o{ Sujet : "service_id"
Service ||--o{ Stagiaire : "service_id"

CustomUser ||--o{ Thematique : "cree_par_id"
CustomUser ||--o{ Sujet : "encadrant_id"
CustomUser ||--o{ Sujet : "cree_par_id"

Thematique ||--o{ Sujet : "thematique_id"
Thematique ||--o{ Stagiaire : "thematique_id"
Sujet ||--o{ Stagiaire : "sujet_id"

CustomUser ||--o{ Stagiaire : "encadrant_id"
CustomUser ||--o{ Stagiaire : "cree_par_id"
CustomUser ||--o{ Stagiaire : "validee_par_id"

Stagiaire ||--o{ Tache : "stagiaire_id"
Stagiaire ||--o{ Mission : "stagiaire_id"
Stagiaire ||--o{ RapportStage : "stagiaire_id"
Stagiaire ||--o{ ContratStage : "stagiaire_id"

CustomUser ||--o{ Tache : "creee_par_id"
CustomUser ||--o{ Mission : "creee_par_id"
CustomUser ||--o{ RapportStage : "valide_par_id"
CustomUser ||--o{ ContratStage : "encadrant_entreprise_id"
CustomUser ||--o{ ContratStage : "signature_rh_par_id"
CustomUser ||--o{ ContratStage : "cree_par_id"
CustomUser ||--o{ DureeEstimee : "cree_par_id"

Mission ||--o{ RapportStage : "mission_id"

@enduml
