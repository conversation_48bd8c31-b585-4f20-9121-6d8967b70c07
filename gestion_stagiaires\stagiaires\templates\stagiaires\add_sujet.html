{% extends 'stagiaires/base.html' %}

{% block title %}Ajouter un sujet de stage{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h3><i class="fas fa-lightbulb me-2"></i>Ajouter un sujet de stage</h3>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                
                {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {{ form.non_field_errors }}
                </div>
                {% endif %}
                
                <div class="row mb-3">
                    <div class="col-md-8">
                        <label for="{{ form.titre.id_for_label }}" class="form-label">Titre du sujet *</label>
                        {{ form.titre }}
                        {% if form.titre.errors %}
                        <div class="text-danger small mt-1">{{ form.titre.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <label for="{{ form.thematique.id_for_label }}" class="form-label">Thématique *</label>
                        {{ form.thematique }}
                        {% if form.thematique.errors %}
                        <div class="text-danger small mt-1">{{ form.thematique.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="{{ form.description.id_for_label }}" class="form-label">Description *</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                    <div class="text-danger small mt-1">{{ form.description.errors }}</div>
                    {% endif %}
                </div>

                <!-- Section Service -->
                {% if user.role == 'ENCADRANT' and 'service_info' in form.fields %}
                <div class="mb-3">
                    <label for="{{ form.service_info.id_for_label }}" class="form-label">Service</label>
                    {{ form.service_info }}
                    <div class="form-text text-info">
                        <i class="fas fa-info-circle me-1"></i>
                        Les sujets que vous créez sont automatiquement assignés à votre service.
                    </div>
                    {{ form.service }}
                </div>
                {% else %}
                <div class="mb-3">
                    <label for="{{ form.service.id_for_label }}" class="form-label">Service</label>
                    {{ form.service }}
                    {% if form.service.errors %}
                    <div class="text-danger small mt-1">{{ form.service.errors }}</div>
                    {% endif %}
                </div>
                {% endif %}

                <div class="row mb-3">
                    {% if user.role != 'ENCADRANT' %}
                    <div class="col-md-6">
                        <label for="{{ form.encadrant.id_for_label }}" class="form-label">Encadrant *</label>
                        {{ form.encadrant }}
                        {% if form.encadrant.errors %}
                        <div class="text-danger small mt-1">{{ form.encadrant.errors }}</div>
                        {% endif %}
                    </div>
                    {% else %}
                    <!-- Si l'utilisateur est un encadrant, afficher son nom et inclure le champ caché -->
                    <div class="col-md-6">
                        <label class="form-label">Encadrant</label>
                        <input type="text" class="form-control" value="{{ user.get_full_name|default:user.username }}" readonly>
                        {{ form.encadrant }}
                    </div>
                    {% endif %}
                    
                    <div class="col-md-6">
                        <label for="{{ form.niveau_difficulte.id_for_label }}" class="form-label">Niveau de difficulté *</label>
                        {{ form.niveau_difficulte }}
                        {% if form.niveau_difficulte.errors %}
                        <div class="text-danger small mt-1">{{ form.niveau_difficulte.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="{{ form.duree_recommandee.id_for_label }}" class="form-label">Durée recommandée (jours) *</label>
                        {{ form.duree_recommandee }}
                        {% if form.duree_recommandee.errors %}
                        <div class="text-danger small mt-1">{{ form.duree_recommandee.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="{{ form.competences_requises.id_for_label }}" class="form-label">Compétences requises</label>
                    {{ form.competences_requises }}
                    {% if form.competences_requises.errors %}
                    <div class="text-danger small mt-1">{{ form.competences_requises.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="mb-3 form-check">
                    {{ form.actif }}
                    <label class="form-check-label" for="{{ form.actif.id_for_label }}">Sujet actif</label>
                    {% if form.actif.errors %}
                    <div class="text-danger small mt-1">{{ form.actif.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="d-flex justify-content-between mt-4">
                    <a href="{% url 'sujets_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i>Enregistrer le sujet
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}


