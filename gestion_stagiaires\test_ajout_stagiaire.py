#!/usr/bin/env python
"""
Test de l'ajout de stagiaires par l'admin
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service
from datetime import date, timedelta

User = get_user_model()

def test_ajout_stagiaire():
    """Test de l'ajout de stagiaires"""
    
    print("=== Test de l'ajout de stagiaires par l'admin ===")
    
    # 1. Vérifier les utilisateurs admin
    print("\n1️⃣ Vérification des utilisateurs admin:")
    
    admins = User.objects.filter(role='ADMIN', is_active=True)
    print(f"   Nombre d'admins: {admins.count()}")
    
    if not admins.exists():
        print("   ❌ Aucun admin trouvé")
        return
    
    admin_user = admins.first()
    print(f"   ✅ Admin: {admin_user.username}")
    
    # 2. Vérifier les services et encadrants
    print("\n2️⃣ Vérification des services et encadrants:")
    
    services = Service.objects.filter(actif=True)
    print(f"   Services actifs: {services.count()}")
    
    encadrants = User.objects.filter(role='ENCADRANT', is_active=True)
    print(f"   Encadrants actifs: {encadrants.count()}")
    
    if not encadrants.exists():
        print("   ❌ Aucun encadrant trouvé")
        return
    
    encadrant = encadrants.first()
    print(f"   ✅ Encadrant: {encadrant.get_full_name()}")
    
    # 3. Test d'accès à la page d'ajout
    print("\n3️⃣ Test d'accès à la page d'ajout:")
    
    client = Client()
    client.force_login(admin_user)
    
    try:
        response = client.get('/add-stagiaire/')
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Page d'ajout accessible")
            
            content = response.content.decode('utf-8')
            
            # Vérifier la présence du formulaire
            checks = [
                ('form', 'Formulaire présent'),
                ('id_nom', 'Champ nom'),
                ('id_prenom', 'Champ prénom'),
                ('id_email', 'Champ email'),
                ('id_encadrant', 'Champ encadrant'),
                ('csrf', 'Token CSRF'),
            ]
            
            for check, desc in checks:
                if check in content:
                    print(f"      ✅ {desc}")
                else:
                    print(f"      ❌ {desc} manquant")
        
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
    
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # 4. Test d'ajout de stagiaire
    print("\n4️⃣ Test d'ajout de stagiaire:")
    
    # Données de test
    stagiaire_data = {
        'nom': 'TestNom',
        'prenom': 'TestPrenom',
        'email': '<EMAIL>',
        'telephone': '0123456789',
        'date_naissance': '2000-01-01',
        'departement': 'IT',
        'encadrant': encadrant.id,
        'date_debut': date.today().strftime('%Y-%m-%d'),
        'date_fin': (date.today() + timedelta(days=90)).strftime('%Y-%m-%d'),
        'etablissement': 'Université Test',
        'niveau_etude': 'Master 2',
        'specialite': 'Informatique',
        'statut': 'EN_COURS',
        'technologies': 'Python, Django',
    }
    
    if services.exists():
        stagiaire_data['service'] = services.first().id
    
    print(f"   Données de test: {stagiaire_data}")
    
    # Supprimer le stagiaire de test s'il existe
    Stagiaire.objects.filter(email='<EMAIL>').delete()
    
    try:
        response = client.post('/add-stagiaire/', stagiaire_data)
        print(f"   Status POST: {response.status_code}")
        
        if response.status_code == 302:  # Redirection après succès
            print("   ✅ Redirection après ajout")
            
            # Vérifier que le stagiaire a été créé
            stagiaire_cree = Stagiaire.objects.filter(email='<EMAIL>').first()
            
            if stagiaire_cree:
                print(f"   ✅ Stagiaire créé: {stagiaire_cree.nom_complet}")
                print(f"      ID: {stagiaire_cree.id}")
                print(f"      Créé par: {stagiaire_cree.cree_par}")
                print(f"      Encadrant: {stagiaire_cree.encadrant}")
                print(f"      Service: {stagiaire_cree.service}")
                
                # Nettoyer
                stagiaire_cree.delete()
                print("   🧹 Stagiaire de test supprimé")
            else:
                print("   ❌ Stagiaire non créé en base")
        
        elif response.status_code == 200:  # Formulaire avec erreurs
            print("   ⚠️ Formulaire retourné avec erreurs")
            
            content = response.content.decode('utf-8')
            
            # Chercher les erreurs dans le contenu
            if 'error' in content.lower() or 'erreur' in content.lower():
                print("   ❌ Erreurs détectées dans le formulaire")
                
                # Extraire les erreurs si possible
                if 'alert-danger' in content:
                    print("   Erreurs trouvées dans le HTML")
            else:
                print("   ⚠️ Pas d'erreurs visibles, mais formulaire non soumis")
        
        else:
            print(f"   ❌ Status inattendu: {response.status_code}")
    
    except Exception as e:
        print(f"   ❌ Erreur lors de l'ajout: {e}")
        import traceback
        traceback.print_exc()
    
    # 5. Test avec l'admin Django
    print("\n5️⃣ Test avec l'admin Django:")
    
    try:
        response = client.get('/admin/stagiaires/stagiaire/add/')
        print(f"   Status admin: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Page admin accessible")
            
            # Test d'ajout via admin
            admin_data = {
                'nom': 'AdminTest',
                'prenom': 'AdminTest',
                'email': '<EMAIL>',
                'date_naissance': '2000-01-01',
                'departement': 'IT',
                'encadrant': encadrant.id,
                'date_debut': date.today().strftime('%Y-%m-%d'),
                'date_fin': (date.today() + timedelta(days=90)).strftime('%Y-%m-%d'),
                'etablissement': 'Test Admin',
                'niveau_etude': 'Master',
                'specialite': 'Test',
                'statut': 'EN_COURS',
                '_save': 'Enregistrer',
            }
            
            if services.exists():
                admin_data['service'] = services.first().id
            
            # Supprimer le stagiaire de test s'il existe
            Stagiaire.objects.filter(email='<EMAIL>').delete()
            
            response = client.post('/admin/stagiaires/stagiaire/add/', admin_data)
            print(f"   Status POST admin: {response.status_code}")
            
            if response.status_code == 302:
                print("   ✅ Ajout via admin réussi")
                
                stagiaire_admin = Stagiaire.objects.filter(email='<EMAIL>').first()
                if stagiaire_admin:
                    print(f"   ✅ Stagiaire admin créé: {stagiaire_admin.nom_complet}")
                    stagiaire_admin.delete()
                    print("   🧹 Stagiaire admin supprimé")
            else:
                print("   ❌ Échec ajout via admin")
        
    except Exception as e:
        print(f"   ❌ Erreur admin: {e}")
    
    print(f"\n{'='*50}")
    print("📊 DIAGNOSTIC:")
    print("Si l'ajout ne fonctionne pas, vérifiez:")
    print("1. Les permissions de l'utilisateur admin")
    print("2. La validation du formulaire")
    print("3. Les contraintes de base de données")
    print("4. Les erreurs JavaScript côté client")
    print("5. Les logs du serveur Django")
    print(f"{'='*50}")

if __name__ == '__main__':
    test_ajout_stagiaire()
