{% extends 'stagiaires/base.html' %}

{% block title %}Inscription - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="col-md-8 col-lg-6">
        <div class="auth-card">
            <div class="auth-header">
                <h2 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    Créer un compte
                </h2>
                <p class="mb-0 mt-2 opacity-75">Rejoignez l'équipe de gestion des stagiaires MEF</p>
            </div>
            <div class="auth-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-1"></i>Prénom
                            </label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.first_name.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-1"></i>Nom
                            </label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.last_name.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-at me-1"></i>Nom d'utilisateur
                        </label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.username.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Adresse email
                        </label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.email.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.role.id_for_label }}" class="form-label">
                            <i class="fas fa-briefcase me-1"></i>Rôle
                        </label>
                        {{ form.role }}
                        {% if form.role.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.role.errors }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3" id="service-field" style="display: none;">
                        <label for="{{ form.service.id_for_label }}" class="form-label">
                            <i class="fas fa-building me-1"></i>Service <span class="text-danger">*</span>
                        </label>
                        {{ form.service }}
                        <div class="form-text">Obligatoire pour les encadrants</div>
                        {% if form.service.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.service.errors }}
                            </div>
                        {% endif %}
                    </div>


                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-1"></i>Mot de passe
                            </label>
                            {{ form.password1 }}
                            {% if form.password1.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.password1.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-1"></i>Confirmer le mot de passe
                            </label>
                            {{ form.password2 }}
                            {% if form.password2.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.password2.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Créer le compte
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <p class="mb-0">Déjà un compte ?</p>
                        <a href="{% url 'login' %}" class="text-decoration-none">
                            <i class="fas fa-sign-in-alt me-1"></i>Se connecter
                        </a>
                    </div>
                </form>
                <div class="text-center mt-3">
                    <a href="{% url 'home' %}" class="text-decoration-none">
                        <i class="fas fa-home me-1"></i>Retour à l'accueil
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function toggleServiceField(role) {
        const serviceField = document.getElementById('service-field');
        const serviceSelect = document.getElementById('id_service');

        if (role === 'ENCADRANT') {
            serviceField.style.display = 'block';
            serviceSelect.required = true;
        } else {
            serviceField.style.display = 'none';
            serviceSelect.required = false;
            serviceSelect.value = '';  // Réinitialiser la sélection
        }
    }

    // Exécuter au chargement de la page
    document.addEventListener('DOMContentLoaded', function() {
        const roleSelect = document.getElementById('id_role');
        if (roleSelect) {
            toggleServiceField(roleSelect.value);
        }
    });
</script>
{% endblock %}

