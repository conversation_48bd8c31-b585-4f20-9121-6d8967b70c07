#!/usr/bin/env python
"""
Test de l'ajout de sujets
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Sujet, Thematique, Service

User = get_user_model()

def test_ajout_sujet():
    """Test de l'ajout de sujets"""
    
    print("=== TEST AJOUT DE SUJETS ===")
    
    # 1. Vérifier les données existantes
    print(f"📊 État actuel:")
    print(f"   Sujets en base: {Sujet.objects.count()}")
    print(f"   Thématiques actives: {Thematique.objects.filter(active=True).count()}")
    print(f"   Services actifs: {Service.objects.filter(actif=True).count()}")
    
    # 2. Récupérer un utilisateur admin
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not admin:
        print("❌ Aucun admin trouvé")
        return
    
    print(f"✅ Admin: {admin.get_full_name()}")
    if encadrant:
        print(f"✅ Encadrant: {encadrant.get_full_name()}")
        print(f"   Service encadrant: {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    # 3. Vérifier les thématiques disponibles
    thematiques = Thematique.objects.filter(active=True)
    print(f"\n📋 Thématiques disponibles ({thematiques.count()}):")
    for thematique in thematiques:
        print(f"   • {thematique.titre} (Service: {thematique.service.nom if thematique.service else 'Général'})")
    
    if not thematiques.exists():
        print("❌ Aucune thématique active trouvée")
        return
    
    # 4. Test d'accès au formulaire d'ajout
    print(f"\n📝 Test d'accès au formulaire:")
    
    client = Client()
    client.force_login(admin)
    
    response = client.get('/sujets/add/')
    print(f"   Status GET: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifications du contenu
        checks = [
            ('Ajouter un sujet', 'Titre de la page'),
            ('form method="post"', 'Formulaire POST'),
            ('name="titre"', 'Champ titre'),
            ('name="thematique"', 'Champ thématique'),
            ('name="description"', 'Champ description'),
            ('csrf_token', 'Token CSRF'),
        ]
        
        for check, desc in checks:
            if check in content:
                print(f"      ✅ {desc}")
            else:
                print(f"      ❌ {desc} manquant")
    else:
        print(f"   ❌ Erreur d'accès: {response.status_code}")
        return
    
    # 5. Test d'ajout d'un sujet
    print(f"\n➕ Test d'ajout d'un sujet:")
    
    # Prendre la première thématique disponible
    thematique_test = thematiques.first()
    service_test = thematique_test.service if thematique_test.service else Service.objects.filter(actif=True).first()
    
    test_data = {
        'titre': 'Sujet de test automatique',
        'description': 'Description du sujet de test créé automatiquement',
        'thematique': thematique_test.id,
        'duree_recommandee': 60,
        'actif': True,
    }
    
    if service_test:
        test_data['service'] = service_test.id
    
    print(f"   Données de test:")
    print(f"     • Titre: {test_data['titre']}")
    print(f"     • Thématique: {thematique_test.titre}")
    print(f"     • Service: {service_test.nom if service_test else 'Aucun'}")
    
    # Supprimer le sujet de test s'il existe
    Sujet.objects.filter(titre='Sujet de test automatique').delete()
    
    count_avant = Sujet.objects.count()
    
    try:
        response = client.post('/sujets/add/', test_data)
        print(f"   Status POST: {response.status_code}")
        
        if response.status_code == 302:
            print("   ✅ Ajout réussi (redirection)")
            
            # Vérifier que le sujet a été créé
            sujet_cree = Sujet.objects.filter(titre='Sujet de test automatique').first()
            
            if sujet_cree:
                print(f"   ✅ Sujet créé: {sujet_cree.titre}")
                print(f"      ID: {sujet_cree.id}")
                print(f"      Thématique: {sujet_cree.thematique.titre}")
                print(f"      Service: {sujet_cree.service.nom if sujet_cree.service else 'Aucun'}")
                print(f"      Créé par: {sujet_cree.cree_par.get_full_name()}")
                print(f"      Actif: {sujet_cree.actif}")
                
                # Nettoyer
                sujet_cree.delete()
                print(f"      🧹 Sujet de test supprimé")
            else:
                print("   ❌ Sujet non créé en base")
                
                # Vérifier les erreurs potentielles
                count_apres = Sujet.objects.count()
                print(f"      Sujets avant: {count_avant}, après: {count_apres}")
        
        elif response.status_code == 200:
            print("   ❌ Formulaire renvoyé (erreurs)")
            
            # Essayer de récupérer les erreurs du formulaire
            content = response.content.decode('utf-8')
            if 'alert-danger' in content:
                print("      Des erreurs sont présentes dans le formulaire")
            
            # Afficher le contenu pour debug
            print("      Contenu de la réponse (extrait):")
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'error' in line.lower() or 'alert' in line.lower():
                    print(f"        {i}: {line.strip()}")
        
        else:
            print(f"   ❌ Erreur inattendue: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception lors de l'ajout: {e}")
        import traceback
        traceback.print_exc()
    
    # 6. Test avec encadrant
    if encadrant and encadrant.service:
        print(f"\n👤 Test avec encadrant:")
        
        client.force_login(encadrant)
        
        # Récupérer une thématique du service de l'encadrant
        thematique_encadrant = Thematique.objects.filter(
            service=encadrant.service, active=True
        ).first()
        
        if thematique_encadrant:
            test_data_encadrant = {
                'titre': 'Sujet encadrant test',
                'description': 'Sujet créé par un encadrant',
                'thematique': thematique_encadrant.id,
                'duree_recommandee': 45,
                'actif': True,
            }
            
            # Supprimer le sujet de test s'il existe
            Sujet.objects.filter(titre='Sujet encadrant test').delete()
            
            response = client.post('/sujets/add/', test_data_encadrant)
            print(f"   Status POST encadrant: {response.status_code}")
            
            if response.status_code == 302:
                sujet_encadrant = Sujet.objects.filter(titre='Sujet encadrant test').first()
                if sujet_encadrant:
                    print(f"   ✅ Sujet encadrant créé: {sujet_encadrant.titre}")
                    print(f"      Service: {sujet_encadrant.service.nom if sujet_encadrant.service else 'Aucun'}")
                    sujet_encadrant.delete()
                    print(f"      🧹 Sujet encadrant supprimé")
        else:
            print("   ⚠️ Aucune thématique disponible pour l'encadrant")
    
    print(f"\n{'='*50}")
    print("📊 RÉSUMÉ DU TEST:")
    print("")
    print("✅ POINTS VÉRIFIÉS :")
    print("   • Accès au formulaire d'ajout")
    print("   • Présence des champs requis")
    print("   • Soumission du formulaire")
    print("   • Création en base de données")
    print("   • Permissions par rôle")
    print("")
    print("🎯 SI LE PROBLÈME PERSISTE :")
    print("   1. Vérifiez les messages d'erreur Django")
    print("   2. Consultez les logs du serveur")
    print("   3. Testez manuellement via l'interface web")
    print("   4. Vérifiez que les thématiques sont actives")
    print(f"{'='*50}")

if __name__ == '__main__':
    test_ajout_sujet()
