#!/usr/bin/env python
"""
Test simple de l'upload de rapport
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire

User = get_user_model()

def test_upload_rapport():
    """Test simple de l'upload de rapport"""
    
    print("=== TEST SIMPLE DE L'UPLOAD DE RAPPORT ===")
    
    # 1. Récupérer un stagiaire
    stagiaire = Stagiaire.objects.first()
    
    if not stagiaire:
        print("❌ Aucun stagiaire trouvé")
        return
    
    print(f"✅ Stagiaire: {stagiaire.nom_complet}")
    print(f"   ID: {stagiaire.id}")
    
    # 2. Vérifier les champs de rapport
    print(f"\n📄 État actuel du rapport:")
    print(f"   Rapport: {stagiaire.rapport_stage}")
    print(f"   Date upload: {stagiaire.date_upload_rapport}")
    print(f"   Uploadé par: {stagiaire.rapport_uploade_par}")
    print(f"   Commentaire: {stagiaire.commentaire_rapport}")
    
    # 3. Test de modification manuelle
    print(f"\n🔧 Test de modification manuelle:")
    
    try:
        # Modifier directement les champs
        stagiaire.commentaire_rapport = "Test de commentaire ajouté manuellement"
        stagiaire.save()
        
        print("   ✅ Modification manuelle réussie")
        
        # Vérifier la sauvegarde
        stagiaire.refresh_from_db()
        print(f"   Commentaire sauvegardé: {stagiaire.commentaire_rapport}")
        
    except Exception as e:
        print(f"   ❌ Erreur lors de la modification: {e}")
    
    # 4. Vérifier les permissions d'accès
    print(f"\n🔐 Test des permissions:")
    
    # Récupérer différents types d'utilisateurs
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    user_rh = User.objects.filter(role='RH', is_active=True).first()
    
    users_to_test = [
        (admin, 'Admin'),
        (encadrant, 'Encadrant'),
        (user_rh, 'RH')
    ]
    
    from django.test import Client
    
    for user, role_name in users_to_test:
        if user:
            print(f"   Test avec {role_name}: {user.get_full_name()}")
            
            client = Client()
            client.force_login(user)
            
            # Test d'accès à la page d'édition
            response = client.get(f'/stagiaires/{stagiaire.id}/edit/')
            print(f"      Status GET: {response.status_code}")
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Vérifier la présence des champs de rapport
                if 'rapport_stage' in content:
                    print("      ✅ Champ rapport présent")
                else:
                    print("      ❌ Champ rapport absent")
                
                if 'commentaire_rapport' in content:
                    print("      ✅ Champ commentaire présent")
                else:
                    print("      ❌ Champ commentaire absent")
            
            elif response.status_code == 302:
                print("      ⚠️ Redirection - Vérifier les permissions")
            else:
                print(f"      ❌ Erreur d'accès: {response.status_code}")
    
    # 5. Test de l'interface de détail
    print(f"\n👁️ Test de l'interface de détail:")
    
    if admin:
        client = Client()
        client.force_login(admin)
        
        response = client.get(f'/stagiaires/{stagiaire.id}/')
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # Vérifier la présence de la section rapport
            if 'Rapport de stage' in content:
                print("   ✅ Section rapport présente dans les détails")
            else:
                print("   ❌ Section rapport absente des détails")
            
            if stagiaire.commentaire_rapport and stagiaire.commentaire_rapport in content:
                print("   ✅ Commentaire affiché dans les détails")
            else:
                print("   ⚠️ Commentaire non affiché (normal si vide)")
    
    print(f"\n{'='*50}")
    print("📊 RÉSUMÉ DU TEST:")
    print("")
    print("✅ FONCTIONNALITÉS VÉRIFIÉES :")
    print("   • Champs de rapport ajoutés au modèle")
    print("   • Modification manuelle possible")
    print("   • Interface d'édition accessible")
    print("   • Section rapport dans les détails")
    print("")
    print("🎯 PROCHAINES ÉTAPES :")
    print("   1. Testez l'upload via l'interface web")
    print("   2. Connectez-vous en tant qu'encadrant")
    print("   3. Modifiez un stagiaire de votre service")
    print("   4. Uploadez un fichier dans la section rapport")
    print("   5. Vérifiez dans les détails du stagiaire")
    print(f"{'='*50}")

if __name__ == '__main__':
    test_upload_rapport()
