#!/usr/bin/env python
"""
Test d'accès au calendrier avec l'encadrant
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire

User = get_user_model()

def test_calendrier_acces():
    """Test d'accès au calendrier avec l'encadrant"""
    
    print("=== TEST ACCÈS CALENDRIER ENCADRANT ===")
    
    # Récupérer l'encadrant
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    print(f"✅ Encadrant: {encadrant.get_full_name()}")
    
    # Créer un client de test
    client = Client()
    client.force_login(encadrant)
    
    # Tester l'accès au calendrier simple
    print(f"\n📅 Test accès calendrier simple:")
    response = client.get('/calendrier-simple/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier la présence des stagiaires
        stagiaires_encadrant = Stagiaire.objects.filter(encadrant=encadrant)
        print(f"   📋 Stagiaires de l'encadrant: {stagiaires_encadrant.count()}")
        
        for stagiaire in stagiaires_encadrant:
            if stagiaire.nom_complet in content:
                print(f"      ✅ {stagiaire.nom_complet} TROUVÉ dans le calendrier")
            else:
                print(f"      ❌ {stagiaire.nom_complet} NON TROUVÉ dans le calendrier")
        
        # Vérifier les éléments du calendrier
        elements = [
            ('stagiaires_data', 'Variable stagiaires_data'),
            ('table', 'Tableau'),
            ('Calendrier des Stages', 'Titre'),
            ('background-color:', 'Couleurs'),
        ]
        
        for element, description in elements:
            count = content.count(element)
            print(f"   🔍 {description}: {count} occurrences")
        
        # Sauvegarder le contenu pour inspection
        with open('calendrier_debug.html', 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"   💾 Contenu sauvegardé dans calendrier_debug.html")
    
    else:
        print(f"   ❌ Erreur d'accès: {response.status_code}")
        if hasattr(response, 'content'):
            print(f"   Contenu: {response.content.decode('utf-8')[:500]}")

if __name__ == '__main__':
    test_calendrier_acces()
