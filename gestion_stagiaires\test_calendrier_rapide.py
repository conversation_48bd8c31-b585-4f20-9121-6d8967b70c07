#!/usr/bin/env python
"""
Test rapide du calendrier amélioré
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client

User = get_user_model()

def test_calendrier_rapide():
    """Test rapide du calendrier"""
    
    print("=== Test rapide du calendrier amélioré ===")
    
    # Récupérer un encadrant
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    if not encadrant:
        print("❌ Aucun encadrant trouvé")
        return
    
    print(f"✅ Encadrant: {encadrant.get_full_name()}")
    
    # Test d'accès
    client = Client()
    client.force_login(encadrant)
    
    try:
        response = client.get('/calendrier/')
        print(f"✅ Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # Vérifications
            checks = [
                ('calendrier-ameliore', 'Calendrier amélioré'),
                ('stagiaire-color-box', 'Boîtes de couleur'),
                ('calendrier-table', 'Table du calendrier'),
                ('Légende des stagiaires', 'Légende'),
            ]
            
            for check, desc in checks:
                if check in content:
                    print(f"   ✅ {desc}")
                else:
                    print(f"   ❌ {desc} manquant")
        
        else:
            print(f"❌ Erreur: {response.status_code}")
    
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    print("\n🎯 Le calendrier amélioré est prêt !")
    print("   • Couleurs distinctes pour chaque stagiaire")
    print("   • Légende interactive")
    print("   • Vue calendrier mensuel")
    print("   • Filtrage par service pour les encadrants")

if __name__ == '__main__':
    test_calendrier_rapide()
