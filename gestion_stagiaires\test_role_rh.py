#!/usr/bin/env python
"""
Test du changement de libellé du rôle RH
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client

User = get_user_model()

def test_role_rh():
    """Test du nouveau libellé pour le rôle RH"""
    
    print("=== Test du changement de libellé RH ===")
    
    # 1. Vérifier les choix de rôles
    print("\n1️⃣ Vérification des choix de rôles:")
    
    role_choices = dict(User.ROLE_CHOICES)
    print(f"   Choix disponibles: {role_choices}")
    
    if 'RH' in role_choices:
        print(f"   ✅ Rôle RH: '{role_choices['RH']}'")
        if role_choices['RH'] == 'Gestionnaire RH':
            print("   ✅ Libellé correct: 'Gestionnaire RH'")
        else:
            print(f"   ❌ Libellé incorrect: '{role_choices['RH']}'")
    else:
        print("   ❌ Rôle RH non trouvé")
    
    # 2. Vérifier les utilisateurs RH existants
    print("\n2️⃣ Vérification des utilisateurs RH:")
    
    users_rh = User.objects.filter(role='RH')
    print(f"   Nombre d'utilisateurs RH: {users_rh.count()}")
    
    for user in users_rh:
        role_display = user.get_role_display()
        print(f"   • {user.username}: {role_display}")
        
        if role_display == 'Gestionnaire RH':
            print(f"     ✅ Affichage correct")
        else:
            print(f"     ❌ Affichage incorrect: '{role_display}'")
    
    # 3. Test dans l'interface d'administration
    print("\n3️⃣ Test dans l'interface d'administration:")
    
    admin_user = User.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("   ❌ Aucun administrateur trouvé")
        return
    
    client = Client()
    client.force_login(admin_user)
    
    # Test de la page de liste des utilisateurs
    try:
        response = client.get('/admin/stagiaires/customuser/')
        print(f"   Status page liste: {response.status_code}")
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            if 'Gestionnaire RH' in content:
                print("   ✅ 'Gestionnaire RH' trouvé dans la liste")
            else:
                print("   ❌ 'Gestionnaire RH' non trouvé dans la liste")
            
            if 'Ressources Humaines' in content:
                print("   ⚠️ Ancien libellé 'Ressources Humaines' encore présent")
            else:
                print("   ✅ Ancien libellé 'Ressources Humaines' supprimé")
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test de la page d'ajout d'utilisateur
    try:
        response = client.get('/admin/stagiaires/customuser/add/')
        print(f"   Status page ajout: {response.status_code}")
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            if 'Gestionnaire RH' in content:
                print("   ✅ 'Gestionnaire RH' trouvé dans le formulaire d'ajout")
            else:
                print("   ❌ 'Gestionnaire RH' non trouvé dans le formulaire d'ajout")
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # 4. Test de création d'un utilisateur RH
    print("\n4️⃣ Test de création d'un utilisateur RH:")
    
    try:
        # Supprimer l'utilisateur de test s'il existe
        User.objects.filter(username='test_gestionnaire_rh').delete()
        
        # Créer un nouvel utilisateur RH
        test_user = User.objects.create_user(
            username='test_gestionnaire_rh',
            email='<EMAIL>',
            first_name='Test',
            last_name='Gestionnaire',
            role='RH'
        )
        
        print(f"   ✅ Utilisateur créé: {test_user.username}")
        print(f"   Rôle: {test_user.role}")
        print(f"   Affichage: {test_user.get_role_display()}")
        
        if test_user.get_role_display() == 'Gestionnaire RH':
            print("   ✅ Affichage correct pour le nouvel utilisateur")
        else:
            print(f"   ❌ Affichage incorrect: '{test_user.get_role_display()}'")
        
        # Nettoyer
        test_user.delete()
        print("   🧹 Utilisateur de test supprimé")
        
    except Exception as e:
        print(f"   ❌ Erreur lors de la création: {e}")
    
    # 5. Vérifier dans les templates
    print("\n5️⃣ Vérification dans les templates:")
    
    if users_rh.exists():
        user_rh = users_rh.first()
        
        # Test de la page de profil
        try:
            client.force_login(user_rh)
            response = client.get('/')  # Page d'accueil
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                if 'Gestionnaire RH' in content:
                    print("   ✅ 'Gestionnaire RH' affiché dans l'interface utilisateur")
                else:
                    print("   ⚠️ 'Gestionnaire RH' non trouvé dans l'interface utilisateur")
            
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
    
    print(f"\n{'='*50}")
    print("📊 RÉSUMÉ:")
    print("✅ Le libellé 'Ressources Humaines' a été changé en 'Gestionnaire RH'")
    print("✅ Le changement s'applique à:")
    print("   • L'interface d'administration Django")
    print("   • Les formulaires d'ajout/modification")
    print("   • L'affichage dans les templates")
    print("   • La méthode get_role_display()")
    print(f"{'='*50}")

if __name__ == '__main__':
    test_role_rh()
