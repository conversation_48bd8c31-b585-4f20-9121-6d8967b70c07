# Generated manually to create the thematique table

from django.db import migrations, models
import django.db.models.deletion
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        ('stagiaires', '0001_initial'),
    ]
    
    # Désactiver les transactions atomiques pour cette migration
    atomic = False

    operations = [
        # Désactiver temporairement les contraintes de clé étrangère
        migrations.RunSQL(
            "PRAGMA foreign_keys = OFF;",
            "PRAGMA foreign_keys = ON;"
        ),
        
        migrations.CreateModel(
            name='Thematique',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titre', models.CharField(max_length=200, verbose_name='Titre')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('active', models.BooleanField(default=True, verbose_name='Active')),
                ('date_creation', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
                ('date_modification', models.DateTimeField(auto_now=True, verbose_name='Date de modification')),
                ('cree_par', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='thematiques_creees', to=settings.AUTH_USER_MODEL, verbose_name='Créée par')),
                ('service', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='thematiques', to='stagiaires.service', verbose_name='Service')),
            ],
            options={
                'verbose_name': 'Thématique',
                'verbose_name_plural': 'Thématiques',
                'ordering': ['-date_creation'],
            },
        ),
        
        # Insérer une thématique par défaut avec l'ID 1
        migrations.RunSQL(
            "INSERT INTO stagiaires_thematique (id, titre, active, date_creation, date_modification) VALUES (1, 'Thématique par défaut', 1, datetime('now'), datetime('now'));",
            "DELETE FROM stagiaires_thematique WHERE id = 1;"
        ),
        
        # Réactiver les contraintes de clé étrangère
        migrations.RunSQL(
            "PRAGMA foreign_keys = ON;",
            "PRAGMA foreign_keys = OFF;"
        ),
    ]
