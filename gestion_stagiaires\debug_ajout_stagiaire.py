#!/usr/bin/env python
"""
Debug de l'ajout de stagiaires - Interface web
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service
from datetime import date, timedelta
import re

User = get_user_model()

def debug_ajout_interface():
    """Debug de l'interface d'ajout de stagiaires"""
    
    print("=== Debug de l'interface d'ajout de stagiaires ===")
    
    # Récupérer un admin
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not admin or not encadrant:
        print("❌ Admin ou encadrant manquant")
        return
    
    client = Client()
    client.force_login(admin)
    
    print(f"✅ Connecté en tant que: {admin.username} (rôle: {admin.role})")
    
    # 1. Analyser la page d'ajout
    print("\n1️⃣ Analyse de la page d'ajout:")
    
    response = client.get('/stagiaires/add/')
    content = response.content.decode('utf-8')
    
    # Vérifier les éléments du formulaire
    form_elements = [
        ('form', 'Balise form'),
        ('csrf', 'Token CSRF'),
        ('id_nom', 'Champ nom'),
        ('id_prenom', 'Champ prénom'),
        ('id_email', 'Champ email'),
        ('id_encadrant', 'Champ encadrant'),
        ('submit', 'Bouton submit'),
    ]
    
    for element, desc in form_elements:
        if element in content:
            print(f"   ✅ {desc}")
        else:
            print(f"   ❌ {desc} manquant")
    
    # Vérifier l'action du formulaire
    form_action = re.search(r'<form[^>]*action="([^"]*)"', content)
    if form_action:
        print(f"   📋 Action du formulaire: {form_action.group(1)}")
    else:
        print("   ⚠️ Action du formulaire non trouvée")
    
    # Vérifier la méthode du formulaire
    form_method = re.search(r'<form[^>]*method="([^"]*)"', content)
    if form_method:
        print(f"   📋 Méthode du formulaire: {form_method.group(1)}")
    else:
        print("   ⚠️ Méthode du formulaire non trouvée")
    
    # 2. Test d'ajout avec suivi des redirections
    print("\n2️⃣ Test d'ajout avec suivi des redirections:")
    
    # Supprimer le stagiaire de test s'il existe
    Stagiaire.objects.filter(email='<EMAIL>').delete()
    
    data = {
        'nom': 'DebugTest',
        'prenom': 'DebugTest',
        'email': '<EMAIL>',
        'date_naissance': '2000-01-01',
        'departement': 'IT',
        'encadrant': encadrant.id,
        'date_debut': date.today().strftime('%Y-%m-%d'),
        'date_fin': (date.today() + timedelta(days=30)).strftime('%Y-%m-%d'),
        'etablissement': 'Debug Test',
        'niveau_etude': 'Master',
        'specialite': 'Debug',
        'statut': 'EN_COURS',
    }
    
    print(f"   Données envoyées: {data}")
    
    # Test sans suivre les redirections
    response = client.post('/stagiaires/add/', data)
    print(f"   Status initial: {response.status_code}")
    
    if response.status_code == 302:
        print(f"   ✅ Redirection vers: {response.url}")
        
        # Suivre la redirection
        response_redirect = client.get(response.url)
        print(f"   Status après redirection: {response_redirect.status_code}")
        
        # Vérifier les messages
        content_redirect = response_redirect.content.decode('utf-8')
        if 'success' in content_redirect.lower() or 'succès' in content_redirect.lower():
            print("   ✅ Message de succès détecté")
        else:
            print("   ⚠️ Pas de message de succès visible")
    
    elif response.status_code == 200:
        print("   ⚠️ Formulaire retourné (possibles erreurs)")
        
        # Analyser les erreurs
        content = response.content.decode('utf-8')
        
        # Chercher les erreurs de validation
        error_patterns = [
            r'<div[^>]*alert-danger[^>]*>(.*?)</div>',
            r'<span[^>]*text-danger[^>]*>(.*?)</span>',
            r'<ul[^>]*errorlist[^>]*>(.*?)</ul>',
        ]
        
        errors_found = []
        for pattern in error_patterns:
            matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
            errors_found.extend(matches)
        
        if errors_found:
            print("   ❌ Erreurs trouvées:")
            for error in errors_found:
                clean_error = re.sub(r'<[^>]+>', '', error).strip()
                if clean_error:
                    print(f"      • {clean_error}")
        else:
            print("   ⚠️ Pas d'erreurs visibles dans le HTML")
    
    # 3. Vérifier si le stagiaire a été créé
    print("\n3️⃣ Vérification en base de données:")
    
    stagiaire_cree = Stagiaire.objects.filter(email='<EMAIL>').first()
    
    if stagiaire_cree:
        print(f"   ✅ Stagiaire créé en base: {stagiaire_cree.nom_complet}")
        print(f"      ID: {stagiaire_cree.id}")
        print(f"      Créé par: {stagiaire_cree.cree_par}")
        print(f"      Date création: {stagiaire_cree.date_creation}")
        
        # Nettoyer
        stagiaire_cree.delete()
        print("   🧹 Stagiaire supprimé")
    else:
        print("   ❌ Stagiaire non trouvé en base")
    
    # 4. Test avec un utilisateur RH
    print("\n4️⃣ Test avec un utilisateur RH:")
    
    user_rh = User.objects.filter(role='RH', is_active=True).first()
    if user_rh:
        client.force_login(user_rh)
        print(f"   Connecté en tant que: {user_rh.username} (rôle: {user_rh.role})")
        
        response = client.get('/stagiaires/add/')
        print(f"   Status page RH: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Page accessible pour RH")
            
            # Test d'ajout
            data['email'] = '<EMAIL>'
            Stagiaire.objects.filter(email='<EMAIL>').delete()
            
            response = client.post('/stagiaires/add/', data)
            print(f"   Status POST RH: {response.status_code}")
            
            stagiaire_rh = Stagiaire.objects.filter(email='<EMAIL>').first()
            if stagiaire_rh:
                print(f"   ✅ Stagiaire créé par RH: {stagiaire_rh.nom_complet}")
                stagiaire_rh.delete()
            else:
                print("   ❌ Stagiaire non créé par RH")
    else:
        print("   ⚠️ Aucun utilisateur RH trouvé")
    
    # 5. Vérifier les contraintes et validations
    print("\n5️⃣ Vérification des contraintes:")
    
    # Test avec email dupliqué
    print("   Test email dupliqué:")
    
    # Créer un stagiaire existant
    stagiaire_existant = Stagiaire.objects.create(
        nom='Existant',
        prenom='Existant',
        email='<EMAIL>',
        date_naissance=date(2000, 1, 1),
        departement='IT',
        encadrant=encadrant,
        date_debut=date.today(),
        date_fin=date.today() + timedelta(days=30),
        etablissement='Test',
        niveau_etude='Master',
        specialite='Test',
        cree_par=admin
    )
    
    # Tenter de créer un autre avec le même email
    data_duplicate = data.copy()
    data_duplicate['email'] = '<EMAIL>'
    data_duplicate['nom'] = 'Duplicate'
    
    client.force_login(admin)
    response = client.post('/stagiaires/add/', data_duplicate)
    
    if response.status_code == 200:
        print("   ✅ Formulaire retourné pour email dupliqué (validation OK)")
        
        content = response.content.decode('utf-8')
        if 'email' in content.lower() and ('existe' in content.lower() or 'unique' in content.lower()):
            print("   ✅ Message d'erreur email dupliqué détecté")
        else:
            print("   ⚠️ Message d'erreur email pas clair")
    else:
        print(f"   ⚠️ Status inattendu pour email dupliqué: {response.status_code}")
    
    # Nettoyer
    stagiaire_existant.delete()
    
    print(f"\n{'='*60}")
    print("📊 DIAGNOSTIC FINAL:")
    print("✅ L'ajout de stagiaires FONCTIONNE techniquement")
    print("✅ Les validations fonctionnent")
    print("✅ Les redirections fonctionnent")
    print("")
    print("🔍 Si vous ne voyez pas le stagiaire ajouté:")
    print("1. Vérifiez que vous êtes sur la bonne page de liste")
    print("2. Vérifiez les filtres appliqués (service, statut, etc.)")
    print("3. Rafraîchissez la page (Ctrl+F5)")
    print("4. Vérifiez les permissions d'affichage")
    print(f"{'='*60}")

if __name__ == '__main__':
    debug_ajout_interface()
