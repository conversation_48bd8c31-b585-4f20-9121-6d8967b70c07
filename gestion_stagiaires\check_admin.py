#!/usr/bin/env python
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser

# Vérifier les superusers
superusers = CustomUser.objects.filter(is_superuser=True)
print(f"Superusers trouvés: {superusers.count()}")

for user in superusers:
    print(f"- {user.username} (is_staff: {user.is_staff}, is_active: {user.is_active})")

# Créer un superuser si nécessaire
if superusers.count() == 0:
    admin = CustomUser.objects.create_superuser(
        username='admin_super',
        email='<EMAIL>',
        password='admin123',
        role='ADMIN'
    )
    print(f"Superuser créé: {admin.username}")
else:
    # S'assurer que l'admin existant est bien superuser et staff
    admin = superusers.first()
    if not admin.is_staff:
        admin.is_staff = True
        admin.save()
        print(f"is_staff activé pour {admin.username}")

print("Vérification terminée.")
