{% extends 'stagiaires/base.html' %}

{% block title %}Export des Données - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-download me-2"></i>
                        Export des Données
                    </h3>
                    <a href="{% url 'reports' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Retour aux rapports
                    </a>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Type d'export -->
                        <div class="mb-4">
                            <h5><i class="fas fa-file-export me-1"></i>Type d'export</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card border-primary">
                                        <div class="card-body text-center">
                                            <input type="radio" class="form-check-input" name="export_type" value="excel" id="excel" checked>
                                            <label for="excel" class="form-check-label d-block">
                                                <i class="fas fa-file-excel fa-3x text-success mb-2"></i>
                                                <h6>Excel (.xlsx)</h6>
                                                <small class="text-muted">Format tableur avec formules</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-secondary">
                                        <div class="card-body text-center">
                                            <input type="radio" class="form-check-input" name="export_type" value="csv" id="csv">
                                            <label for="csv" class="form-check-label d-block">
                                                <i class="fas fa-file-csv fa-3x text-info mb-2"></i>
                                                <h6>CSV (.csv)</h6>
                                                <small class="text-muted">Format texte séparé par virgules</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-danger">
                                        <div class="card-body text-center">
                                            <input type="radio" class="form-check-input" name="export_type" value="pdf" id="pdf">
                                            <label for="pdf" class="form-check-label d-block">
                                                <i class="fas fa-file-pdf fa-3x text-danger mb-2"></i>
                                                <h6>PDF (.pdf)</h6>
                                                <small class="text-muted">Document formaté pour impression</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Données à exporter -->
                        <div class="mb-4">
                            <h5><i class="fas fa-database me-1"></i>Données à exporter</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="data_types" value="stagiaires" id="stagiaires" checked>
                                        <label class="form-check-label" for="stagiaires">
                                            <i class="fas fa-users me-1"></i>Informations des stagiaires
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="data_types" value="stages" id="stages" checked>
                                        <label class="form-check-label" for="stages">
                                            <i class="fas fa-briefcase me-1"></i>Détails des stages
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="data_types" value="evaluations" id="evaluations">
                                        <label class="form-check-label" for="evaluations">
                                            <i class="fas fa-star me-1"></i>Évaluations
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="data_types" value="encadrants" id="encadrants">
                                        <label class="form-check-label" for="encadrants">
                                            <i class="fas fa-user-tie me-1"></i>Informations des encadrants
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="data_types" value="statistiques" id="statistiques">
                                        <label class="form-check-label" for="statistiques">
                                            <i class="fas fa-chart-bar me-1"></i>Statistiques générales
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="data_types" value="rapports" id="rapports">
                                        <label class="form-check-label" for="rapports">
                                            <i class="fas fa-file-alt me-1"></i>Rapports de stage
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Filtres -->
                        <div class="mb-4">
                            <h5><i class="fas fa-filter me-1"></i>Filtres</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="date_debut_filtre" class="form-label">Date de début</label>
                                        <input type="date" class="form-control" id="date_debut_filtre" name="date_debut_filtre">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="date_fin_filtre" class="form-label">Date de fin</label>
                                        <input type="date" class="form-control" id="date_fin_filtre" name="date_fin_filtre">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="departement_filtre" class="form-label">Département</label>
                                        <select class="form-control" id="departement_filtre" name="departement_filtre">
                                            <option value="">Tous les départements</option>
                                            <option value="IT">Informatique</option>
                                            <option value="MARKETING">Marketing</option>
                                            <option value="RH">Ressources Humaines</option>
                                            <option value="FINANCE">Finance</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="statut_filtre" class="form-label">Statut</label>
                                        <select class="form-control" id="statut_filtre" name="statut_filtre">
                                            <option value="">Tous les statuts</option>
                                            <option value="EN_COURS">En cours</option>
                                            <option value="TERMINE">Terminé</option>
                                            <option value="EN_ATTENTE">En attente</option>
                                            <option value="ANNULE">Annulé</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Options avancées -->
                        <div class="mb-4">
                            <h5><i class="fas fa-cogs me-1"></i>Options avancées</h5>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="include_headers" id="include_headers" checked>
                                <label class="form-check-label" for="include_headers">
                                    Inclure les en-têtes de colonnes
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="include_summary" id="include_summary">
                                <label class="form-check-label" for="include_summary">
                                    Inclure un résumé statistique
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="compress_file" id="compress_file">
                                <label class="form-check-label" for="compress_file">
                                    Compresser le fichier (ZIP)
                                </label>
                            </div>
                        </div>

                        <!-- Aperçu -->
                        <div class="mb-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-1"></i>Aperçu de l'export</h6>
                                <p class="mb-1"><strong>Format :</strong> <span id="format_preview">Excel (.xlsx)</span></p>
                                <p class="mb-1"><strong>Données :</strong> <span id="data_preview">Stagiaires, Stages</span></p>
                                <p class="mb-0"><strong>Estimation :</strong> <span id="size_preview">~2.5 MB, 150 lignes</span></p>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'reports' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>Annuler
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-primary me-2" onclick="previewExport()">
                                    <i class="fas fa-eye me-1"></i>Aperçu
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-download me-1"></i>Télécharger
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mise à jour de l'aperçu en temps réel
    function updatePreview() {
        const formatRadios = document.querySelectorAll('input[name="export_type"]');
        const dataCheckboxes = document.querySelectorAll('input[name="data_types"]:checked');
        
        let selectedFormat = '';
        formatRadios.forEach(radio => {
            if (radio.checked) {
                selectedFormat = radio.nextElementSibling.querySelector('h6').textContent;
            }
        });
        
        let selectedData = [];
        dataCheckboxes.forEach(checkbox => {
            selectedData.push(checkbox.nextElementSibling.textContent.trim());
        });
        
        document.getElementById('format_preview').textContent = selectedFormat;
        document.getElementById('data_preview').textContent = selectedData.join(', ') || 'Aucune donnée sélectionnée';
    }
    
    // Écouteurs d'événements
    document.querySelectorAll('input[name="export_type"], input[name="data_types"]').forEach(input => {
        input.addEventListener('change', updatePreview);
    });
    
    // Initialisation
    updatePreview();
});

function previewExport() {
    alert('Fonctionnalité d\'aperçu en cours de développement.');
}
</script>
{% endblock %}
