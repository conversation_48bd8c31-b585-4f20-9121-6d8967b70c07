#!/usr/bin/env python
"""
Debug du calendrier des encadrants - Pourquoi les stagiaires ne s'affichent pas
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire
from datetime import datetime, timedelta
import calendar

User = get_user_model()

def debug_calendrier_encadrant():
    """Debug du calendrier des encadrants"""
    
    print("=== DEBUG CALENDRIER ENCADRANT ===")
    
    # 1. Récupérer l'encadrant
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not encadrant:
        print("❌ Aucun encadrant trouvé")
        return
    
    print(f"✅ Encadrant trouvé: {encadrant.get_full_name()} (ID: {encadrant.id})")
    print(f"   Email: {encadrant.email}")
    print(f"   Service: {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    # 2. Récupérer TOUS les stagiaires de cet encadrant
    print(f"\n📋 TOUS les stagiaires de {encadrant.get_full_name()}:")
    
    tous_stagiaires = Stagiaire.objects.filter(encadrant=encadrant)
    print(f"   Total stagiaires: {tous_stagiaires.count()}")
    
    for stagiaire in tous_stagiaires:
        print(f"\n   📝 {stagiaire.nom_complet}")
        print(f"      ID: {stagiaire.id}")
        print(f"      Dates: {stagiaire.date_debut} → {stagiaire.date_fin}")
        print(f"      Service: {stagiaire.service.nom if stagiaire.service else 'Aucun'}")
        print(f"      Sujet: {stagiaire.sujet.titre if stagiaire.sujet else 'Aucun'}")
        print(f"      Statut: Actif")
        
        # Vérifier si le stage est en cours
        from datetime import datetime as dt
        today = dt.now().date()
        if stagiaire.date_debut <= today <= stagiaire.date_fin:
            print(f"      ✅ Stage EN COURS")
        elif stagiaire.date_debut > today:
            print(f"      ⏳ Stage FUTUR (dans {(stagiaire.date_debut - today).days} jours)")
        else:
            print(f"      ⏹️ Stage TERMINÉ (il y a {(today - stagiaire.date_fin).days} jours)")
    
    # 3. Test de la logique du calendrier pour le mois actuel
    print(f"\n📅 TEST LOGIQUE CALENDRIER MOIS ACTUEL:")
    
    today = datetime.now()
    year = today.year
    month = today.month
    
    print(f"   Année: {year}, Mois: {month} ({calendar.month_name[month]})")
    
    # Calculer les dates de début et fin pour l'affichage
    start_date = datetime(year, month, 1)
    days_in_month = calendar.monthrange(year, month)[1]
    end_date = datetime(year, month, days_in_month)
    
    print(f"   Période calendrier: {start_date.date()} → {end_date.date()}")
    
    # Appliquer la même logique que dans la vue
    stagiaires_calendrier = Stagiaire.objects.filter(
        encadrant=encadrant,
        date_debut__lte=end_date,
        date_fin__gte=start_date
    ).order_by('nom', 'prenom')
    
    print(f"\n   🔍 Stagiaires qui DEVRAIENT apparaître dans le calendrier: {stagiaires_calendrier.count()}")
    
    for stagiaire in stagiaires_calendrier:
        print(f"      ✅ {stagiaire.nom_complet}")
        print(f"         Dates: {stagiaire.date_debut} → {stagiaire.date_fin}")
        
        # Vérifier les conditions
        debut_ok = stagiaire.date_debut <= end_date.date()
        fin_ok = stagiaire.date_fin >= start_date.date()
        
        print(f"         Début <= fin_mois: {debut_ok} ({stagiaire.date_debut} <= {end_date.date()})")
        print(f"         Fin >= début_mois: {fin_ok} ({stagiaire.date_fin} >= {start_date.date()})")
    
    # 4. Test pour d'autres mois
    print(f"\n📅 TEST AUTRES MOIS:")
    
    # Mois précédent
    prev_month = month - 1 if month > 1 else 12
    prev_year = year if month > 1 else year - 1
    
    start_prev = datetime(prev_year, prev_month, 1)
    days_prev = calendar.monthrange(prev_year, prev_month)[1]
    end_prev = datetime(prev_year, prev_month, days_prev)
    
    stagiaires_prev = Stagiaire.objects.filter(
        encadrant=encadrant,
        date_debut__lte=end_prev,
        date_fin__gte=start_prev
    )
    
    print(f"   Mois précédent ({calendar.month_name[prev_month]} {prev_year}): {stagiaires_prev.count()} stagiaires")
    
    # Mois suivant
    next_month = month + 1 if month < 12 else 1
    next_year = year if month < 12 else year + 1
    
    start_next = datetime(next_year, next_month, 1)
    days_next = calendar.monthrange(next_year, next_month)[1]
    end_next = datetime(next_year, next_month, days_next)
    
    stagiaires_next = Stagiaire.objects.filter(
        encadrant=encadrant,
        date_debut__lte=end_next,
        date_fin__gte=start_next
    )
    
    print(f"   Mois suivant ({calendar.month_name[next_month]} {next_year}): {stagiaires_next.count()} stagiaires")
    
    # 5. Vérifier les données de base
    print(f"\n🔍 VÉRIFICATION DES DONNÉES:")
    
    # Vérifier que l'encadrant est bien assigné
    stagiaires_avec_encadrant = Stagiaire.objects.filter(encadrant__isnull=False)
    print(f"   Stagiaires avec encadrant: {stagiaires_avec_encadrant.count()}")
    
    stagiaires_sans_encadrant = Stagiaire.objects.filter(encadrant__isnull=True)
    print(f"   Stagiaires sans encadrant: {stagiaires_sans_encadrant.count()}")
    
    # Vérifier les encadrants
    encadrants = User.objects.filter(role='ENCADRANT', is_active=True)
    print(f"   Encadrants actifs: {encadrants.count()}")
    
    for enc in encadrants:
        nb_stagiaires = Stagiaire.objects.filter(encadrant=enc).count()
        print(f"      {enc.get_full_name()}: {nb_stagiaires} stagiaires")
    
    # 6. Test de la requête exacte de la vue
    print(f"\n🧪 TEST REQUÊTE EXACTE DE LA VUE:")
    
    # Simuler exactement ce qui se passe dans calendrier_simple_view
    from datetime import datetime
    import calendar
    
    today = datetime.now()
    year = today.year
    month = today.month
    
    start_date = datetime(year, month, 1)
    days_in_month = calendar.monthrange(year, month)[1]
    end_date = datetime(year, month, days_in_month)
    
    print(f"   Paramètres:")
    print(f"      today: {today}")
    print(f"      year: {year}")
    print(f"      month: {month}")
    print(f"      start_date: {start_date}")
    print(f"      end_date: {end_date}")
    
    # Requête exacte
    stagiaires = Stagiaire.objects.filter(
        encadrant=encadrant,
        date_debut__lte=end_date,
        date_fin__gte=start_date
    ).order_by('nom', 'prenom')
    
    print(f"\n   Requête SQL équivalente:")
    print(f"   SELECT * FROM stagiaires_stagiaire")
    print(f"   WHERE encadrant_id = {encadrant.id}")
    print(f"   AND date_debut <= '{end_date.date()}'")
    print(f"   AND date_fin >= '{start_date.date()}'")
    print(f"   ORDER BY nom, prenom")
    
    print(f"\n   Résultat: {stagiaires.count()} stagiaires")
    
    for stagiaire in stagiaires:
        print(f"      ✅ {stagiaire.nom_complet} ({stagiaire.date_debut} - {stagiaire.date_fin})")
    
    # 7. Recommandations
    print(f"\n💡 RECOMMANDATIONS:")
    
    if stagiaires_calendrier.count() == 0:
        print("   ⚠️ Aucun stagiaire ne correspond aux critères du calendrier")
        print("   📝 Solutions possibles:")
        print("      1. Vérifier les dates des stages")
        print("      2. Vérifier l'assignation des encadrants")
        print("      3. Tester avec un autre mois")
        print("      4. Vérifier que les stagiaires sont actifs")
    else:
        print("   ✅ Des stagiaires correspondent aux critères")
        print("   📝 Le problème peut venir de:")
        print("      1. L'affichage dans le template")
        print("      2. La génération des périodes")
        print("      3. Les permissions de l'utilisateur")
    
    print(f"\n{'='*60}")
    print("🔍 RÉSUMÉ DU DEBUG:")
    print(f"   Encadrant: {encadrant.get_full_name()}")
    print(f"   Total stagiaires: {tous_stagiaires.count()}")
    print(f"   Stagiaires mois actuel: {stagiaires_calendrier.count()}")
    print(f"   Période: {start_date.date()} → {end_date.date()}")
    print(f"{'='*60}")

if __name__ == '__main__':
    debug_calendrier_encadrant()
