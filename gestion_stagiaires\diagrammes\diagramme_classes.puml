@startuml Diagramme_Classes_Gestion_Stagiaires

!theme plain
skinparam classAttributeIconSize 0
skinparam classFontSize 12
skinparam packageStyle rectangle

title Diagramme de Classes - Système de Gestion des Stagiaires MEF

package "Authentification et Utilisateurs" {
    class CustomUser {
        +id: Integer
        +username: String
        +email: String
        +first_name: String
        +last_name: String
        +password: String
        +role: String {ADMIN, RH, ENCADRANT}
        +is_admin: Boolean
        +is_active: Boolean
        +date_joined: DateTime
        +last_login: DateTime
        --
        +get_full_name(): String
        +get_role_display(): String
        +has_perm(permission): Boolean
        +check_password(password): <PERSON><PERSON><PERSON>
    }

    enum RoleChoices {
        ADMIN
        RH
        ENCADRANT
    }
}

package "Organisation" {
    class Service {
        +id: Integer
        +nom: String
        +code_service: String
        +description: Text
        +actif: Boolean
        +date_creation: DateTime
        +date_modification: DateTime
        --
        +get_stagiaires_actifs(): QuerySet
        +get_encadrants(): QuerySet
        +__str__(): String
    }

    class Thematique {
        +id: Integer
        +nom: String
        +description: Text
        +actif: Boolean
        +date_creation: DateTime
        --
        +get_sujets(): QuerySet
        +__str__(): String
    }

    class Sujet {
        +id: Integer
        +titre: String
        +description: Text
        +objectifs: Text
        +competences_requises: Text
        +duree_estimee: Integer
        +difficulte: String
        +actif: Boolean
        +date_creation: DateTime
        --
        +get_stagiaires_assignes(): QuerySet
        +__str__(): String
    }

    class DureeEstimee {
        +id: Integer
        +nom: String
        +duree_jours: Integer
        +description: Text
        --
        +__str__(): String
    }
}

package "Gestion des Stagiaires" {
    class Stagiaire {
        +id: Integer
        +nom: String
        +prenom: String
        +email: String
        +telephone: String
        +date_naissance: Date
        +lieu_naissance: String
        +adresse: Text
        +departement: String
        +etablissement: String
        +niveau_etude: String
        +specialite: String
        +date_debut: Date
        +date_fin: Date
        +statut: String {EN_COURS, TERMINE, SUSPENDU, ANNULE}
        +cv: FileField
        +convention_stage: FileField
        +assurance: FileField
        +date_creation: DateTime
        +date_modification: DateTime
        --
        +nom_complet(): String
        +duree_stage(): Integer
        +jours_restants(): Integer
        +get_statut_display(): String
        +get_taches_actives(): QuerySet
        +get_missions(): QuerySet
        +__str__(): String
    }

    enum StatutStage {
        EN_COURS
        TERMINE
        SUSPENDU
        ANNULE
    }

    enum DepartementChoices {
        IT
        FINANCE
        RH
        JURIDIQUE
        COMMUNICATION
    }
}

package "Gestion des Tâches" {
    class Tache {
        +id: Integer
        +titre: String
        +description: Text
        +priorite: String {HAUTE, NORMALE, BASSE}
        +statut: String {A_FAIRE, EN_COURS, TERMINEE, ANNULEE}
        +date_creation: DateTime
        +date_debut: DateTime
        +date_fin_prevue: Date
        +date_fin_reelle: DateTime
        +commentaires: Text
        +progression: Integer
        --
        +get_priorite_display(): String
        +get_statut_display(): String
        +est_en_retard(): Boolean
        +duree_realisee(): Integer
        +__str__(): String
    }

    enum PrioriteTache {
        HAUTE
        NORMALE
        BASSE
    }

    enum StatutTache {
        A_FAIRE
        EN_COURS
        TERMINEE
        ANNULEE
    }

    class TacheStage {
        +id: Integer
        +nom: String
        +description: Text
        +date_debut: Date
        +date_fin: Date
        +statut: String
        +commentaires: Text
        --
        +__str__(): String
    }
}

package "Missions et Projets" {
    class Mission {
        +id: Integer
        +titre: String
        +description: Text
        +objectifs: Text
        +date_debut: Date
        +date_fin_prevue: Date
        +date_fin_reelle: Date
        +statut: String {PLANIFIEE, EN_COURS, TERMINEE, ANNULEE}
        +priorite: String
        +budget: Decimal
        +resultats_attendus: Text
        +resultats_obtenus: Text
        +date_creation: DateTime
        --
        +get_statut_display(): String
        +get_progression(): Integer
        +est_terminee(): Boolean
        +__str__(): String
    }

    enum StatutMission {
        PLANIFIEE
        EN_COURS
        TERMINEE
        ANNULEE
    }
}

package "Documents et Contrats" {
    class ContratStage {
        +id: Integer
        +numero_contrat: String
        +date_creation: DateTime
        +date_signature_stagiaire: DateTime
        +date_signature_encadrant: DateTime
        +date_signature_rh: DateTime
        +statut: String {BROUILLON, EN_ATTENTE, SIGNE, ARCHIVE}
        +contenu: Text
        +fichier_pdf: FileField
        +commentaires: Text
        --
        +est_signe(): Boolean
        +get_statut_display(): String
        +generer_pdf(): FileField
        +__str__(): String
    }

    enum StatutContrat {
        BROUILLON
        EN_ATTENTE
        SIGNE
        ARCHIVE
    }

    class RapportStage {
        +id: Integer
        +titre: String
        +contenu: Text
        +fichier_rapport: FileField
        +date_soumission: DateTime
        +date_validation: DateTime
        +statut: String {BROUILLON, SOUMIS, VALIDE, REJETE}
        +commentaires_encadrant: Text
        +note: Integer
        --
        +est_valide(): Boolean
        +get_statut_display(): String
        +__str__(): String
    }

    enum StatutRapport {
        BROUILLON
        SOUMIS
        VALIDE
        REJETE
    }
}

' Relations entre les classes

' Relations CustomUser
CustomUser ||--o{ Service : appartient_à
CustomUser ||--o{ Stagiaire : encadre
CustomUser ||--o{ Tache : crée
CustomUser ||--o{ Mission : supervise
CustomUser ||--o{ ContratStage : signe

' Relations Service
Service ||--o{ Stagiaire : accueille
Service ||--o{ Thematique : propose
Service ||--o{ CustomUser : emploie

' Relations Stagiaire (entité centrale)
Stagiaire ||--o{ Tache : a_assignée
Stagiaire ||--o{ Mission : participe_à
Stagiaire ||--o{ TacheStage : effectue
Stagiaire ||--o{ ContratStage : signe
Stagiaire ||--o{ RapportStage : rédige
Stagiaire }o--|| Service : appartient_à
Stagiaire }o--|| CustomUser : encadré_par
Stagiaire }o--o| Sujet : travaille_sur
Stagiaire }o--o| DureeEstimee : a_durée

' Relations Thématiques et Sujets
Thematique ||--o{ Sujet : contient
Service ||--o{ Thematique : gère

' Relations Tâches et Missions
Mission ||--o{ Tache : contient
Tache }o--|| CustomUser : créée_par
Tache }o--|| Stagiaire : assignée_à

' Relations Documents
ContratStage }o--|| Stagiaire : concerne
RapportStage }o--|| Stagiaire : rédigé_par
RapportStage }o--o| Mission : documente

' Notes explicatives
note top of CustomUser : Modèle utilisateur personnalisé\navec gestion des rôles RBAC
note top of Stagiaire : Entité centrale du système\ncontenant toutes les informations\ndu stagiaire et ses documents
note top of Service : Organisation en services\npour la gestion par département
note top of Tache : Système de tâches avec\npriorités et suivi temporel
note bottom of Mission : Missions complexes\npouvant contenir plusieurs tâches

@enduml
