{% extends 'stagiaires/base.html' %}
{% load custom_filters %}

{% block title %}Rapports de {{ stagiaire.nom_complet }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            Rapports de {{ stagiaire.nom_complet }}
                        </h4>
                        <div>
                            <a href="{% url 'soumettre_rapport' stagiaire.id %}" class="btn btn-light">
                                <i class="fas fa-plus me-1"></i>Soumettre un rapport
                            </a>
                            <a href="{% url 'stagiaires_list' %}" class="btn btn-outline-light ms-2">
                                <i class="fas fa-arrow-left me-1"></i>Retour
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Informations du stagiaire -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info">Informations du stagiaire</h6>
                                    <p class="mb-1"><strong>Nom :</strong> {{ stagiaire.nom_complet }}</p>
                                    <p class="mb-1"><strong>Email :</strong> {{ stagiaire.email }}</p>
                                    <p class="mb-1"><strong>Période :</strong> {{ stagiaire.date_debut|date:"d/m/Y" }} - {{ stagiaire.date_fin|date:"d/m/Y" }}</p>
                                    <p class="mb-0"><strong>Encadrant :</strong> {{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h6 class="card-title text-success">Statistiques des rapports</h6>
                                    <div class="row text-center">
                                        <div class="col-3">
                                            <div class="text-primary">
                                                <h4>{{ total_rapports }}</h4>
                                                <small>Total</small>
                                            </div>
                                        </div>
                                        <div class="col-3">
                                            <div class="text-success">
                                                <h4>{{ rapports_valides }}</h4>
                                                <small>Validés</small>
                                            </div>
                                        </div>
                                        <div class="col-3">
                                            <div class="text-warning">
                                                <h4>{{ rapports_en_attente }}</h4>
                                                <small>En attente</small>
                                            </div>
                                        </div>
                                        <div class="col-3">
                                            <div class="text-danger">
                                                <h4>{{ rapports_rejetes }}</h4>
                                                <small>Rejetés</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filtres et recherche -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchRapports" placeholder="Rechercher un rapport...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterStatut">
                                <option value="">Tous les statuts</option>
                                <option value="BROUILLON">Brouillon</option>
                                <option value="SOUMIS">Soumis</option>
                                <option value="EN_REVISION">En révision</option>
                                <option value="VALIDE">Validé</option>
                                <option value="REJETE">Rejeté</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterMission">
                                <option value="">Toutes les missions</option>
                                {% for rapport in rapports %}
                                    {% if rapport.mission %}
                                        <option value="{{ rapport.mission.id }}">{{ rapport.mission.titre }}</option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- Liste des rapports -->
                    {% if rapports %}
                    <div class="table-responsive">
                        <table class="table table-hover" id="rapportsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Titre</th>
                                    <th>Mission liée</th>
                                    <th>Statut</th>
                                    <th>Date de soumission</th>
                                    <th>Validation</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rapport in rapports %}
                                <tr data-statut="{{ rapport.statut }}" data-mission="{{ rapport.mission.id|default:'' }}">
                                    <td>
                                        <div>
                                            <strong>{{ rapport.titre }}</strong>
                                            <br>
                                            <small class="text-muted">{{ rapport.description|truncatechars:80 }}</small>
                                            <br>
                                            <small class="text-info">
                                                <i class="fas fa-file me-1"></i>
                                                <a href="{{ rapport.fichier_rapport.url }}" target="_blank">{{ rapport.fichier_rapport.name|basename }}</a>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        {% if rapport.mission %}
                                            <span class="badge bg-info">{{ rapport.mission.titre }}</span>
                                        {% else %}
                                            <span class="text-muted">Aucune mission</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if rapport.statut == 'BROUILLON' %}
                                            <span class="badge bg-secondary">Brouillon</span>
                                        {% elif rapport.statut == 'SOUMIS' %}
                                            <span class="badge bg-warning">Soumis</span>
                                        {% elif rapport.statut == 'EN_REVISION' %}
                                            <span class="badge bg-info">En révision</span>
                                        {% elif rapport.statut == 'VALIDE' %}
                                            <span class="badge bg-success">Validé</span>
                                        {% elif rapport.statut == 'REJETE' %}
                                            <span class="badge bg-danger">Rejeté</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if rapport.date_soumission %}
                                            {{ rapport.date_soumission|date:"d/m/Y H:i" }}
                                        {% else %}
                                            <span class="text-muted">Non soumis</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if rapport.valide_par %}
                                            <div>
                                                <strong>{{ rapport.valide_par.get_full_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ rapport.date_validation|date:"d/m/Y H:i" }}</small>
                                                {% if rapport.note_rapport %}
                                                    <br>
                                                    <span class="badge bg-primary">Note: {{ rapport.note_rapport }}/20</span>
                                                {% endif %}
                                            </div>
                                        {% else %}
                                            <span class="text-muted">En attente</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ rapport.fichier_rapport.url }}" target="_blank" class="btn btn-sm btn-outline-info" title="Télécharger">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            {% if user.role == 'ENCADRANT' or user.role == 'RH' or user.role == 'ADMIN' %}
                                                {% if rapport.statut == 'SOUMIS' or rapport.statut == 'EN_REVISION' %}
                                                <a href="{% url 'valider_rapport' rapport.id %}" class="btn btn-sm btn-outline-success" title="Valider">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                                {% endif %}
                                            {% endif %}
                                            <button class="btn btn-sm btn-outline-primary" onclick="showRapportDetails({{ rapport.id }})" title="Détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun rapport soumis</h5>
                        <p class="text-muted">Commencez par soumettre un rapport pour ce stagiaire.</p>
                        <a href="{% url 'soumettre_rapport' stagiaire.id %}" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>Soumettre le premier rapport
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour les détails de rapport -->
<div class="modal fade" id="rapportDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails du rapport</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="rapportDetailsContent">
                <!-- Contenu chargé dynamiquement -->
            </div>
        </div>
    </div>
</div>

<script>
// Recherche et filtrage
document.getElementById('searchRapports').addEventListener('input', filterRapports);
document.getElementById('filterStatut').addEventListener('change', filterRapports);
document.getElementById('filterMission').addEventListener('change', filterRapports);

function filterRapports() {
    const searchTerm = document.getElementById('searchRapports').value.toLowerCase();
    const statutFilter = document.getElementById('filterStatut').value;
    const missionFilter = document.getElementById('filterMission').value;
    const rows = document.querySelectorAll('#rapportsTable tbody tr');

    rows.forEach(row => {
        const titre = row.cells[0].textContent.toLowerCase();
        const statut = row.getAttribute('data-statut');
        const mission = row.getAttribute('data-mission');

        const matchesSearch = titre.includes(searchTerm);
        const matchesStatut = !statutFilter || statut === statutFilter;
        const matchesMission = !missionFilter || mission === missionFilter;

        row.style.display = matchesSearch && matchesStatut && matchesMission ? '' : 'none';
    });
}

// Affichage des détails de rapport
function showRapportDetails(rapportId) {
    // Ici vous pouvez ajouter une requête AJAX pour charger les détails
    // Pour l'instant, on affiche un message simple
    document.getElementById('rapportDetailsContent').innerHTML = `
        <p>Chargement des détails du rapport ${rapportId}...</p>
        <p>Cette fonctionnalité peut être étendue avec une requête AJAX pour charger les détails complets.</p>
    `;
    new bootstrap.Modal(document.getElementById('rapportDetailsModal')).show();
}

// Fonction pour extraire le nom de fichier
document.addEventListener('DOMContentLoaded', function() {
    // Cette fonction est déjà gérée par le template filter |basename
});
</script>
{% endblock %}
