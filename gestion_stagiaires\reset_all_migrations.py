#!/usr/bin/env python
import os
import django
import shutil
from pathlib import Path

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.db import connection
from django.conf import settings

print("=== RÉINITIALISATION COMPLÈTE DES MIGRATIONS ===")

# 1. Sauvegarde de la base de données
print("\n1. Sauvegarde des données importantes...")
# Cette étape est optionnelle mais recommandée
# Vous pouvez utiliser dumpdata pour sauvegarder vos données

# 2. Supprimer toutes les entrées de migration de la base de données
print("\n2. Suppression de toutes les entrées de migration de la base de données...")
with connection.cursor() as cursor:
    cursor.execute("DELETE FROM django_migrations")
    print("✓ Toutes les entrées de migration ont été supprimées.")

# 3. Sauvegarder et supprimer les fichiers de migration existants
print("\n3. Sauvegarde et suppression des fichiers de migration existants...")

# Liste des applications à traiter
apps = ['stagiaires', 'admin', 'auth', 'contenttypes', 'sessions']

for app in apps:
    # Déterminer le chemin des migrations
    if app in ['admin', 'auth', 'contenttypes', 'sessions']:
        # Applications Django intégrées
        migrations_dir = Path(django.__path__[0]) / app / 'migrations'
    else:
        # Applications du projet
        migrations_dir = Path(settings.BASE_DIR) / app / 'migrations'
    
    if not migrations_dir.exists():
        print(f"   ⚠️ Dossier de migrations introuvable pour {app}: {migrations_dir}")
        continue
    
    # Créer un dossier de sauvegarde
    backup_dir = migrations_dir / 'backup'
    backup_dir.mkdir(exist_ok=True)
    
    # Sauvegarder et supprimer les fichiers de migration (sauf __init__.py)
    migration_files = [f for f in migrations_dir.glob('*.py') if f.name != '__init__.py']
    for file in migration_files:
        try:
            # Pour les applications Django intégrées, on ne supprime pas les fichiers
            if app in ['admin', 'auth', 'contenttypes', 'sessions']:
                print(f"   ℹ️ Application intégrée {app}: {file.name} (non supprimé)")
            else:
                # Copier dans la sauvegarde puis supprimer
                shutil.copy(file, backup_dir / file.name)
                file.unlink()
                print(f"   ✓ Fichier sauvegardé et supprimé: {file.name}")
        except Exception as e:
            print(f"   ❌ Erreur lors du traitement de {file}: {e}")

# 4. Créer de nouvelles migrations initiales pour les applications du projet
print("\n4. Création de nouvelles migrations initiales...")
for app in ['stagiaires']:
    try:
        os.system(f"python manage.py makemigrations {app}")
        print(f"   ✓ Nouvelles migrations créées pour {app}")
    except Exception as e:
        print(f"   ❌ Erreur lors de la création des migrations pour {app}: {e}")

# 5. Appliquer les migrations avec --fake-initial
print("\n5. Application des migrations avec --fake-initial...")
try:
    os.system("python manage.py migrate --fake-initial")
    print("   ✓ Migrations appliquées avec succès")
except Exception as e:
    print(f"   ❌ Erreur lors de l'application des migrations: {e}")

print("\n=== RÉINITIALISATION TERMINÉE ===")
print("\nVous pouvez maintenant vérifier l'état des migrations avec:")
print("python manage.py showmigrations")
print("\nEt démarrer le serveur avec:")
print("python manage.py runserver")