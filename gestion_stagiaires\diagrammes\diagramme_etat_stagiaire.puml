@startuml Diagramme_Etat_Stagiaire

!theme plain
skinparam state {
  BackgroundColor LightBlue
  BorderColor DarkBlue
  ArrowColor Black
}

title Diagramme d'États - Cycle de Vie d'un Stagiaire

[*] --> CANDIDATURE : Demande de stage reçue

state CANDIDATURE {
  CANDIDATURE : Documents en attente
  CANDIDATURE : CV, Convention, Assurance
  CANDIDATURE : Validation RH requise
}

CANDIDATURE --> ACCEPTE : Documents validés\npar RH
CANDIDATURE --> REFUSE : Documents\nincomplete/invalides
CANDIDATURE --> CANDIDATURE : Demande de\ncorrections

state ACCEPTE {
  ACCEPTE : Stagiaire accepté
  ACCEPTE : Service assigné
  ACCEPTE : Encadrant assigné
  ACCEPTE : En attente de début
}

ACCEPTE --> EN_COURS : Date de début\natteinte
ACCEPTE --> ANNULE : Annulation avant\ndébut de stage

state EN_COURS {
  EN_COURS : Stage en cours
  EN_COURS : Tâches assignées
  EN_COURS : Suivi régulier
  EN_COURS : Rencontres encadrant
  
  state "Suivi Période" as SuiviPeriode {
    state VERT : Plus de 10 jours restants
    state ORANGE : Exactement 10 jours restants  
    state ROUGE : Moins de 10 jours restants
    
    VERT --> ORANGE : Approche de\nla fin (10 jours)
    ORANGE --> ROUGE : Moins de\n10 jours restants
    ROUGE --> ROUGE : Countdown\njusqu'à la fin
  }
  
  state "Gestion Tâches" as GestionTaches {
    state A_FAIRE : Tâches assignées
    state EN_COURS_TACHE : Tâches en cours
    state TERMINEE : Tâches terminées
    
    A_FAIRE --> EN_COURS_TACHE : Démarrage tâche
    EN_COURS_TACHE --> TERMINEE : Validation encadrant
    TERMINEE --> A_FAIRE : Nouvelles tâches
  }
}

EN_COURS --> SUSPENDU : Suspension\ntemporaire
EN_COURS --> TERMINE : Fin normale\ndu stage
EN_COURS --> ANNULE : Annulation\nen cours de stage

state SUSPENDU {
  SUSPENDU : Stage suspendu
  SUSPENDU : Raison documentée
  SUSPENDU : Possibilité de reprise
}

SUSPENDU --> EN_COURS : Reprise du stage
SUSPENDU --> ANNULE : Annulation\ndéfinitive

state TERMINE {
  TERMINE : Stage terminé
  TERMINE : Rapport soumis
  TERMINE : Évaluation finale
  TERMINE : Attestation générée
}

state ANNULE {
  ANNULE : Stage annulé
  ANNULE : Raison documentée
  ANNULE : Dossier archivé
}

state REFUSE {
  REFUSE : Candidature refusée
  REFUSE : Documents insuffisants
  REFUSE : Critères non remplis
}

TERMINE --> ARCHIVE : Archivage\ncomplet du dossier
ANNULE --> ARCHIVE : Archivage\ndossier annulé
REFUSE --> ARCHIVE : Archivage\ncandidature refusée

state ARCHIVE {
  ARCHIVE : Dossier archivé
  ARCHIVE : Données conservées
  ARCHIVE : Consultation historique
}

ARCHIVE --> [*] : Fin du cycle

' Transitions d'urgence
EN_COURS --> TERMINE : Fin anticipée\n(accord mutuel)
ACCEPTE --> TERMINE : Annulation\navec compensation

' Notes explicatives
note right of EN_COURS : État principal avec\nsous-états pour le suivi\ndes tâches et de la période

note right of SuiviPeriode : Indicateurs visuels :\n🟢 VERT : > 10 jours\n🟠 ORANGE : = 10 jours\n🔴 ROUGE : < 10 jours

note right of TERMINE : État final normal\navec génération automatique\nde l'attestation

note bottom : Les transitions sont déclenchées\npar les actions des utilisateurs\n(RH, Encadrant) ou par le système\n(dates, échéances)

@enduml
