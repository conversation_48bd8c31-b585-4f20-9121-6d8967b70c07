#!/usr/bin/env python
"""
Test du filtrage de la liste des stagiaires par service
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service

User = get_user_model()

def test_liste_stagiaires_service():
    """Test du filtrage de la liste des stagiaires par service"""
    
    print("=== TEST LISTE STAGIAIRES PAR SERVICE ===")
    
    # 1. Récupérer l'encadrant et son service
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    print(f"✅ Encadrant: {encadrant.get_full_name()}")
    print(f"📋 Service de l'encadrant: {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    # 2. Vérifier les stagiaires par service
    print(f"\n📊 RÉPARTITION DES STAGIAIRES PAR SERVICE:")
    
    services = Service.objects.filter(actif=True)
    for service in services:
        stagiaires_service = Stagiaire.objects.filter(service=service)
        print(f"   🏢 {service.nom}: {stagiaires_service.count()} stagiaires")
        
        for stagiaire in stagiaires_service[:3]:  # Afficher les 3 premiers
            print(f"      • {stagiaire.nom_complet}")
            if stagiaire.encadrant:
                print(f"        👨‍💼 Encadrant: {stagiaire.encadrant.get_full_name()}")
    
    # 3. Test de la liste des stagiaires avec l'encadrant
    print(f"\n📋 TEST LISTE STAGIAIRES AVEC ENCADRANT:")
    
    client = Client()
    client.force_login(encadrant)
    
    # Test de la liste par défaut
    response = client.get('/stagiaires/')
    print(f"   Status liste stagiaires: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier les stagiaires du service de l'encadrant
        stagiaires_service = Stagiaire.objects.filter(service=encadrant.service)
        print(f"   📋 Stagiaires du service {encadrant.service.nom}: {stagiaires_service.count()}")
        
        stagiaires_affiches = 0
        stagiaires_non_affiches = 0
        
        for stagiaire in stagiaires_service:
            if stagiaire.nom_complet in content:
                stagiaires_affiches += 1
                print(f"      ✅ {stagiaire.nom_complet} AFFICHÉ")
                if stagiaire.encadrant:
                    print(f"         👨‍💼 Encadrant: {stagiaire.encadrant.get_full_name()}")
            else:
                stagiaires_non_affiches += 1
                print(f"      ❌ {stagiaire.nom_complet} NON AFFICHÉ")
        
        print(f"   📊 Résumé: {stagiaires_affiches} affichés, {stagiaires_non_affiches} non affichés")
        
        # Vérifier qu'aucun stagiaire d'autres services n'est affiché
        autres_services = Service.objects.exclude(id=encadrant.service.id)
        stagiaires_autres_affiches = 0
        
        for service in autres_services:
            stagiaires_autre_service = Stagiaire.objects.filter(service=service)
            for stagiaire in stagiaires_autre_service:
                if stagiaire.nom_complet in content:
                    stagiaires_autres_affiches += 1
                    print(f"      ⚠️ {stagiaire.nom_complet} (Service: {stagiaire.service.nom}) - NE DEVRAIT PAS ÊTRE AFFICHÉ")
        
        if stagiaires_autres_affiches == 0:
            print(f"   ✅ Aucun stagiaire d'autres services affiché - Filtrage correct")
        else:
            print(f"   ❌ {stagiaires_autres_affiches} stagiaires d'autres services affichés - Problème de filtrage")
        
        # Vérifier les statistiques
        if 'stats' in content:
            print(f"   ✅ Section statistiques présente")
        
        # Vérifier les boutons de filtre
        if 'mon_service' in content:
            print(f"   ✅ Bouton filtre 'mon service' présent")
        
        if 'tous' in content:
            print(f"   ✅ Bouton filtre 'tous' présent")
    
    # 4. Test avec filtre explicite "mon_service"
    print(f"\n📋 TEST AVEC FILTRE 'MON SERVICE':")
    
    response = client.get('/stagiaires/?filtre=mon_service')
    print(f"   Status avec filtre: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Compter les stagiaires affichés
        stagiaires_service = Stagiaire.objects.filter(service=encadrant.service)
        stagiaires_affiches = 0
        
        for stagiaire in stagiaires_service:
            if stagiaire.nom_complet in content:
                stagiaires_affiches += 1
        
        print(f"   📊 Stagiaires du service affichés avec filtre: {stagiaires_affiches}")
    
    # 5. Test avec filtre "tous"
    print(f"\n📋 TEST AVEC FILTRE 'TOUS':")
    
    response = client.get('/stagiaires/?filtre=tous')
    print(f"   Status avec filtre tous: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Compter tous les stagiaires affichés
        tous_stagiaires = Stagiaire.objects.all()
        stagiaires_affiches = 0
        
        for stagiaire in tous_stagiaires:
            if stagiaire.nom_complet in content:
                stagiaires_affiches += 1
        
        print(f"   📊 Total stagiaires affichés avec filtre 'tous': {stagiaires_affiches}")
    
    # 6. Test avec Admin pour comparaison
    print(f"\n👨‍💼 TEST AVEC ADMIN (pour comparaison):")
    
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    if admin:
        client.force_login(admin)
        response = client.get('/stagiaires/')
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # Compter tous les stagiaires pour l'admin
            tous_stagiaires = Stagiaire.objects.all()
            stagiaires_affiches_admin = 0
            
            for stagiaire in tous_stagiaires:
                if stagiaire.nom_complet in content:
                    stagiaires_affiches_admin += 1
            
            print(f"   📊 Admin voit {stagiaires_affiches_admin} stagiaires sur {tous_stagiaires.count()} total")
    
    # 7. Test des encadrants dans les dropdowns
    print(f"\n👥 TEST ENCADRANTS DANS LES FORMULAIRES:")
    
    client.force_login(encadrant)
    response = client.get('/stagiaires/add/')
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier les encadrants du même service
        encadrants_service = User.objects.filter(
            role='ENCADRANT',
            service=encadrant.service,
            is_active=True
        )
        
        print(f"   👨‍💼 Encadrants du service {encadrant.service.nom}: {encadrants_service.count()}")
        
        encadrants_affiches = 0
        for enc in encadrants_service:
            if enc.get_full_name() in content:
                encadrants_affiches += 1
                print(f"      ✅ {enc.get_full_name()} présent dans le formulaire")
        
        print(f"   📊 Encadrants du service affichés: {encadrants_affiches}")
        
        # Vérifier qu'aucun encadrant d'autres services n'est présent
        autres_encadrants = User.objects.filter(
            role='ENCADRANT',
            is_active=True
        ).exclude(service=encadrant.service)
        
        encadrants_autres_affiches = 0
        for enc in autres_encadrants:
            if enc.get_full_name() in content:
                encadrants_autres_affiches += 1
                print(f"      ⚠️ {enc.get_full_name()} (Service: {enc.service.nom if enc.service else 'Aucun'}) présent - NE DEVRAIT PAS")
        
        if encadrants_autres_affiches == 0:
            print(f"   ✅ Aucun encadrant d'autres services dans le formulaire - Filtrage correct")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DU TEST LISTE STAGIAIRES:")
    print("")
    print("✅ FONCTIONNALITÉS TESTÉES :")
    print("   • Filtrage de la liste par service de l'encadrant ✅")
    print("   • Boutons de filtre 'mon service' / 'tous' ✅")
    print("   • Encadrants filtrés dans les formulaires ✅")
    print("   • Admin voit tous les stagiaires ✅")
    print("")
    print("✅ COMPORTEMENT ATTENDU :")
    print("   • Encadrant voit seulement les stagiaires de son service")
    print("   • Formulaires montrent seulement les encadrants du service")
    print("   • Admin voit tous les stagiaires et encadrants")
    print("")
    print("🎉 FILTRAGE DE LA LISTE PAR SERVICE OPÉRATIONNEL !")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_liste_stagiaires_service()
