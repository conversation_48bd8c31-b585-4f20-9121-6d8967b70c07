{% extends 'stagiaires/base.html' %}

{% block title %}Gestion des Conventions{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-file-contract me-2"></i>
                        Gestion des Conventions de Stage
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Statistiques améliorées -->
                   
                    <!-- Alerte pour conventions en attente -->
                    {% if stagiaires %}
                        {% for stagiaire in stagiaires %}
                            {% if stagiaire.statut_convention == 'EN_ATTENTE' and stagiaire.convention_stage %}
                                {% if forloop.first %}
                                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Attention !</strong> Vous avez des conventions de stage en attente de validation.
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                                {% endif %}
                            {% endif %}
                        {% endfor %}
                    {% endif %}

                    <!-- Filtres -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="Rechercher un stagiaire...">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select class="form-control" id="statusFilter">
                                <option value="">Tous les statuts</option>
                                <option value="EN_ATTENTE">⏳ En attente</option>
                                <option value="VALIDEE">✅ Validée</option>
                                <option value="REJETEE">❌ Rejetée</option>
                                <option value="MODIFIEE">⚠️ À modifier</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-outline-warning" id="showPendingOnly">
                                <i class="fas fa-clock me-1"></i>Afficher seulement les conventions en attente
                            </button>
                        </div>
                    </div>

                    <!-- Liste des conventions -->
                    {% if stagiaires %}
                    <div class="table-responsive">
                        <table class="table table-hover" id="conventionsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Stagiaire</th>
                                    <th>Département</th>
                                    <th>Encadrant</th>
                                    <th>Période</th>
                                    <th>Statut</th>
                                    <th>Date upload</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stagiaire in stagiaires %}
                                <tr data-status="{{ stagiaire.statut_convention }}" data-name="{{ stagiaire.nom_complet|lower }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-primary text-white me-2">
                                                {{ stagiaire.prenom.0 }}{{ stagiaire.nom.0 }}
                                            </div>
                                            <div>
                                                <strong>{{ stagiaire.nom_complet }}</strong><br>
                                                <small class="text-muted">{{ stagiaire.email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ stagiaire.get_departement_display }}</span>
                                    </td>
                                    <td>
                                        {% if stagiaire.encadrant %}
                                            {{ stagiaire.encadrant.get_full_name }}
                                        {% else %}
                                            <span class="text-muted">Non assigné</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>
                                            {{ stagiaire.date_debut|date:"d/m/Y" }}<br>
                                            {{ stagiaire.date_fin|date:"d/m/Y" }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if stagiaire.statut_convention == 'VALIDEE' %}success{% elif stagiaire.statut_convention == 'REJETEE' %}danger{% elif stagiaire.statut_convention == 'MODIFIEE' %}warning{% else %}secondary{% endif %}">
                                            {{ stagiaire.get_statut_convention_display }}
                                        </span>
                                        {% if stagiaire.date_validation_convention %}
                                            <br><small class="text-muted">{{ stagiaire.date_validation_convention|date:"d/m/Y" }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ stagiaire.date_creation|date:"d/m/Y H:i" }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            {% if stagiaire.convention_stage %}
                                                <a href="{% url 'convention_detail' stagiaire.id %}"
                                                   class="btn btn-sm btn-info" title="Détails complets">
                                                    <i class="fas fa-info-circle"></i>
                                                </a>
                                                <a href="{{ stagiaire.convention_stage.url }}" target="_blank"
                                                   class="btn btn-sm btn-outline-primary" title="Voir le document">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if stagiaire.statut_convention == 'EN_ATTENTE' %}
                                                    <a href="{% url 'convention_validation' stagiaire.id %}"
                                                       class="btn btn-sm btn-warning pulse-animation" title="⚡ Valider maintenant">
                                                        <i class="fas fa-signature"></i>
                                                    </a>
                                                {% elif stagiaire.statut_convention == 'VALIDEE' %}
                                                    {% if stagiaire.contrats.exists %}
                                                        <a href="{% url 'contrat_detail' stagiaire.contrats.first.id %}"
                                                           class="btn btn-sm btn-info" title="📋 Voir le contrat">
                                                            <i class="fas fa-file-contract"></i>
                                                        </a>
                                                    {% else %}
                                                        <a href="{% url 'contrat_create' stagiaire.id %}"
                                                           class="btn btn-sm btn-primary pulse-animation" title="📋 Créer le contrat">
                                                            <i class="fas fa-plus"></i>
                                                        </a>
                                                    {% endif %}
                                                    <button class="btn btn-sm btn-success" disabled title="✅ Convention validée">
                                                        <i class="fas fa-check-circle"></i>
                                                    </button>
                                                {% elif stagiaire.statut_convention == 'REJETEE' %}
                                                    <a href="{% url 'convention_validation' stagiaire.id %}"
                                                       class="btn btn-sm btn-danger" title="Revoir la décision">
                                                        <i class="fas fa-redo"></i>
                                                    </a>
                                                {% else %}
                                                    <a href="{% url 'convention_validation' stagiaire.id %}"
                                                       class="btn btn-sm btn-warning" title="Valider">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge bg-secondary">Aucune convention</span>
                                            {% endif %}
                                            <a href="{% url 'convention_upload' stagiaire.id %}"
                                               class="btn btn-sm btn-success" title="Upload/Remplacer">
                                                <i class="fas fa-upload"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune convention trouvée</h5>
                        <p class="text-muted">Les conventions uploadées apparaîtront ici.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

/* Animation pour les boutons de validation en attente */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

/* Mise en évidence des lignes en attente */
tr[data-status="EN_ATTENTE"] {
    background-color: rgba(255, 193, 7, 0.1);
    border-left: 4px solid #ffc107;
}

/* Style pour les conventions validées */
tr[data-status="VALIDEE"] {
    background-color: rgba(25, 135, 84, 0.05);
}

/* Style pour les conventions rejetées */
tr[data-status="REJETEE"] {
    background-color: rgba(220, 53, 69, 0.05);
}

/* Amélioration des badges */
.badge {
    font-size: 0.75em;
    padding: 0.375rem 0.75rem;
}

/* Bouton de filtre actif */
.filter-active {
    background-color: #ffc107 !important;
    color: #000 !important;
    border-color: #ffc107 !important;
}
</style>

<script>
// Filtrage et recherche
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const showPendingBtn = document.getElementById('showPendingOnly');
    const table = document.getElementById('conventionsTable');
    let showPendingOnly = false;

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;
        const rows = table.querySelectorAll('tbody tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const name = row.dataset.name;
            const status = row.dataset.status;

            const matchesSearch = name.includes(searchTerm);
            const matchesStatus = !statusValue || status === statusValue;
            const matchesPending = !showPendingOnly || status === 'EN_ATTENTE';

            const shouldShow = matchesSearch && matchesStatus && matchesPending;
            row.style.display = shouldShow ? '' : 'none';

            if (shouldShow) visibleCount++;
        });

        // Mettre à jour le compteur
        updateVisibleCount(visibleCount);
    }

    function updateVisibleCount(count) {
        let countElement = document.getElementById('visibleCount');
        if (!countElement) {
            countElement = document.createElement('small');
            countElement.id = 'visibleCount';
            countElement.className = 'text-muted ms-2';
            document.querySelector('.card-title').appendChild(countElement);
        }
        countElement.textContent = `(${count} affichée${count > 1 ? 's' : ''})`;
    }

    // Bouton pour afficher seulement les conventions en attente
    showPendingBtn.addEventListener('click', function() {
        showPendingOnly = !showPendingOnly;

        if (showPendingOnly) {
            this.classList.add('filter-active');
            this.innerHTML = '<i class="fas fa-clock me-1"></i>Afficher toutes les conventions';
            statusFilter.value = 'EN_ATTENTE';
        } else {
            this.classList.remove('filter-active');
            this.innerHTML = '<i class="fas fa-clock me-1"></i>Afficher seulement les conventions en attente';
            statusFilter.value = '';
        }

        filterTable();
    });

    searchInput.addEventListener('input', filterTable);
    statusFilter.addEventListener('change', filterTable);

    // Initialiser le compteur
    filterTable();

    // Notification pour les conventions en attente
    const pendingRows = table.querySelectorAll('tbody tr[data-status="EN_ATTENTE"]');
    if (pendingRows.length > 0) {
        console.log(`🔔 ${pendingRows.length} convention(s) en attente de validation`);

        // Ajouter un badge de notification dans le titre
        const title = document.querySelector('.card-title');
        if (title && !title.querySelector('.notification-badge')) {
            const badge = document.createElement('span');
            badge.className = 'badge bg-warning text-dark ms-2 notification-badge';
            badge.textContent = `${pendingRows.length} en attente`;
            title.appendChild(badge);
        }
    }

    // Confirmation avant validation
    document.querySelectorAll('a[title*="Valider"]').forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.title.includes('maintenant')) {
                const stagiaireRow = this.closest('tr');
                const stagiaireName = stagiaireRow.querySelector('strong').textContent;

                if (!confirm(`Êtes-vous sûr de vouloir procéder à la validation de la convention de ${stagiaireName} ?\n\nCette action confirmera l'acceptation du stagiaire au stage.`)) {
                    e.preventDefault();
                }
            }
        });
    });
});
</script>
{% endblock %}
