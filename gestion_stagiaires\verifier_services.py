#!/usr/bin/env python
"""
Script pour vérifier et recréer les services si nécessaire
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Service

User = get_user_model()

def verifier_services():
    """Vérifier et recréer les services si nécessaire"""
    
    print("=== VÉRIFICATION DES SERVICES ===")
    
    # Lister tous les services
    services = Service.objects.all()
    print(f"📋 Services existants: {services.count()}")
    
    for service in services:
        print(f"   🏢 {service.nom} (ID: {service.id}, Actif: {service.actif})")
    
    # Vérifier si Marketing existe
    service_marketing = Service.objects.filter(nom__iexact='marketing').first()
    
    if not service_marketing:
        print(f"\n❌ Service Marketing non trouvé - Création...")
        
        service_marketing = Service.objects.create(
            nom='Marketing',
            code_service='MKT001',
            actif=True
        )
        print(f"✅ Service Marketing créé: {service_marketing.nom} (ID: {service_marketing.id})")
    else:
        print(f"\n✅ Service Marketing trouvé: {service_marketing.nom}")
    
    # Assigner l'encadrant au service Marketing
    encadrant = User.objects.filter(email='<EMAIL>').first()
    if encadrant:
        print(f"\n👨‍💼 Assignation de {encadrant.get_full_name()} au service Marketing...")
        encadrant.service = service_marketing
        encadrant.save()
        print(f"✅ Service assigné: {encadrant.get_full_name()} → {service_marketing.nom}")
    
    # Vérification finale
    print(f"\n🔍 VÉRIFICATION FINALE:")
    services_finaux = Service.objects.all()
    for service in services_finaux:
        encadrants_count = User.objects.filter(service=service, role='ENCADRANT').count()
        print(f"   🏢 {service.nom}: {encadrants_count} encadrants")

if __name__ == '__main__':
    verifier_services()
