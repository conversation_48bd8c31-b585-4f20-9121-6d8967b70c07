#!/usr/bin/env python
import os
import django
import sqlite3

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.conf import settings

# Chemin vers la base de données
db_path = settings.DATABASES['default']['NAME']

print(f"Chemin de la base de données : {db_path}")
print(f"Le fichier existe : {os.path.exists(db_path)}")

# Vérifier si le fichier est une base de données SQLite valide
if os.path.exists(db_path):
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version();")
        version = cursor.fetchone()
        print(f"Version SQLite : {version[0]}")
        
        # Lister les tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print("\nTables dans la base de données :")
        for table in tables:
            print(f"- {table[0]}")
        
        conn.close()
        print("\nLa base de données SQLite est valide et accessible.")
    except sqlite3.Error as e:
        print(f"Erreur lors de l'accès à la base de données : {e}")
else:
    print("Le fichier de base de données n'existe pas encore.")
    print("Exécutez 'python manage.py migrate' pour créer la base de données.")