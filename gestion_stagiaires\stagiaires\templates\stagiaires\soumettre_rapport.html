{% extends 'stagiaires/base.html' %}

{% block title %}Soumettre un rapport - {{ stagiaire.nom_complet }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-file-upload me-2"></i>
                            Soumettre un rapport pour {{ stagiaire.nom_complet }}
                        </h4>
                        <a href="{% url 'rapports_stagiaire' stagiaire.id %}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-1"></i>Retour
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Informations du stagiaire -->
                    <div class="alert alert-info">
                        <div class="row">
                            <div class="col-md-6">
                                <strong><i class="fas fa-user me-1"></i>Stagiaire :</strong> {{ stagiaire.nom_complet }}<br>
                                <strong><i class="fas fa-envelope me-1"></i>Email :</strong> {{ stagiaire.email }}
                            </div>
                            <div class="col-md-6">
                                <strong><i class="fas fa-calendar me-1"></i>Période de stage :</strong><br>
                                {{ stagiaire.date_debut|date:"d/m/Y" }} - {{ stagiaire.date_fin|date:"d/m/Y" }}
                                ({{ stagiaire.duree_stage }} jours)
                            </div>
                        </div>
                    </div>

                    <!-- Formulaire de soumission -->
                    <form method="post" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="{{ form.titre.id_for_label }}" class="form-label">
                                        <i class="fas fa-heading me-1"></i>Titre du rapport *
                                    </label>
                                    {{ form.titre }}
                                    {% if form.titre.errors %}
                                        <div class="text-danger small">{{ form.titre.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">Donnez un titre clair et descriptif à votre rapport</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.mission.id_for_label }}" class="form-label">
                                        <i class="fas fa-tasks me-1"></i>Mission liée
                                    </label>
                                    {{ form.mission }}
                                    {% if form.mission.errors %}
                                        <div class="text-danger small">{{ form.mission.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">Sélectionnez la mission à laquelle ce rapport se rapporte (optionnel)</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.fichier_rapport.id_for_label }}" class="form-label">
                                        <i class="fas fa-file-pdf me-1"></i>Fichier du rapport *
                                    </label>
                                    {{ form.fichier_rapport }}
                                    {% if form.fichier_rapport.errors %}
                                        <div class="text-danger small">{{ form.fichier_rapport.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">Formats acceptés : PDF, DOC, DOCX (max 10 MB)</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">
                                        <i class="fas fa-align-left me-1"></i>Description du rapport *
                                    </label>
                                    {{ form.description }}
                                    {% if form.description.errors %}
                                        <div class="text-danger small">{{ form.description.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">Décrivez brièvement le contenu de votre rapport, les points clés abordés, etc.</div>
                                </div>
                            </div>
                        </div>

                        <!-- Affichage des erreurs générales -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Conseils pour la soumission -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h6 class="card-title text-primary">
                                    <i class="fas fa-lightbulb me-1"></i>Conseils pour un bon rapport
                                </h6>
                                <ul class="mb-0 small">
                                    <li><strong>Structure claire :</strong> Introduction, développement, conclusion</li>
                                    <li><strong>Contenu pertinent :</strong> Décrivez vos activités, apprentissages et réalisations</li>
                                    <li><strong>Analyse critique :</strong> Réflexion sur votre expérience et les compétences acquises</li>
                                    <li><strong>Présentation soignée :</strong> Orthographe, mise en page, illustrations si nécessaire</li>
                                    <li><strong>Respect des consignes :</strong> Vérifiez les exigences spécifiques de votre formation</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Informations sur le processus de validation -->
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-info-circle me-1"></i>Processus de validation</h6>
                            <p class="mb-0">
                                Une fois soumis, votre rapport sera examiné par votre encadrant. 
                                Vous recevrez une notification par email lorsque la validation sera effectuée. 
                                Le rapport peut être accepté, rejeté ou nécessiter des révisions.
                            </p>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'rapports_stagiaire' stagiaire.id %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-paper-plane me-1"></i>Soumettre le rapport
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Validation du fichier côté client
document.getElementById('{{ form.fichier_rapport.id_for_label }}').addEventListener('change', function() {
    const file = this.files[0];
    const maxSize = 10 * 1024 * 1024; // 10 MB
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    
    if (file) {
        // Vérifier la taille
        if (file.size > maxSize) {
            alert('Le fichier est trop volumineux. La taille maximale autorisée est de 10 MB.');
            this.value = '';
            return;
        }
        
        // Vérifier le type
        if (!allowedTypes.includes(file.type)) {
            alert('Type de fichier non autorisé. Veuillez sélectionner un fichier PDF, DOC ou DOCX.');
            this.value = '';
            return;
        }
        
        // Afficher les informations du fichier
        showFileInfo(file);
    }
});

function showFileInfo(file) {
    // Supprimer l'ancien affichage s'il existe
    const existingInfo = document.getElementById('file-info');
    if (existingInfo) {
        existingInfo.remove();
    }
    
    // Créer l'affichage des informations
    const fileInfo = document.createElement('div');
    fileInfo.id = 'file-info';
    fileInfo.className = 'alert alert-success mt-2';
    fileInfo.innerHTML = `
        <h6><i class="fas fa-check-circle me-1"></i>Fichier sélectionné</h6>
        <p class="mb-1"><strong>Nom :</strong> ${file.name}</p>
        <p class="mb-1"><strong>Taille :</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
        <p class="mb-0"><strong>Type :</strong> ${file.type}</p>
    `;
    
    // Ajouter après le champ de fichier
    const fileField = document.getElementById('{{ form.fichier_rapport.id_for_label }}');
    fileField.parentNode.appendChild(fileInfo);
}

// Validation du formulaire avant soumission
document.querySelector('form').addEventListener('submit', function(e) {
    const fileField = document.getElementById('{{ form.fichier_rapport.id_for_label }}');
    const titreField = document.getElementById('{{ form.titre.id_for_label }}');
    const descriptionField = document.getElementById('{{ form.description.id_for_label }}');
    
    // Vérifier que les champs obligatoires sont remplis
    if (!titreField.value.trim()) {
        e.preventDefault();
        alert('Veuillez saisir un titre pour le rapport.');
        titreField.focus();
        return;
    }
    
    if (!descriptionField.value.trim()) {
        e.preventDefault();
        alert('Veuillez saisir une description pour le rapport.');
        descriptionField.focus();
        return;
    }
    
    if (!fileField.files.length) {
        e.preventDefault();
        alert('Veuillez sélectionner un fichier pour le rapport.');
        fileField.focus();
        return;
    }
    
    // Confirmation avant soumission
    if (!confirm('Êtes-vous sûr de vouloir soumettre ce rapport ? Une fois soumis, il sera envoyé pour validation.')) {
        e.preventDefault();
        return;
    }
    
    // Afficher un indicateur de chargement
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Soumission en cours...';
    submitBtn.disabled = true;
});

// Génération automatique du titre basé sur la mission sélectionnée
document.getElementById('{{ form.mission.id_for_label }}').addEventListener('change', function() {
    const titreField = document.getElementById('{{ form.titre.id_for_label }}');
    const selectedOption = this.options[this.selectedIndex];
    
    if (selectedOption.value && !titreField.value.trim()) {
        const missionTitle = selectedOption.text;
        titreField.value = `Rapport de mission : ${missionTitle}`;
    }
});
</script>
{% endblock %}
