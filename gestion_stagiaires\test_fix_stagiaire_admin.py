#!/usr/bin/env python
"""
Script pour tester que le problème d'ajout de stagiaires dans l'admin est résolu
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service
from stagiaires.admin import StagiaireAdmin
from django.contrib import admin
from django.test import RequestFactory
from datetime import date, timedelta

User = get_user_model()

def test_fix_stagiaire_admin():
    """Tester que le problème d'ajout de stagiaires est résolu"""
    
    print("=== Test de la correction du problème d'ajout de stagiaires ===")
    
    # Récupérer les utilisateurs nécessaires
    admin_user = User.objects.filter(is_superuser=True).first()
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not admin_user:
        print("❌ Aucun administrateur trouvé")
        return
    
    if not encadrant:
        print("❌ Aucun encadrant trouvé")
        return
    
    print(f"✅ Admin: {admin_user.username}")
    print(f"✅ Encadrant: {encadrant.username}")
    
    # Test 1: Création directe avec tous les champs
    print(f"\n🧪 Test 1: Création directe avec cree_par")
    
    try:
        # Supprimer le stagiaire de test s'il existe
        Stagiaire.objects.filter(email='<EMAIL>').delete()
        
        stagiaire_data = {
            'nom': 'TestFix',
            'prenom': 'Stagiaire',
            'email': '<EMAIL>',
            'date_naissance': date(2000, 1, 1),
            'departement': 'IT',
            'encadrant': encadrant,
            'service': encadrant.service,
            'date_debut': date.today(),
            'date_fin': date.today() + timedelta(days=90),
            'etablissement': 'Université Test Fix',
            'niveau_etude': 'Master',
            'specialite': 'Informatique',
            'cree_par': admin_user  # Champ obligatoire
        }
        
        stagiaire = Stagiaire.objects.create(**stagiaire_data)
        print(f"   ✅ Stagiaire créé: {stagiaire.nom_complet}")
        print(f"      Créé par: {stagiaire.cree_par}")
        
        # Nettoyer
        stagiaire.delete()
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 2: Simulation de l'interface d'administration
    print(f"\n🖥️ Test 2: Simulation de l'interface d'administration")
    
    try:
        # Créer une instance de StagiaireAdmin
        stagiaire_admin = StagiaireAdmin(Stagiaire, admin.site)
        
        # Simuler une requête
        factory = RequestFactory()
        request = factory.post('/admin/stagiaires/stagiaire/add/')
        request.user = admin_user
        
        # Créer un stagiaire sans cree_par (comme dans l'interface)
        stagiaire_data_admin = {
            'nom': 'TestAdmin',
            'prenom': 'Stagiaire',
            'email': '<EMAIL>',
            'date_naissance': date(2000, 1, 1),
            'departement': 'IT',
            'encadrant': encadrant,
            'service': encadrant.service,
            'date_debut': date.today(),
            'date_fin': date.today() + timedelta(days=90),
            'etablissement': 'Université Test Admin',
            'niveau_etude': 'Master',
            'specialite': 'Informatique'
            # cree_par manquant intentionnellement
        }
        
        # Supprimer le stagiaire de test s'il existe
        Stagiaire.objects.filter(email='<EMAIL>').delete()
        
        # Créer le stagiaire
        stagiaire = Stagiaire(**stagiaire_data_admin)
        
        # Simuler la méthode save_model de l'admin
        stagiaire_admin.save_model(request, stagiaire, None, change=False)
        
        print(f"   ✅ Stagiaire créé via admin: {stagiaire.nom_complet}")
        print(f"      ID: {stagiaire.id}")
        print(f"      Créé par: {stagiaire.cree_par}")
        
        # Vérifier que le stagiaire est bien en base
        stagiaire_db = Stagiaire.objects.get(id=stagiaire.id)
        print(f"   ✅ Stagiaire trouvé en base: {stagiaire_db.nom_complet}")
        
        # Nettoyer
        stagiaire.delete()
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Vérification des fieldsets
    print(f"\n📋 Test 3: Vérification des fieldsets")
    
    try:
        stagiaire_admin = StagiaireAdmin(Stagiaire, admin.site)
        factory = RequestFactory()
        request = factory.get('/admin/stagiaires/stagiaire/add/')
        request.user = admin_user
        
        fieldsets = stagiaire_admin.get_fieldsets(request)
        readonly_fields = stagiaire_admin.get_readonly_fields(request)
        
        print(f"   ✅ Nombre de fieldsets: {len(fieldsets)}")
        print(f"   ✅ Champs en lecture seule: {len(readonly_fields)}")
        
        # Vérifier que cree_par est en lecture seule
        if 'cree_par' in readonly_fields:
            print(f"   ✅ 'cree_par' est en lecture seule")
        else:
            print(f"   ❌ 'cree_par' n'est pas en lecture seule")
        
        # Vérifier que cree_par est dans les fieldsets
        all_fields = []
        for name, options in fieldsets:
            fields = options.get('fields', [])
            all_fields.extend(fields)
        
        if 'cree_par' in all_fields:
            print(f"   ✅ 'cree_par' est dans les fieldsets")
        else:
            print(f"   ❌ 'cree_par' n'est pas dans les fieldsets")
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 4: Test de formulaire complet
    print(f"\n📝 Test 4: Test de formulaire complet")
    
    try:
        stagiaire_admin = StagiaireAdmin(Stagiaire, admin.site)
        factory = RequestFactory()
        request = factory.get('/admin/stagiaires/stagiaire/add/')
        request.user = admin_user
        
        # Récupérer le formulaire
        form_class = stagiaire_admin.get_form(request)
        
        # Données du formulaire (sans cree_par)
        form_data = {
            'nom': 'TestFormulaire',
            'prenom': 'Stagiaire',
            'email': '<EMAIL>',
            'telephone': '0123456789',
            'date_naissance': date(2000, 1, 1),
            'departement': 'IT',
            'service': encadrant.service.id if encadrant.service else '',
            'encadrant': encadrant.id,
            'date_debut': date.today(),
            'date_fin': date.today() + timedelta(days=90),
            'statut': 'EN_COURS',
            'etablissement': 'Université Test Formulaire',
            'niveau_etude': 'Master',
            'specialite': 'Informatique',
            'technologies': 'Python, Django',
            'thematique': '',
            'sujet': '',
            'duree_estimee': 90,
            'description_taches': 'Tâches de test',
            'statut_taches': 'NON_COMMENCEES',
            'statut_convention': 'EN_ATTENTE',
            'commentaire_convention': '',
            'evaluation_encadrant': '',
            'note_finale': '',
        }
        
        # Créer le formulaire
        form = form_class(data=form_data)
        
        print(f"   📋 Validation du formulaire:")
        if form.is_valid():
            print(f"   ✅ Formulaire valide")
            
            # Supprimer le stagiaire de test s'il existe
            Stagiaire.objects.filter(email='<EMAIL>').delete()
            
            # Sauvegarder via le formulaire
            stagiaire = form.save(commit=False)
            
            # Simuler save_model
            stagiaire_admin.save_model(request, stagiaire, form, change=False)
            
            print(f"   ✅ Stagiaire créé via formulaire: {stagiaire.nom_complet}")
            print(f"      Créé par: {stagiaire.cree_par}")
            
            # Nettoyer
            stagiaire.delete()
            
        else:
            print(f"   ❌ Formulaire invalide:")
            for field, errors in form.errors.items():
                print(f"      • {field}: {errors}")
    
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n=== Test terminé ===")
    print(f"\n✅ Le problème d'ajout de stagiaires devrait maintenant être résolu!")
    print(f"\n📋 Résumé des corrections:")
    print(f"   • Le champ 'cree_par' est maintenant en lecture seule")
    print(f"   • Il est automatiquement rempli lors de la création")
    print(f"   • La méthode save_model gère tous les cas d'erreur")
    print(f"   • Des messages de succès/erreur sont affichés")

if __name__ == '__main__':
    test_fix_stagiaire_admin()
