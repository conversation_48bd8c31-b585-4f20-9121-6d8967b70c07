{% extends 'stagiaires/base.html' %}

{% block title %}Ajouter un stagiaire | Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        Ajouter un stagiaire
                    </h3>
                    <div>
                        <a href="{% url 'stagiaires_list' %}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- Informations personnelles -->
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>Informations personnelles</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="{{ form.nom.id_for_label }}" class="form-label">Nom *</label>
                                            {{ form.nom }}
                                            {% if form.nom.errors %}
                                                <div class="text-danger small mt-1">{{ form.nom.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.prenom.id_for_label }}" class="form-label">Prénom *</label>
                                            {{ form.prenom }}
                                            {% if form.prenom.errors %}
                                                <div class="text-danger small mt-1">{{ form.prenom.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.email.id_for_label }}" class="form-label">Email *</label>
                                            {{ form.email }}
                                            {% if form.email.errors %}
                                                <div class="text-danger small mt-1">{{ form.email.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.telephone.id_for_label }}" class="form-label">Téléphone</label>
                                            {{ form.telephone }}
                                            {% if form.telephone.errors %}
                                                <div class="text-danger small mt-1">{{ form.telephone.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.date_naissance.id_for_label }}" class="form-label">Date de naissance</label>
                                            {{ form.date_naissance }}
                                            {% if form.date_naissance.errors %}
                                                <div class="text-danger small mt-1">{{ form.date_naissance.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Informations académiques -->
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Informations académiques</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="{{ form.etablissement.id_for_label }}" class="form-label">Établissement *</label>
                                            {{ form.etablissement }}
                                            {% if form.etablissement.errors %}
                                                <div class="text-danger small mt-1">{{ form.etablissement.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.niveau_etude.id_for_label }}" class="form-label">Niveau d'étude *</label>
                                            {{ form.niveau_etude }}
                                            {% if form.niveau_etude.errors %}
                                                <div class="text-danger small mt-1">{{ form.niveau_etude.errors }}</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="{{ form.specialite.id_for_label }}" class="form-label">Spécialité *</label>
                                            {{ form.specialite }}
                                            {% if form.specialite.errors %}
                                                <div class="text-danger small mt-1">{{ form.specialite.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Informations de stage -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Informations de stage</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="{{ form.date_debut.id_for_label }}" class="form-label">Date de début *</label>
                                            {{ form.date_debut }}
                                            {% if form.date_debut.errors %}
                                                <div class="text-danger small mt-1">{{ form.date_debut.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="{{ form.date_fin.id_for_label }}" class="form-label">Date de fin *</label>
                                            {{ form.date_fin }}
                                            {% if form.date_fin.errors %}
                                                <div class="text-danger small mt-1">{{ form.date_fin.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="{{ form.duree_estimee.id_for_label }}" class="form-label">Durée estimée (jours) *</label>
                                            {{ form.duree_estimee }}
                                            {% if form.duree_estimee.errors %}
                                                <div class="text-danger small mt-1">{{ form.duree_estimee.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.thematique.id_for_label }}" class="form-label">Thématique *</label>
                                            {{ form.thematique }}
                                            {% if form.thematique.errors %}
                                                <div class="text-danger small mt-1">{{ form.thematique.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.sujet.id_for_label }}" class="form-label">Sujet *</label>
                                            {{ form.sujet }}
                                            {% if form.sujet.errors %}
                                                <div class="text-danger small mt-1">{{ form.sujet.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Ajout des champs manquants dans la section appropriée -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Informations du stage</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.departement.id_for_label }}" class="form-label">Département *</label>
                                            {{ form.departement }}
                                            {% if form.departement.errors %}
                                                <div class="text-danger small mt-1">{{ form.departement.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {% if user.role == 'ENCADRANT' %}
                                                <label class="form-label">Encadrant</label>
                                                <input type="text" class="form-control" value="{{ user.get_full_name|default:user.username }}" readonly>
                                                {{ form.encadrant }}
                                            {% else %}
                                                <label for="{{ form.encadrant.id_for_label }}" class="form-label">Encadrant *</label>
                                                {{ form.encadrant }}
                                            {% endif %}
                                            {% if form.encadrant.errors %}
                                                <div class="text-danger small mt-1">{{ form.encadrant.errors }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Enregistrer le stagiaire
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Script pour charger dynamiquement les sujets en fonction de la thématique sélectionnée
    document.addEventListener('DOMContentLoaded', function() {
        const thematiqueSelect = document.getElementById('{{ form.thematique.id_for_label }}');
        const sujetSelect = document.getElementById('{{ form.sujet.id_for_label }}');
        const dureeInput = document.getElementById('{{ form.duree_estimee.id_for_label }}');
        
        // Fonction pour charger les sujets
        function loadSujets() {
            const thematiqueId = thematiqueSelect.value;
            if (!thematiqueId) {
                // Vider la liste des sujets si aucune thématique n'est sélectionnée
                sujetSelect.innerHTML = '<option value="">Sélectionner un sujet</option>';
                return;
            }
            
            // Appel AJAX pour récupérer les sujets
            fetch(`/api/sujets-par-thematique/?thematique_id=${thematiqueId}`)
                .then(response => response.json())
                .then(data => {
                    // Vider la liste actuelle
                    sujetSelect.innerHTML = '<option value="">Sélectionner un sujet</option>';
                    
                    // Ajouter les nouveaux sujets
                    data.forEach(sujet => {
                        const option = document.createElement('option');
                        option.value = sujet.id;
                        option.textContent = sujet.titre;
                        option.dataset.duree = sujet.duree;
                        sujetSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Erreur lors du chargement des sujets:', error));
        }
        
        // Charger les sujets au chargement de la page
        if (thematiqueSelect.value) {
            loadSujets();
        }
        
        // Charger les sujets quand la thématique change
        thematiqueSelect.addEventListener('change', loadSujets);
        
        // Mettre à jour la durée estimée quand le sujet change
        sujetSelect.addEventListener('change', function() {
            const selectedOption = sujetSelect.options[sujetSelect.selectedIndex];
            if (selectedOption && selectedOption.dataset.duree) {
                dureeInput.value = selectedOption.dataset.duree;
            }
        });
    });
</script>
{% endblock %}
{% endblock %}


