{% extends 'stagiaires/base.html' %}

{% block title %}{{ title }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Durées Estimées des Stages
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-5">
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0">Ajouter une durée estimée</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post">
                                        {% csrf_token %}
                                        <div class="mb-3">
                                            <label for="{{ form.duree.id_for_label }}" class="form-label">
                                                <i class="fas fa-clock me-1"></i>{{ form.duree.label }}
                                            </label>
                                            {{ form.duree }}
                                            {% if form.duree.errors %}
                                                <div class="text-danger small mt-1">{{ form.duree.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <div class="mb-3">
                                            <label for="{{ form.commentaire.id_for_label }}" class="form-label">
                                                <i class="fas fa-comment me-1"></i>{{ form.commentaire.label }}
                                            </label>
                                            {{ form.commentaire }}
                                            {% if form.commentaire.errors %}
                                                <div class="text-danger small mt-1">{{ form.commentaire.errors }}</div>
                                            {% endif %}
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>Enregistrer
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-7">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0">Durées estimées existantes</h5>
                                </div>
                                <div class="card-body">
                                    {% if durees_estimees %}
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Durée</th>
                                                    <th>Commentaire</th>
                                                    <th>Créé par</th>
                                                    <th>Date</th>
                                                    {% if user.role == 'ADMIN' %}
                                                    <th>Actions</th>
                                                    {% endif %}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for duree in durees_estimees %}
                                                <tr>
                                                    <td>{{ duree.duree }} jours</td>
                                                    <td>{{ duree.commentaire|default:"-" }}</td>
                                                    <td>{{ duree.cree_par.get_full_name }}</td>
                                                    <td>{{ duree.date_creation|date:"d/m/Y" }}</td>
                                                    {% if user.role == 'ADMIN' %}
                                                    <td>
                                                        <button class="btn btn-sm btn-danger" onclick="confirmDeleteDuree({{ duree.id }})">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                    {% endif %}
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Aucune durée estimée n'a été enregistrée pour le moment.
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if user.role == 'ADMIN' %}
<script>
function confirmDeleteDuree(dureeId) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer cette durée estimée ?`)) {
        // Créer un formulaire pour envoyer une requête POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = "{% url 'delete_duree_estimee' 0 %}".replace('0', dureeId);
        
        // Ajouter le token CSRF
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = '{{ csrf_token }}';
        form.appendChild(csrfToken);
        
        // Ajouter le formulaire au document et le soumettre
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endif %}
{% endblock %}


