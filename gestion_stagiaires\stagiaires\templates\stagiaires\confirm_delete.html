{% extends 'stagiaires/base.html' %}

{% block title %}Confirmer la suppression - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Confirmer la suppression
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-warning me-2"></i>
                        <strong>Attention !</strong> Cette action est irréversible.
                    </div>

                    <p class="mb-4">
                        Êtes-vous sûr de vouloir supprimer
                        {% if object_type %}
                            {{ object_type|default:"l'élément" }}
                        {% else %}
                            l'élément
                        {% endif %}
                        <strong>"{{ object_name|default:object }}"</strong> ?
                    </p>

                    {% if object_type == 'service' %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note :</strong> La suppression de ce service peut affecter les utilisateurs, thématiques et sujets qui lui sont associés.
                    </div>
                    {% endif %}

                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger me-2">
                            <i class="fas fa-trash me-1"></i>
                            Confirmer la suppression
                        </button>
                    </form>

                    <a href="{% if cancel_url %}{% url cancel_url %}{% else %}{% url 'services_list' %}{% endif %}" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i>
                        Annuler
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}