#!/usr/bin/env python
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import Sujet
from django.db import connection

print("=== VÉRIFICATION DU MODÈLE SUJET ===")

# 1. Afficher les champs du modèle
print("Champs du modèle Sujet:")
for field in Sujet._meta.get_fields():
    print(f"- {field.name} ({field.__class__.__name__})")

# 2. Vérifier la structure de la table dans la base de données
with connection.cursor() as cursor:
    cursor.execute("PRAGMA table_info(stagiaires_sujet)")
    columns = cursor.fetchall()
    print("\nStructure de la table stagiaires_sujet:")
    for col in columns:
        print(f"- {col[1]} ({col[2]}) {'PRIMARY KEY' if col[5] else ''}")

# 3. Vérifier les données dans la table
with connection.cursor() as cursor:
    cursor.execute("SELECT COUNT(*) FROM stagiaires_sujet")
    count = cursor.fetchone()[0]
    print(f"\nNombre de sujets dans la base de données: {count}")
    
    if count > 0:
        cursor.execute("SELECT id, titre, thematique_id, actif FROM stagiaires_sujet LIMIT 5")
        rows = cursor.fetchall()
        print("\nExemples de sujets:")
        for row in rows:
            print(f"- ID: {row[0]}, Titre: {row[1]}, Thématique ID: {row[2]}, Actif: {row[3]}")

print("\n=== TERMINÉ ===")