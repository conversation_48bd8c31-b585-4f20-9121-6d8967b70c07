@startuml Diagramme_Sequence_Rencontre_Encadrant_Stagiaire

!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60

title Diagramme de Séquence - Processus de Rencontre Encadrant-Stagiaire

actor "Encadrant" as Encadrant
participant "Interface Web" as UI
participant "Syst<PERSON> Auth" as Auth
participant "<PERSON><PERSON> Rencontre" as VueRencontre
participant "Modèle Stagiaire" as ModelStagiaire
participant "Modèle Tâche" as ModelTache
participant "Système Email" as Email
participant "Base de Données" as DB

== Authentification et Accès ==

Encadrant -> UI : Accède à la liste des stagiaires
UI -> Auth : Vérifier authentification
Auth -> UI : Utilisateur authentifié (rôle: ENCADRANT)
UI -> VueRencontre : Filtrer stagiaires par service
VueRencontre -> ModelStagiaire : get_stagiaires_by_service(encadrant.service)
ModelStagiaire -> DB : SELECT * FROM stagiaires WHERE service_id = ?
DB -> ModelStagiaire : Liste des stagiaires du service
ModelStagiaire -> VueRencontre : Stagiaires filtrés
VueRencontre -> UI : Afficher liste avec bouton "Rencontre"

== Initiation de la Rencontre ==

Encadrant -> UI : Clic sur bouton "Rencontre" pour un stagiaire
UI -> VueRencontre : rencontre_stagiaire_view(stagiaire_id)
VueRencontre -> Auth : Vérifier permissions (même service)
Auth -> VueRencontre : Permission accordée

VueRencontre -> ModelStagiaire : get_stagiaire_by_id(stagiaire_id)
ModelStagiaire -> DB : SELECT * FROM stagiaires WHERE id = ?
DB -> ModelStagiaire : Données du stagiaire
ModelStagiaire -> VueRencontre : Objet Stagiaire

VueRencontre -> ModelTache : get_taches_by_stagiaire(stagiaire_id)
ModelTache -> DB : SELECT * FROM taches WHERE stagiaire_id = ?
DB -> ModelTache : Liste des tâches existantes
ModelTache -> VueRencontre : Tâches du stagiaire

VueRencontre -> UI : Afficher page de rencontre (infos + CV + tâches)

== Consultation du CV ==

Encadrant -> UI : Clic sur "Consulter CV"
UI -> VueRencontre : Ouvrir CV dans nouvel onglet
VueRencontre -> ModelStagiaire : get_cv_url(stagiaire)
ModelStagiaire -> VueRencontre : URL du fichier CV
VueRencontre -> UI : Rediriger vers CV (target="_blank")

note right of UI : Le CV s'ouvre dans un\nnouvel onglet pour consultation

== Ajout de Nouvelles Tâches ==

Encadrant -> UI : Remplir formulaire de nouvelle tâche
Encadrant -> UI : Clic sur "Ajouter Tâche"
UI -> VueRencontre : POST avec données de la tâche

VueRencontre -> VueRencontre : Valider formulaire TacheForm
alt Formulaire valide
    VueRencontre -> ModelTache : Créer nouvelle tâche
    ModelTache -> DB : INSERT INTO taches (titre, description, priorité, ...)
    DB -> ModelTache : Tâche créée avec ID
    ModelTache -> VueRencontre : Confirmation création
    VueRencontre -> UI : Message de succès + rechargement page
else Formulaire invalide
    VueRencontre -> UI : Afficher erreurs de validation
end

== Gestion des Tâches Existantes ==

Encadrant -> UI : Modifier statut d'une tâche (Démarrer/Terminer)
UI -> VueRencontre : Requête de changement de statut
VueRencontre -> ModelTache : update_statut_tache(tache_id, nouveau_statut)
ModelTache -> DB : UPDATE taches SET statut = ?, date_debut = ? WHERE id = ?
DB -> ModelTache : Confirmation mise à jour
ModelTache -> VueRencontre : Statut mis à jour
VueRencontre -> UI : Actualiser affichage des tâches

== Envoi du Récapitulatif par Email ==

Encadrant -> UI : Clic sur "Envoyer par Email"
UI -> VueRencontre : Demande d'envoi email

VueRencontre -> ModelTache : get_all_taches_stagiaire(stagiaire_id)
ModelTache -> DB : SELECT * FROM taches WHERE stagiaire_id = ? ORDER BY date_creation DESC
DB -> ModelTache : Toutes les tâches du stagiaire
ModelTache -> VueRencontre : Liste complète des tâches

VueRencontre -> VueRencontre : Construire contenu email
note right of VueRencontre : Formatage du message avec :\n- Nom du stagiaire\n- Liste des tâches\n- Descriptions et échéances\n- Signature de l'encadrant

VueRencontre -> Email : send_mail(destinataire, sujet, message)
Email -> Email : Envoyer via SMTP ou console
alt Email envoyé avec succès
    Email -> VueRencontre : Confirmation envoi
    VueRencontre -> UI : Message "Email envoyé avec succès"
else Erreur d'envoi
    Email -> VueRencontre : Erreur SMTP
    VueRencontre -> UI : Message d'erreur
end

== Fin de la Rencontre ==

Encadrant -> UI : Navigation vers autre page ou déconnexion
UI -> VueRencontre : Sauvegarder session
VueRencontre -> DB : Enregistrer logs d'activité (optionnel)

note over Encadrant, DB : Toutes les données sont persistées\net le stagiaire recevra le récapitulatif par email

== Gestion des Erreurs ==

alt Stagiaire non trouvé
    VueRencontre -> UI : Erreur 404 - Stagiaire inexistant
else Permission refusée
    Auth -> UI : Erreur 403 - Accès non autorisé
else Erreur serveur
    VueRencontre -> UI : Erreur 500 - Problème technique
end

@enduml
