{% extends 'stagiaires/base.html' %}

{% block title %}Contrats de Stage - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-file-contract me-2"></i>
                        Gestion des Contrats de Stage
                    </h3>
                    <a href="{% url 'dashboard' %}" class="btn btn-dark btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Retour au tableau de bord
                    </a>
                </div>
                <div class="card-body">
                    <!-- Actions rapides -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="Rechercher un contrat...">
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-success" onclick="createContract()">
                                <i class="fas fa-plus me-1"></i>Nouveau contrat
                            </button>
                            <button class="btn btn-outline-primary ms-2" onclick="generateTemplate()">
                                <i class="fas fa-file-alt me-1"></i>Modèle
                            </button>
                        </div>
                    </div>

                    <!-- Statistiques contrats -->
                    
                    <!-- Filtres -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <select class="form-control">
                                <option value="">Tous les statuts</option>
                                <option value="SIGNE">Signé</option>
                                <option value="EN_ATTENTE">En attente</option>
                                <option value="EXPIRE">Expiré</option>
                                <option value="ANNULE">Annulé</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control">
                                <option value="">Tous les départements</option>
                                <option value="IT">Informatique</option>
                                <option value="MARKETING">Marketing</option>
                                <option value="RH">RH</option>
                                <option value="FINANCE">Finance</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" placeholder="Date de début">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" placeholder="Date de fin">
                        </div>
                    </div>

                    <!-- Liste des contrats -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><i class="fas fa-hashtag me-1"></i>Référence</th>
                                    <th><i class="fas fa-user me-1"></i>Stagiaire</th>
                                    <th><i class="fas fa-building me-1"></i>Département</th>
                                    <th><i class="fas fa-calendar me-1"></i>Période</th>
                                    <th><i class="fas fa-info-circle me-1"></i>Statut</th>
                                    <th><i class="fas fa-signature me-1"></i>Signature RH</th>
                                    <th><i class="fas fa-cogs me-1"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for contrat in contrats %}
                                <tr>
                                    <td>
                                        <strong>{{ contrat.reference }}</strong>
                                        <br>
                                        <small class="text-muted">Créé le {{ contrat.date_creation|date:"d/m/Y" }}</small>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-primary text-white me-2">
                                                {{ contrat.stagiaire.nom.0 }}{{ contrat.stagiaire.prenom.0 }}
                                            </div>
                                            <div>
                                                <strong>{{ contrat.stagiaire.nom_complet }}</strong>
                                                <br>
                                                <small class="text-muted">{{ contrat.stagiaire.email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ contrat.stagiaire.get_departement_display }}</td>
                                    <td>
                                        <small>{{ contrat.stagiaire.date_debut|date:"d/m/Y" }} - {{ contrat.stagiaire.date_fin|date:"d/m/Y" }}</small>
                                        <br>
                                        <span class="badge bg-info">{{ contrat.duree_hebdomadaire }}h/sem</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if contrat.statut == 'ENTIEREMENT_SIGNE' %}success{% elif contrat.statut == 'PARTIELLEMENT_SIGNE' %}warning{% elif contrat.statut == 'EN_ATTENTE_SIGNATURE' %}info{% else %}secondary{% endif %}">
                                            {{ contrat.get_statut_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            {% if contrat.signature_rh %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Signé
                                                </span>
                                                {% if contrat.signature_rh_par %}
                                                    <br><small class="text-muted">{{ contrat.signature_rh_par.get_full_name }}</small>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>En attente
                                                </span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'contrat_detail' contrat.id %}" class="btn btn-outline-primary" title="Voir contrat">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-success" title="Télécharger PDF" onclick="downloadContract('{{ contrat.reference }}')">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            {% if user.role == 'RH' or user.role == 'ADMIN' %}
                                                {% if contrat.statut != 'ENTIEREMENT_SIGNE' %}
                                                    <button class="btn btn-outline-warning" title="Modifier" onclick="editContract('{{ contrat.reference }}')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                {% endif %}
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        <i class="fas fa-file-contract fa-2x mb-2"></i>
                                        <br>
                                        Aucun contrat trouvé
                                        <br>
                                        <small>Les contrats apparaîtront ici après validation des conventions</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Actions en lot -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6><i class="fas fa-tasks me-1"></i>Actions en lot</h6>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-outline-primary" onclick="bulkExport()">
                                            <i class="fas fa-file-export me-1"></i>Exporter sélection
                                        </button>
                                        <button class="btn btn-outline-info" onclick="bulkReminder()">
                                            <i class="fas fa-bell me-1"></i>Envoyer rappels
                                        </button>
                                        <button class="btn btn-outline-success" onclick="bulkArchive()">
                                            <i class="fas fa-archive me-1"></i>Archiver
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour créer/modifier un contrat -->
<div class="modal fade" id="contractModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contractModalTitle">Nouveau contrat de stage</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="contractModalBody">
                <!-- Contenu dynamique -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="saveContractBtn">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}
</style>

<script>
function createContract() {
    document.getElementById('contractModalTitle').textContent = 'Nouveau contrat de stage';
    document.getElementById('contractModalBody').innerHTML = `
        <div class="text-center">
            <i class="fas fa-file-contract fa-4x text-success mb-3"></i>
            <h5>Création d'un nouveau contrat</h5>
            <p class="text-muted">Fonctionnalité en cours de développement</p>
        </div>
    `;
    new bootstrap.Modal(document.getElementById('contractModal')).show();
}

function viewContract(reference) {
    alert(`Affichage du contrat ${reference}`);
}

function editContract(reference) {
    document.getElementById('contractModalTitle').textContent = `Modifier le contrat ${reference}`;
    document.getElementById('contractModalBody').innerHTML = `
        <div class="text-center">
            <i class="fas fa-edit fa-4x text-warning mb-3"></i>
            <h5>Modification du contrat ${reference}</h5>
            <p class="text-muted">Fonctionnalité en cours de développement</p>
        </div>
    `;
    new bootstrap.Modal(document.getElementById('contractModal')).show();
}

function downloadContract(reference) {
    alert(`Téléchargement du contrat ${reference} en cours...`);
}

function sendReminder(reference) {
    if (confirm(`Envoyer un rappel pour le contrat ${reference} ?`)) {
        alert('Rappel envoyé avec succès !');
    }
}

function generateTemplate() {
    alert('Génération du modèle de contrat en cours...');
}

function bulkExport() {
    alert('Export en lot en cours de développement');
}

function bulkReminder() {
    alert('Envoi de rappels en lot en cours de développement');
}

function bulkArchive() {
    alert('Archivage en lot en cours de développement');
}
</script>
{% endblock %}
