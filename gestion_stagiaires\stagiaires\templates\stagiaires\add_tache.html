{% extends 'stagiaires/base.html' %}

{% block title %}{{ title }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        Ajouter une tâche pour {{ stagiaire.nom_complet }}
                    </h3>
                    <a href="{% url 'taches_stagiaire' stagiaire.id %}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Retour aux tâches
                    </a>
                </div>
                <div class="card-body">
                    <!-- Informations du stagiaire -->
                    <div class="alert alert-info mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <strong><i class="fas fa-user me-1"></i> Stagiaire :</strong> {{ stagiaire.nom_complet }}<br>
                                <strong><i class="fas fa-building me-1"></i> Département :</strong> {{ stagiaire.get_departement_display }}<br>
                                <strong><i class="fas fa-graduation-cap me-1"></i> Formation :</strong> {{ stagiaire.specialite }}
                            </div>
                            <div class="col-md-6">
                                <strong><i class="fas fa-calendar-alt me-1"></i> Période :</strong> Du {{ stagiaire.date_debut|date:"d/m/Y" }} au {{ stagiaire.date_fin|date:"d/m/Y" }}<br>
                                <strong><i class="fas fa-user-tie me-1"></i> Encadrant :</strong> {{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}<br>
                                <strong><i class="fas fa-info-circle me-1"></i> Statut :</strong> 
                                <span class="badge {% if stagiaire.statut == 'EN_COURS' %}bg-success{% elif stagiaire.statut == 'TERMINE' %}bg-secondary{% elif stagiaire.statut == 'PLANIFIE' %}bg-info{% else %}bg-warning{% endif %}">
                                    {{ stagiaire.get_statut_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Formulaire d'ajout de tâche -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.titre.id_for_label }}" class="form-label">{{ form.titre.label }}</label>
                            {{ form.titre }}
                            {% if form.titre.errors %}
                                <div class="text-danger small mt-1">{{ form.titre.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">{{ form.description.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.priorite.id_for_label }}" class="form-label">{{ form.priorite.label }}</label>
                                {{ form.priorite }}
                                {% if form.priorite.errors %}
                                    <div class="text-danger small mt-1">{{ form.priorite.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.statut.id_for_label }}" class="form-label">{{ form.statut.label }}</label>
                                {{ form.statut }}
                                {% if form.statut.errors %}
                                    <div class="text-danger small mt-1">{{ form.statut.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.date_debut_prevue.id_for_label }}" class="form-label">{{ form.date_debut_prevue.label }}</label>
                                {{ form.date_debut_prevue }}
                                {% if form.date_debut_prevue.errors %}
                                    <div class="text-danger small mt-1">{{ form.date_debut_prevue.errors }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.date_fin_prevue.id_for_label }}" class="form-label">{{ form.date_fin_prevue.label }}</label>
                                {{ form.date_fin_prevue }}
                                {% if form.date_fin_prevue.errors %}
                                    <div class="text-danger small mt-1">{{ form.date_fin_prevue.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Enregistrer la tâche
                            </button>
                            <a href="{% url 'taches_stagiaire' stagiaire.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

