#!/usr/bin/env python
"""
Test de la suppression de la case admin de l'inscription publique
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client

User = get_user_model()

def test_case_admin_inscription():
    """Test de la case admin dans l'inscription vs interface admin"""
    
    print("=== TEST CASE ADMIN INSCRIPTION ===")
    
    client = Client()
    
    # 1. Test de la page d'inscription publique
    print("📝 TEST PAGE D'INSCRIPTION PUBLIQUE:")
    
    response = client.get('/register/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier que la case admin n'est PAS présente
        elements_admin = [
            'is_admin',
            'Privilèges d\'administration',
            'form.is_admin',
            'shield-alt',
            'admin'
        ]
        
        admin_elements_found = []
        for element in elements_admin:
            if element in content:
                admin_elements_found.append(element)
        
        if admin_elements_found:
            print(f"   ❌ Éléments admin trouvés dans l'inscription publique:")
            for element in admin_elements_found:
                print(f"      • {element}")
        else:
            print(f"   ✅ Aucun élément admin trouvé dans l'inscription publique")
        
        # Vérifier que les autres champs sont présents
        champs_requis = [
            'first_name',
            'last_name',
            'username',
            'email',
            'role',
            'service',
            'password1',
            'password2'
        ]
        
        champs_presents = []
        for champ in champs_requis:
            if champ in content:
                champs_presents.append(champ)
        
        print(f"   📋 Champs présents: {len(champs_presents)}/{len(champs_requis)}")
        
        if len(champs_presents) == len(champs_requis):
            print(f"   ✅ Tous les champs requis sont présents")
        else:
            champs_manquants = set(champs_requis) - set(champs_presents)
            print(f"   ⚠️ Champs manquants: {champs_manquants}")
    
    # 2. Test de l'interface d'ajout d'utilisateurs par l'admin
    print(f"\n👨‍💼 TEST INTERFACE ADMIN AJOUT UTILISATEUR:")
    
    # Se connecter en tant qu'admin
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    if not admin:
        print("   ❌ Aucun admin trouvé pour le test")
        return
    
    client.force_login(admin)
    
    response = client.get('/users/add/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier que la case admin EST présente
        elements_admin = [
            'is_admin',
            'Privilèges d\'administration',
            'form.is_admin',
            'shield-alt'
        ]
        
        admin_elements_found = []
        for element in elements_admin:
            if element in content:
                admin_elements_found.append(element)
        
        if admin_elements_found:
            print(f"   ✅ Éléments admin trouvés dans l'interface admin:")
            for element in admin_elements_found:
                print(f"      • {element}")
        else:
            print(f"   ❌ Aucun élément admin trouvé dans l'interface admin")
        
        # Vérifier que tous les champs sont présents
        champs_admin = [
            'first_name',
            'last_name',
            'username',
            'email',
            'role',
            'service',
            'is_admin',
            'password1',
            'password2'
        ]
        
        champs_presents_admin = []
        for champ in champs_admin:
            if champ in content:
                champs_presents_admin.append(champ)
        
        print(f"   📋 Champs présents: {len(champs_presents_admin)}/{len(champs_admin)}")
        
        if len(champs_presents_admin) == len(champs_admin):
            print(f"   ✅ Tous les champs admin sont présents")
        else:
            champs_manquants = set(champs_admin) - set(champs_presents_admin)
            print(f"   ⚠️ Champs manquants: {champs_manquants}")
    
    # 3. Test d'accès non autorisé à l'interface admin
    print(f"\n🔒 TEST ACCÈS NON AUTORISÉ:")
    
    # Se déconnecter
    client.logout()
    
    # Essayer d'accéder à l'interface admin sans être connecté
    response = client.get('/users/add/')
    print(f"   Status sans connexion: {response.status_code}")
    
    if response.status_code == 302:  # Redirection vers login
        print(f"   ✅ Redirection vers login pour utilisateur non connecté")
    else:
        print(f"   ❌ Accès autorisé sans connexion")
    
    # Se connecter en tant qu'utilisateur non-admin
    non_admin = User.objects.filter(role__in=['RH', 'ENCADRANT'], is_active=True).first()
    if non_admin:
        client.force_login(non_admin)
        
        response = client.get('/users/add/')
        print(f"   Status utilisateur non-admin: {response.status_code}")
        
        if response.status_code == 302:  # Redirection
            print(f"   ✅ Accès refusé pour utilisateur non-admin")
        else:
            print(f"   ❌ Accès autorisé pour utilisateur non-admin")
    
    # 4. Test de création d'utilisateur via inscription publique
    print(f"\n📝 TEST CRÉATION UTILISATEUR INSCRIPTION PUBLIQUE:")
    
    client.logout()
    
    # Données de test
    user_data = {
        'username': 'test_user_public',
        'email': '<EMAIL>',
        'first_name': 'Test',
        'last_name': 'Public',
        'role': 'ENCADRANT',
        'password1': 'TestPassword123!',
        'password2': 'TestPassword123!',
    }
    
    response = client.post('/register/', user_data)
    print(f"   Status création: {response.status_code}")
    
    if response.status_code == 302:  # Redirection après succès
        # Vérifier que l'utilisateur a été créé sans privilèges admin
        try:
            new_user = User.objects.get(username='test_user_public')
            print(f"   ✅ Utilisateur créé: {new_user.get_full_name()}")
            print(f"   🔒 is_superuser: {new_user.is_superuser}")
            print(f"   🔒 is_staff: {new_user.is_staff}")
            
            if not new_user.is_superuser and not new_user.is_staff:
                print(f"   ✅ Utilisateur créé SANS privilèges admin")
            else:
                print(f"   ❌ Utilisateur créé AVEC privilèges admin")
            
            # Nettoyer
            new_user.delete()
            print(f"   🗑️ Utilisateur de test supprimé")
            
        except User.DoesNotExist:
            print(f"   ❌ Utilisateur non créé")
    
    # 5. Test de création d'utilisateur via interface admin
    print(f"\n👨‍💼 TEST CRÉATION UTILISATEUR INTERFACE ADMIN:")
    
    client.force_login(admin)
    
    # Données de test avec privilèges admin
    admin_user_data = {
        'username': 'test_user_admin',
        'email': '<EMAIL>',
        'first_name': 'Test',
        'last_name': 'Admin',
        'role': 'RH',
        'is_admin': True,  # Case admin cochée
        'password1': 'TestPassword123!',
        'password2': 'TestPassword123!',
    }
    
    response = client.post('/users/add/', admin_user_data)
    print(f"   Status création admin: {response.status_code}")
    
    if response.status_code == 302:  # Redirection après succès
        # Vérifier que l'utilisateur a été créé avec privilèges admin
        try:
            new_admin_user = User.objects.get(username='test_user_admin')
            print(f"   ✅ Utilisateur admin créé: {new_admin_user.get_full_name()}")
            print(f"   🔒 is_superuser: {new_admin_user.is_superuser}")
            print(f"   🔒 is_staff: {new_admin_user.is_staff}")
            
            if new_admin_user.is_superuser and new_admin_user.is_staff:
                print(f"   ✅ Utilisateur créé AVEC privilèges admin")
            else:
                print(f"   ❌ Utilisateur créé SANS privilèges admin")
            
            # Nettoyer
            new_admin_user.delete()
            print(f"   🗑️ Utilisateur admin de test supprimé")
            
        except User.DoesNotExist:
            print(f"   ❌ Utilisateur admin non créé")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ TEST CASE ADMIN:")
    print("")
    print("✅ FONCTIONNALITÉS TESTÉES :")
    print("   • Inscription publique sans case admin ✅")
    print("   • Interface admin avec case admin ✅")
    print("   • Contrôle d'accès à l'interface admin ✅")
    print("   • Création utilisateur sans privilèges (public) ✅")
    print("   • Création utilisateur avec privilèges (admin) ✅")
    print("")
    print("✅ SÉCURITÉ RENFORCÉE :")
    print("   • Utilisateurs publics ne peuvent pas s'auto-promouvoir admin ✅")
    print("   • Seuls les admins peuvent créer des admins ✅")
    print("   • Interface séparée pour gestion utilisateurs ✅")
    print("")
    print("🎉 CASE ADMIN CORRECTEMENT SÉCURISÉE !")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_case_admin_inscription()
