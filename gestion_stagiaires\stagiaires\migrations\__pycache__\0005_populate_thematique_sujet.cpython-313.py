�

    �oh  �                   �@   � S SK Jr  S r " S S\R                  5      rg)�    )�
migrationsc                 �  � U R                  SS5      nU R                  SS5      nUR                  R                  5       nU(       d  UR                  R                  SSSS9nUR                  R	                  SS9R                  US	9  g )
N�
stagiaires�Sujet�
Thematiqueu   Thématique par défautu2   Thématique créée automatiquement pour migrationT)�titre�description�active)�thematique__isnull)�
thematique)�	get_model�objects�first�create�filter�update)�apps�
schema_editorr   r   �default_thematiques        �XC:\Users\<USER>\gestion_stagiaires\stagiaires\migrations\0005_populate_thematique_sujet.py�set_default_thematiquer      s�   � ��N�N�<��1�E�����l�;�J� $�+�+�1�1�3���'�/�/�6�6�+�L�� 7� 
�� 
�M�M���D��1�8�8�DV�8�W�    c                   �@   � \ rS rSrS/r\R                  " \5      /rSr	g)�	Migration�   )r   �XXXX_previous_migration� N)
�__name__�
__module__�__qualname__�__firstlineno__�dependenciesr   �	RunPythonr   �
operations�__static_attributes__r   r   r   r   r      s&   � � 	2��L� 	���3�4��Jr   r   N)�	django.dbr   r   r   r   r   r   �<module>r'      s    ��  �X�$�
�$�$� r   