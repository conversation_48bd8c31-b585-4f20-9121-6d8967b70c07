#!/usr/bin/env python
import os
import sqlite3
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.conf import settings

# Chemin vers la base de données
db_path = settings.DATABASES['default']['NAME']

print(f"Connexion à la base de données : {db_path}")

# Connexion à la base de données
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Interface simple pour exécuter des commandes SQL
print("SQLite shell simple. Tapez 'exit' ou 'quit' pour quitter.")
print("Exemples de commandes :")
print("  .tables (liste les tables)")
print("  SELECT name FROM sqlite_master WHERE type='table';")

while True:
    try:
        command = input("sqlite> ")
        if command.lower() in ['exit', 'quit', '.quit', '.exit']:
            break
        elif command.lower() == '.tables':
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            for table in tables:
                print(table[0])
        else:
            cursor.execute(command)
            results = cursor.fetchall()
            for row in results:
                print(row)
    except sqlite3.Error as e:
        print(f"Erreur SQLite : {e}")
    except Exception as e:
        print(f"Erreur : {e}")

conn.close()
print("Connexion fermée.")