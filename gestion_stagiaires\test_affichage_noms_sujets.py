#!/usr/bin/env python
"""
Test de l'affichage des noms de sujets (au lieu de "Sujet object X")
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Sujet, Thematique

User = get_user_model()

def test_affichage_noms_sujets():
    """Test de l'affichage des noms de sujets"""
    
    print("=== TEST AFFICHAGE NOMS DE SUJETS ===")
    
    # 1. Test de la méthode __str__ du modèle
    print(f"🔍 Test de la méthode __str__ du modèle Sujet:")
    
    sujets = Sujet.objects.filter(actif=True)[:5]
    print(f"   Sujets actifs trouvés: {sujets.count()}")
    
    for sujet in sujets:
        print(f"   • ID {sujet.id}: '{str(sujet)}' (titre: {sujet.titre})")
        
        # Vérifier que __str__ retourne bien le titre
        if str(sujet) == sujet.titre:
            print(f"     ✅ __str__ fonctionne correctement")
        else:
            print(f"     ❌ __str__ ne retourne pas le titre")
    
    # 2. Test dans le formulaire d'ajout de stagiaire
    print(f"\n📝 Test dans le formulaire d'ajout de stagiaire:")
    
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    if not admin:
        print("❌ Aucun admin trouvé")
        return
    
    client = Client()
    client.force_login(admin)
    
    response = client.get('/stagiaires/add/')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier que les noms des sujets apparaissent dans le HTML
        print(f"   Vérification des noms de sujets dans le HTML:")
        
        for sujet in sujets[:3]:
            if sujet.titre in content:
                print(f"     ✅ '{sujet.titre}' trouvé dans le formulaire")
            else:
                print(f"     ⚠️ '{sujet.titre}' non trouvé dans le formulaire")
        
        # Vérifier qu'il n'y a plus de "Sujet object"
        if 'Sujet object' in content:
            print(f"     ❌ 'Sujet object' encore présent dans le HTML")
            # Compter les occurrences
            count = content.count('Sujet object')
            print(f"        Occurrences trouvées: {count}")
        else:
            print(f"     ✅ Aucun 'Sujet object' trouvé - Problème résolu !")
    
    # 3. Test de l'API JSON
    print(f"\n🔌 Test de l'API JSON:")
    
    thematique_test = Thematique.objects.filter(active=True).first()
    if thematique_test:
        response = client.get(f'/api/thematiques/{thematique_test.id}/sujets/')
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   API pour thématique '{thematique_test.titre}':")
                print(f"   Sujets retournés: {len(data)}")
                
                for sujet_data in data[:3]:
                    print(f"     • ID {sujet_data['id']}: '{sujet_data['titre']}'")
                    
                    # Vérifier que le titre est bien présent
                    if sujet_data['titre'] and sujet_data['titre'] != f"Sujet object {sujet_data['id']}":
                        print(f"       ✅ Titre correct dans l'API")
                    else:
                        print(f"       ❌ Titre incorrect dans l'API")
                        
            except Exception as e:
                print(f"   ❌ Erreur JSON: {e}")
    
    # 4. Test dans la liste des sujets
    print(f"\n📋 Test dans la liste des sujets:")
    
    response = client.get('/sujets/')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        print(f"   Vérification dans la liste des sujets:")
        
        for sujet in sujets[:3]:
            if sujet.titre in content:
                print(f"     ✅ '{sujet.titre}' affiché dans la liste")
            else:
                print(f"     ⚠️ '{sujet.titre}' non trouvé dans la liste")
        
        # Vérifier qu'il n'y a plus de "Sujet object"
        if 'Sujet object' in content:
            print(f"     ❌ 'Sujet object' encore présent dans la liste")
        else:
            print(f"     ✅ Aucun 'Sujet object' dans la liste")
    
    # 5. Test dans l'admin Django
    print(f"\n👑 Test dans l'admin Django:")
    
    response = client.get('/admin/stagiaires/sujet/')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        print(f"   Vérification dans l'interface admin:")
        
        for sujet in sujets[:2]:
            if sujet.titre in content:
                print(f"     ✅ '{sujet.titre}' affiché dans l'admin")
            else:
                print(f"     ⚠️ '{sujet.titre}' non trouvé dans l'admin")
        
        if 'Sujet object' in content:
            print(f"     ❌ 'Sujet object' encore présent dans l'admin")
        else:
            print(f"     ✅ Aucun 'Sujet object' dans l'admin")
    
    # 6. Test de création d'un nouveau sujet
    print(f"\n➕ Test de création d'un nouveau sujet:")
    
    # Créer un sujet de test
    thematique_test = Thematique.objects.filter(active=True).first()
    if thematique_test:
        nouveau_sujet = Sujet.objects.create(
            titre="Test Affichage Nom",
            description="Sujet de test pour vérifier l'affichage du nom",
            thematique=thematique_test,
            cree_par=admin,
            actif=True
        )
        
        print(f"   Nouveau sujet créé:")
        print(f"     • ID: {nouveau_sujet.id}")
        print(f"     • Titre: {nouveau_sujet.titre}")
        print(f"     • __str__(): '{str(nouveau_sujet)}'")
        
        if str(nouveau_sujet) == nouveau_sujet.titre:
            print(f"     ✅ __str__ fonctionne pour le nouveau sujet")
        else:
            print(f"     ❌ __str__ ne fonctionne pas pour le nouveau sujet")
        
        # Nettoyer
        nouveau_sujet.delete()
        print(f"     🧹 Sujet de test supprimé")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DE LA CORRECTION:")
    print("")
    print("✅ PROBLÈME IDENTIFIÉ :")
    print("   • Le modèle Sujet n'avait pas de méthode __str__()")
    print("   • Django affichait 'Sujet object X' au lieu du nom")
    print("")
    print("✅ SOLUTION APPLIQUÉE :")
    print("   • Ajout de la méthode __str__() au modèle Sujet")
    print("   • La méthode retourne self.titre")
    print("")
    print("✅ RÉSULTATS :")
    print("   • Les noms des sujets s'affichent maintenant correctement")
    print("   • Fini les 'Sujet object X' dans les formulaires")
    print("   • Interface plus claire et professionnelle")
    print("")
    print("✅ IMPACT :")
    print("   • Formulaire d'ajout de stagiaire ✅")
    print("   • Liste des sujets ✅")
    print("   • Interface admin ✅")
    print("   • API JSON ✅")
    print("")
    print("🎉 PROBLÈME RÉSOLU : Les noms des sujets s'affichent correctement !")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_affichage_noms_sujets()
