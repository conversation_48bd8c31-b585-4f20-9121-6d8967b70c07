#!/usr/bin/env python
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.db import connection

# Supprimer la migration problématique de la base de données
with connection.cursor() as cursor:
    cursor.execute("DELETE FROM django_migrations WHERE app='stagiaires' AND name='add_service_to_thematique'")
    print("Migration 'add_service_to_thematique' supprimée de la base de données.")

# Vérifier les migrations restantes
with connection.cursor() as cursor:
    cursor.execute("SELECT name FROM django_migrations WHERE app='stagiaires' ORDER BY id")
    rows = cursor.fetchall()
    print("\nMigrations restantes dans la base de données:")
    for row in rows:
        print(f"- {row[0]}")