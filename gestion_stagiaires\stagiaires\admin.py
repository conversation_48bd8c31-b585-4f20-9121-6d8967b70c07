from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from .models import CustomUser, Stagiaire, Tache, Mission, RapportStage, ContratStage, Thematique, Sujet, Service
from django.db import models

User = get_user_model()

@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'email', 'first_name', 'last_name', 'role', 'service', 'is_staff')
    list_filter = ('role', 'service', 'is_staff', 'is_superuser', 'is_active')
    fieldsets = UserAdmin.fieldsets + (
        ('Informations supplémentaires', {'fields': ('role', 'service')}),
    )

    def save_model(self, request, obj, form, change):
        """Validation personnalisée pour s'assurer qu'un encadrant n'a qu'un seul service"""

        # Validation spécifique pour les encadrants
        if obj.role == 'ENCADRANT' and obj.service:
            # Vérifier qu'aucun autre encadrant actif n'a déjà ce service comme service principal
            # Note: Plusieurs encadrants peuvent avoir le même service, c'est autorisé
            # La contrainte est qu'un encadrant ne peut avoir qu'UN SEUL service

            # Cette validation est déjà assurée par le modèle ForeignKey
            # Mais on peut ajouter des vérifications supplémentaires si nécessaire
            pass

        # Validation pour s'assurer qu'un utilisateur non-encadrant n'a pas de service inapproprié
        if obj.role in ['ADMIN', 'RH'] and obj.service:
            # Les admins et RH peuvent avoir un service pour les statistiques
            pass

        super().save_model(request, obj, form, change)

    def get_form(self, request, obj=None, **kwargs):
        """Personnaliser le formulaire selon le contexte"""
        form = super().get_form(request, obj, **kwargs)

        # Ajouter une aide contextuelle pour le champ service
        if 'service' in form.base_fields:
            form.base_fields['service'].help_text = (
                "Un encadrant ne peut être assigné qu'à un seul service. "
                "Plusieurs encadrants peuvent partager le même service."
            )

        return form
    add_fieldsets = UserAdmin.add_fieldsets + (
        ('Informations supplémentaires', {'fields': ('role', 'service')}),
    )


class TacheInline(admin.TabularInline):
    """Inline pour les tâches de stage"""
    model = Tache
    extra = 1
    fields = ('titre', 'date_debut', 'date_fin_prevue', 'statut', 'priorite')
    readonly_fields = ('date_creation',)


class MissionInline(admin.TabularInline):
    """Inline pour les missions"""
    model = Mission
    extra = 0
    fields = ('titre', 'date_debut_prevue', 'date_fin_prevue', 'statut', 'priorite', 'pourcentage_avancement')
    readonly_fields = ('date_creation',)


class RapportStageInline(admin.TabularInline):
    """Inline pour les rapports de stage"""
    model = RapportStage
    extra = 0
    fields = ('titre', 'mission', 'statut', 'note_rapport')
    readonly_fields = ('date_creation', 'date_soumission')


@admin.register(Stagiaire)
class StagiaireAdmin(admin.ModelAdmin):
    list_display = ('nom_complet', 'email', 'departement', 'encadrant', 'statut',
                   'statut_convention', 'statut_taches', 'date_debut', 'date_fin')
    list_filter = ('statut', 'statut_convention', 'statut_taches', 'departement',
                  'date_debut', 'date_fin')
    search_fields = ('nom', 'prenom', 'email', 'etablissement')
    readonly_fields = ('date_creation', 'cree_par', 'stage_termine', 'peut_generer_attestation')
    inlines = [TacheInline, MissionInline, RapportStageInline]

    fieldsets = (
        ('Informations personnelles', {
            'fields': ('nom', 'prenom', 'email', 'telephone', 'date_naissance')
        }),
        ('Informations du stage', {
            'fields': ('departement', 'service', 'encadrant', 'date_debut', 'date_fin', 'statut')
        }),
        ('Informations académiques', {
            'fields': ('etablissement', 'niveau_etude', 'specialite')
        }),
        ('Thématique et sujet', {
            'fields': ('thematique', 'sujet', 'duree_estimee')
        }),
        ('Technologies et compétences', {
            'fields': ('technologies',)
        }),
        ('Documents', {
            'fields': ('cv', 'assurance'),
            'classes': ('collapse',)
        }),
        ('Convention de stage', {
            'fields': ('convention_stage', 'statut_convention', 'date_validation_convention',
                      'validee_par', 'commentaire_convention')
        }),
        ('Tâches et évaluation', {
            'fields': ('description_taches', 'statut_taches', 'evaluation_encadrant', 'note_finale')
        }),
        ('Attestation', {
            'fields': ('attestation_fin_stage', 'date_generation_attestation', 'attestation_generee_par')
        }),
        ('Métadonnées', {
            'fields': ('date_creation', 'cree_par', 'stage_termine', 'peut_generer_attestation'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        # Toujours s'assurer que cree_par est rempli
        if not change:  # Nouveau stagiaire
            obj.cree_par = request.user
        elif not obj.cree_par:  # Si cree_par est vide pour une raison quelconque
            obj.cree_par = request.user

        try:
            # Sauvegarder d'abord pour éviter les erreurs de validation
            super().save_model(request, obj, form, change)

            if not change:
                try:
                    from django.contrib import messages
                    messages.success(request, f"Stagiaire {obj.nom_complet} créé avec succès!")
                except:
                    pass  # Ignorer les erreurs de messages en mode test

        except ValidationError as e:
            try:
                from django.contrib import messages
                messages.error(request, f"Erreur de validation: {e}")
            except:
                pass  # Ignorer les erreurs de messages en mode test
            raise
        except Exception as e:
            try:
                from django.contrib import messages
                messages.error(request, f"Erreur lors de la sauvegarde: {e}")
            except:
                pass  # Ignorer les erreurs de messages en mode test
            raise

    def get_form(self, request, obj=None, **kwargs):
        """Personnaliser le formulaire"""
        form = super().get_form(request, obj, **kwargs)

        # Filtrer les encadrants selon le service si applicable
        if 'encadrant' in form.base_fields:
            form.base_fields['encadrant'].queryset = User.objects.filter(
                role='ENCADRANT',
                is_active=True
            )

        return form


@admin.register(Tache)
class TacheAdmin(admin.ModelAdmin):
    list_display = ('titre', 'stagiaire', 'statut', 'priorite', 'date_debut',
                   'date_fin_prevue', 'est_en_retard')
    list_filter = ('statut', 'priorite', 'date_debut', 'date_fin_prevue')
    search_fields = ('titre', 'description', 'stagiaire__nom', 'stagiaire__prenom')
    readonly_fields = ('date_creation', 'calculer_duree_prevue', 'calculer_duree_reelle', 'est_en_retard')
    
    def est_en_retard(self, obj):
        """Vérifie si la tâche est en retard"""
        if obj.statut in ['TERMINEE', 'ANNULEE']:
            return False
        if not obj.date_fin_prevue:
            return False
        from django.utils import timezone
        return obj.date_fin_prevue < timezone.now().date()
    est_en_retard.short_description = "En retard"
    est_en_retard.boolean = True
    
    def calculer_duree_prevue(self, obj):
        """Calcule la durée prévue en jours"""
        if not obj.date_debut or not obj.date_fin_prevue:
            return "Non définie"
        duree = (obj.date_fin_prevue - obj.date_debut).days
        return f"{duree} jours"
    calculer_duree_prevue.short_description = "Durée prévue"
    
    def calculer_duree_reelle(self, obj):
        """Calcule la durée réelle en jours"""
        if not obj.date_debut or not obj.date_fin_reelle:
            return "Non terminée"
        duree = (obj.date_fin_reelle - obj.date_debut).days
        return f"{duree} jours"
    calculer_duree_reelle.short_description = "Durée réelle"

    fieldsets = (
        ('Informations générales', {
            'fields': ('stagiaire', 'titre', 'description', 'priorite')
        }),
        ('Planification', {
            'fields': ('date_debut', 'date_fin_prevue', 'date_debut_reelle', 'date_fin_reelle')
        }),
        ('Suivi', {
            'fields': ('statut', 'commentaire_stagiaire', 'commentaire_encadrant', 'note')
        }),
        ('Métadonnées', {
            'fields': ('date_creation', 'creee_par', 'duree_prevue', 'duree_reelle', 'en_retard'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Nouvelle tâche
            obj.creee_par = request.user
        super().save_model(request, obj, form, change)


@admin.register(Mission)
class MissionAdmin(admin.ModelAdmin):
    list_display = ('titre', 'stagiaire', 'statut', 'priorite', 'pourcentage_avancement',
                   'date_debut_prevue', 'date_fin_prevue', 'en_retard')
    list_filter = ('statut', 'priorite', 'date_debut_prevue', 'date_fin_prevue', 'creee_par')
    search_fields = ('titre', 'description', 'stagiaire__nom', 'stagiaire__prenom')
    readonly_fields = ('date_creation', 'duree_prevue', 'duree_reelle', 'en_retard', 'derniere_mise_a_jour')

    fieldsets = (
        ('Informations générales', {
            'fields': ('stagiaire', 'titre', 'description', 'priorite', 'creee_par')
        }),
        ('Objectifs et livrables', {
            'fields': ('objectifs', 'livrables_attendus')
        }),
        ('Planification', {
            'fields': ('date_debut_prevue', 'date_fin_prevue', 'date_debut_reelle', 'date_fin_reelle')
        }),
        ('Suivi', {
            'fields': ('statut', 'pourcentage_avancement', 'commentaire_avancement')
        }),
        ('Métadonnées', {
            'fields': ('date_creation', 'derniere_mise_a_jour', 'duree_prevue', 'duree_reelle', 'en_retard'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Nouvelle mission
            obj.creee_par = request.user
        super().save_model(request, obj, form, change)


@admin.register(RapportStage)
class RapportStageAdmin(admin.ModelAdmin):
    list_display = ('titre', 'stagiaire', 'mission', 'statut', 'note_rapport',
                   'date_soumission', 'valide_par')
    list_filter = ('statut', 'date_soumission', 'date_validation', 'valide_par')
    search_fields = ('titre', 'description', 'stagiaire__nom', 'stagiaire__prenom')
    readonly_fields = ('date_creation', 'date_soumission', 'date_validation')

    fieldsets = (
        ('Informations générales', {
            'fields': ('stagiaire', 'mission', 'titre', 'description')
        }),
        ('Fichier', {
            'fields': ('fichier_rapport',)
        }),
        ('Statut et validation', {
            'fields': ('statut', 'valide_par', 'date_validation', 'note_rapport', 'commentaires_validation')
        }),
        ('Métadonnées', {
            'fields': ('date_creation', 'date_soumission'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ContratStage)
class ContratStageAdmin(admin.ModelAdmin):
    list_display = ('reference', 'stagiaire', 'type_contrat', 'statut', 'pourcentage_signatures',
                   'date_creation', 'cree_par')
    list_filter = ('statut', 'type_contrat', 'date_creation', 'signature_rh')
    search_fields = ('reference', 'stagiaire__nom', 'stagiaire__prenom', 'titre_stage')
    readonly_fields = ('reference', 'date_creation', 'date_modification', 'pourcentage_signatures',
                      'signatures_manquantes', 'est_expire')

    fieldsets = (
        ('Informations générales', {
            'fields': ('reference', 'stagiaire', 'type_contrat', 'statut')
        }),
        ('Contenu du contrat', {
            'fields': ('titre_stage', 'description_missions', 'objectifs_pedagogiques', 'competences_acquises')
        }),
        ('Conditions du stage', {
            'fields': ('duree_hebdomadaire', 'gratification_mensuelle', 'avantages')
        }),
        ('Encadrement', {
            'fields': ('encadrant_entreprise', 'tuteur_pedagogique')
        }),
        ('Signature électronique RH', {
            'fields': (
                ('signature_rh', 'date_signature_rh', 'signature_rh_par'),
                'pourcentage_signatures', 'signatures_manquantes'
            )
        }),
        ('Documents', {
            'fields': ('document_contrat', 'document_signe')
        }),
        ('Métadonnées', {
            'fields': ('date_creation', 'cree_par', 'date_modification', 'date_expiration', 'est_expire'),
            'classes': ('collapse',)
        }),
        ('Commentaires', {
            'fields': ('commentaires_admin', 'notes_internes'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Nouveau contrat
            obj.cree_par = request.user
        super().save_model(request, obj, form, change)


@admin.register(Thematique)
class ThematiqueAdmin(admin.ModelAdmin):
    list_display = ('titre', 'active', 'get_nombre_sujets', 'cree_par', 'date_creation')
    list_filter = ('active',)
    search_fields = ('titre', 'description')
    readonly_fields = ('date_creation', 'date_modification', 'cree_par')
    
    def get_nombre_sujets(self, obj):
        return obj.sujets.count()
    get_nombre_sujets.short_description = "Nombre de sujets"
    
    def save_model(self, request, obj, form, change):
        if not change:  # Si c'est une création
            obj.cree_par = request.user
        super().save_model(request, obj, form, change)


@admin.register(Sujet)
class SujetAdmin(admin.ModelAdmin):
    list_display = ('titre', 'thematique', 'niveau_difficulte', 'actif', 'service', 'cree_par', 'date_creation')
    list_filter = ('actif', 'niveau_difficulte', 'thematique', 'service')
    search_fields = ('titre', 'description')
    readonly_fields = ('date_creation', 'date_modification', 'cree_par')
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        # Si l'utilisateur est un encadrant, filtrer par son service
        if hasattr(request.user, 'role') and request.user.role == 'ENCADRANT' and request.user.service:
            return qs.filter(service=request.user.service)
        return qs
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        # Filtrer les thématiques par service pour les encadrants
        if db_field.name == "thematique" and hasattr(request.user, 'role') and request.user.role == 'ENCADRANT' and request.user.service:
            kwargs["queryset"] = Thematique.objects.filter(
                models.Q(service=request.user.service) | models.Q(service__isnull=True)
            )
        # Filtrer les services pour les encadrants
        if db_field.name == "service" and hasattr(request.user, 'role') and request.user.role == 'ENCADRANT' and request.user.service:
            kwargs["queryset"] = Service.objects.filter(id=request.user.service.id)
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def save_model(self, request, obj, form, change):
        if not change:  # Si c'est une création
            obj.cree_par = request.user
            # Si l'encadrant crée un sujet, associer automatiquement son service
            if hasattr(request.user, 'role') and request.user.role == 'ENCADRANT' and request.user.service and not obj.service:
                obj.service = request.user.service
        super().save_model(request, obj, form, change)


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ('nom', 'code_service', 'responsable', 'actif', 'date_creation')
    list_filter = ('actif',)
    search_fields = ('nom', 'code_service', 'description')
    readonly_fields = ('date_creation', 'date_modification', 'cree_par')
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        # Si l'utilisateur est un encadrant, filtrer par son service
        if hasattr(request.user, 'role') and request.user.role == 'ENCADRANT' and request.user.service:
            return qs.filter(id=request.user.service.id)
        return qs
    
    def has_add_permission(self, request):
        # Les administrateurs et RH peuvent ajouter des services
        return hasattr(request.user, 'role') and request.user.role in ['ADMIN', 'RH']
    
    def has_change_permission(self, request, obj=None):
        # Les encadrants ne peuvent modifier que leur propre service
        if hasattr(request.user, 'role'):
            if request.user.role == 'ADMIN':
                return True
            if request.user.role == 'ENCADRANT' and obj and request.user.service and obj.id == request.user.service.id:
                return True
        return False
    
    def has_delete_permission(self, request, obj=None):
        # Seuls les administrateurs peuvent supprimer des services
        return hasattr(request.user, 'role') and request.user.role == 'ADMIN'
    
    def save_model(self, request, obj, form, change):
        if not change:  # Si c'est une création
            obj.cree_par = request.user
        super().save_model(request, obj, form, change)


# Register your models here.
