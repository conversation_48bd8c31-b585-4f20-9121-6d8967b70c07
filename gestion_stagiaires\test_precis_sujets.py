#!/usr/bin/env python
"""
Test précis de la vue sujets pour vérifier le filtrage
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Sujet

User = get_user_model()

def test_precis_sujets():
    """Test précis de la vue sujets"""
    
    print("=== TEST PRÉCIS VUE SUJETS ===")
    
    # Récupérer l'encadrant Marketing
    encadrant_marketing = User.objects.filter(
        role='ENCADRANT',
        service__nom__icontains='marketing'
    ).first()
    
    print(f"👨‍💼 Encadrant: {encadrant_marketing.get_full_name()}")
    print(f"🏢 Service: {encadrant_marketing.service.nom}")
    
    # Test avec le client Django
    client = Client()
    client.force_login(encadrant_marketing)
    
    print(f"\n🌐 TEST REQUÊTE HTTP:")
    response = client.get('/sujets/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        # Récupérer le contexte de la réponse
        context = response.context
        if context:
            sujets_context = context.get('sujets', [])
            print(f"   Sujets dans le contexte: {len(sujets_context) if sujets_context else 0}")

            if sujets_context:
                print(f"\n📝 SUJETS DANS LE CONTEXTE:")
                for sujet in sujets_context:
                    print(f"      • {sujet.titre} (Service: {sujet.service.nom if sujet.service else 'Aucun'})")
        else:
            print(f"   ⚠️ Pas de contexte disponible")

        # Vérifier le contenu HTML
        content = response.content.decode('utf-8')
        
        print(f"\n🔍 ANALYSE CONTENU HTML:")
        
        # Chercher spécifiquement le sujet "js"
        if 'js' in content.lower():
            print(f"   ⚠️ Texte 'js' trouvé dans le HTML")
            
            # Chercher les occurrences
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'js' in line.lower():
                    print(f"      Ligne {i+1}: {line.strip()}")
        else:
            print(f"   ✅ Texte 'js' PAS trouvé dans le HTML")
        
        # Vérifier les sujets Marketing spécifiquement
        sujets_marketing = ['Segmentation de marché', 'Analyse de la concurrence', 'Étude de satisfaction client']
        
        print(f"\n📋 VÉRIFICATION SUJETS MARKETING:")
        for sujet_titre in sujets_marketing:
            if sujet_titre in content:
                print(f"   ✅ {sujet_titre} trouvé")
            else:
                print(f"   ❌ {sujet_titre} non trouvé")
        
        # Vérifier les sujets Informatique
        sujets_informatique = ['java', 'html css', 'application']
        
        print(f"\n💻 VÉRIFICATION SUJETS INFORMATIQUE:")
        for sujet_titre in sujets_informatique:
            if sujet_titre in content:
                print(f"   ⚠️ {sujet_titre} trouvé - NE DEVRAIT PAS")
            else:
                print(f"   ✅ {sujet_titre} non trouvé")
    
    # Test avec un encadrant Informatique pour comparaison
    print(f"\n💻 TEST AVEC ENCADRANT INFORMATIQUE:")
    
    encadrant_informatique = User.objects.filter(
        role='ENCADRANT',
        service__nom__icontains='informatique'
    ).first()
    
    if encadrant_informatique:
        print(f"   👨‍💼 Encadrant: {encadrant_informatique.get_full_name()}")
        
        client.force_login(encadrant_informatique)
        response = client.get('/sujets/')
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # Vérifier que le sujet "js" est bien présent pour l'encadrant Informatique
            if 'js' in content.lower():
                print(f"   ✅ Sujet 'js' trouvé pour encadrant Informatique")
            else:
                print(f"   ❌ Sujet 'js' non trouvé pour encadrant Informatique")
    
    print(f"\n🎉 TEST PRÉCIS TERMINÉ !")

if __name__ == '__main__':
    test_precis_sujets()
