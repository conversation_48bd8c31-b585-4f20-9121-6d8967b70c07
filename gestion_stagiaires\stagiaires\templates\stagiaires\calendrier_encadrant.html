{% extends 'stagiaires/base.html' %}

{% block title %}{{ title }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <!-- En-tête avec navigation -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Calendrier des Stages
                            </h3>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="btn-group" role="group">
                                <a href="?year={{ prev_year }}&month={{ prev_month }}" class="btn btn-outline-light">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                                <span class="btn btn-light disabled">
                                    {{ month_name }} {{ year }}
                                </span>
                                <a href="?year={{ next_year }}&month={{ next_month }}" class="btn btn-outline-light">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{% url 'stagiaires_list' %}" class="btn btn-outline-light">
                                <i class="fas fa-list me-1"></i>
                                Liste des Stagiaires
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Légende simple -->
            {% if stagiaires_data %}
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">📋 Stagiaires du mois</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for data in stagiaires_data %}
                        <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                                <div class="me-3" style="width: 25px; height: 25px; background-color: {{ data.color }}; border-radius: 4px;"></div>
                                <div>
                                    <strong>{{ data.stagiaire.nom_complet }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        {{ data.stagiaire.date_debut|date:"d/m" }} au {{ data.stagiaire.date_fin|date:"d/m/Y" }}
                                        ({{ data.duree_semaines }} semaines)
                                    </small>
                                    {% if data.stagiaire.sujet %}
                                    <br><small class="text-primary">{{ data.stagiaire.sujet.titre|truncatechars:40 }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Calendrier principal simplifié -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">📅 Calendrier - {{ month_name }} {{ year }}</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered mb-0">
                            <!-- En-tête des jours -->
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 150px;" class="text-center">Stagiaire</th>
                                    <th class="text-center">Lun</th>
                                    <th class="text-center">Mar</th>
                                    <th class="text-center">Mer</th>
                                    <th class="text-center">Jeu</th>
                                    <th class="text-center">Ven</th>
                                    <th class="text-center bg-secondary">Sam</th>
                                    <th class="text-center bg-secondary">Dim</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Ligne des dates pour chaque semaine -->
                                {% for semaine in semaines %}
                                <tr class="table-light">
                                    <td class="text-center fw-bold bg-light">
                                        Semaine {{ forloop.counter }}
                                    </td>
                                    {% for jour in semaine %}
                                    <td class="text-center {% if jour.is_today %}bg-warning{% endif %} {% if jour.is_weekend %}bg-light{% endif %}">
                                        {% if jour %}
                                            <strong>{{ jour.day }}</strong>
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>

                                <!-- Ligne pour chaque stagiaire dans cette semaine -->
                                {% for data in stagiaires_data %}
                                <tr>
                                    <td class="text-center align-middle" style="background-color: {{ data.color }}15;">
                                        <strong>{{ data.stagiaire.nom_complet }}</strong>
                                        <br><small class="text-muted">{{ data.duree_semaines }} semaines</small>
                                    </td>

                                    {% for jour in semaine %}
                                    <td class="text-center align-middle {% if jour.is_weekend %}bg-light{% endif %}" style="height: 50px;">
                                        {% if jour %}
                                            <!-- Vérifier si ce jour fait partie de la période du stagiaire -->
                                            {% for periode in data.periodes %}
                                                {% if periode.week == forloop.parentloop.counter0 and periode.day == forloop.counter0 %}
                                                    <div class="w-100 h-100 d-flex align-items-center justify-content-center"
                                                         style="background-color: {{ data.color }}; color: white; border-radius: 4px; font-weight: bold;">
                                                        {{ jour.day }}
                                                        {% if periode.date == data.stagiaire.date_debut %}
                                                            <small class="ms-1">🟢</small>
                                                        {% endif %}
                                                        {% if periode.date == data.stagiaire.date_fin %}
                                                            <small class="ms-1">🔴</small>
                                                        {% endif %}
                                                    </div>
                                                {% endif %}
                                            {% endfor %}
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}

                                <!-- Ligne de séparation entre les semaines -->
                                {% if not forloop.last %}
                                <tr><td colspan="8" class="p-1 bg-secondary"></td></tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Message si aucun stagiaire -->
            {% if not stagiaires_data %}
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucun stage en cours</h4>
                    <p class="text-muted">
                        Aucun stagiaire n'a de stage prévu pour {{ month_name }} {{ year }}.
                    </p>
                    <a href="{% url 'add_stagiaire' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        Ajouter un Stagiaire
                    </a>
                </div>
            </div>
            {% endif %}

            <!-- Informations supplémentaires -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Statistiques du Mois
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="fw-bold text-primary fs-4">{{ stagiaires_data|length }}</div>
                                    <small class="text-muted">Stagiaires</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-success fs-4">{{ month_name }}</div>
                                    <small class="text-muted">Mois Actuel</small>
                                </div>
                                <div class="col-4">
                                    <div class="fw-bold text-info fs-4">{{ year }}</div>
                                    <small class="text-muted">Année</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Navigation Rapide
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <a href="?year={{ year }}&month=1" class="btn btn-outline-primary btn-sm w-100 mb-2">Janvier</a>
                                    <a href="?year={{ year }}&month=4" class="btn btn-outline-primary btn-sm w-100 mb-2">Avril</a>
                                    <a href="?year={{ year }}&month=7" class="btn btn-outline-primary btn-sm w-100 mb-2">Juillet</a>
                                    <a href="?year={{ year }}&month=10" class="btn btn-outline-primary btn-sm w-100">Octobre</a>
                                </div>
                                <div class="col-6">
                                    <a href="?year={{ year|add:-1 }}&month={{ month }}" class="btn btn-outline-secondary btn-sm w-100 mb-2">{{ year|add:-1 }}</a>
                                    <a href="?year={{ year }}&month={{ month }}" class="btn btn-primary btn-sm w-100 mb-2">{{ year }}</a>
                                    <a href="?year={{ year|add:1 }}&month={{ month }}" class="btn btn-outline-secondary btn-sm w-100">{{ year|add:1 }}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Styles simples pour le calendrier */
.table {
    font-size: 0.9rem;
}

.table td, .table th {
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 8px;
}

.table-responsive {
    overflow-x: auto;
}

/* Cellules de stage */
.table td div[style*="background-color"] {
    border-radius: 4px;
    min-height: 40px;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Weekends */
.bg-light {
    background-color: #f8f9fa !important;
}

/* Aujourd'hui */
.bg-warning {
    background-color: #fff3cd !important;
    font-weight: bold;
}

/* Responsive */
@media (max-width: 768px) {
    .table {
        font-size: 0.8rem;
    }

    .table td, .table th {
        padding: 4px;
    }

    .table td div[style*="background-color"] {
        min-height: 30px;
        font-size: 0.7rem;
    }
}

@media (max-width: 576px) {
    .table td:first-child {
        min-width: 120px;
        font-size: 0.7rem;
    }
}
</style>
{% endblock %}
