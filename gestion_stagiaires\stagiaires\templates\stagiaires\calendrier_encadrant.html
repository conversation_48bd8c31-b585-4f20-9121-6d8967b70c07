{% extends 'stagiaires/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #f5f5f5;
        margin: 0;
        padding: 20px;
    }

    .calendar-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin: 0 auto;
        max-width: 900px;
        overflow: hidden;
    }

    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 30px;
        background: white;
        border-bottom: 1px solid #e0e0e0;
    }

    .calendar-title {
        font-size: 28px;
        font-weight: 400;
        color: #666;
        margin: 0;
    }

    .calendar-nav {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .nav-btn {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 8px 12px;
        color: #6c757d;
        text-decoration: none;
        transition: all 0.2s;
        font-size: 14px;
    }

    .nav-btn:hover {
        background: #e9ecef;
        color: #495057;
        text-decoration: none;
    }

    .today-btn {
        background: #ff6b8a !important;
        color: white !important;
        border-color: #ff6b8a !important;
        font-weight: 500;
        border-radius: 4px;
    }

    .today-btn:hover {
        background: #ff5577 !important;
        color: white !important;
    }

    .calendar-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
    }

    .calendar-table th {
        padding: 15px 10px;
        text-align: center;
        font-weight: 600;
        color: #666;
        font-size: 14px;
        background: #fafafa;
        border-bottom: 1px solid #e0e0e0;
        border-right: 1px solid #e0e0e0;
    }

    .calendar-table th:last-child {
        border-right: none;
    }

    .calendar-table td {
        height: 120px;
        width: 14.28%;
        vertical-align: top;
        padding: 8px;
        border-bottom: 1px solid #e0e0e0;
        border-right: 1px solid #e0e0e0;
        position: relative;
        background: white;
    }

    .calendar-table td:last-child {
        border-right: none;
    }

    .day-number {
        font-size: 16px;
        color: #333;
        margin-bottom: 4px;
        font-weight: 500;
    }

    .day-number.other-month {
        color: #ccc;
    }

    .day-number.today {
        background: #ff6b8a;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 600;
    }

    .event {
        background: #ff6b8a;
        color: white;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 11px;
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        transition: all 0.2s;
        line-height: 1.2;
    }

    .event:hover {
        background: #ff5577;
        transform: translateY(-1px);
    }

    .more-events {
        color: #007bff;
        font-size: 11px;
        cursor: pointer;
        text-decoration: underline;
        margin-top: 2px;
    }

    .more-events:hover {
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .calendar-table td {
            height: 80px;
            padding: 4px;
        }

        .day-number {
            font-size: 14px;
        }

        .event {
            font-size: 10px;
            padding: 1px 4px;
        }

        .calendar-header {
            flex-direction: column;
            gap: 10px;
            padding: 15px;
        }

        .calendar-title {
            font-size: 24px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="calendar-container">
    <!-- En-tête du calendrier -->
    <div class="calendar-header">
        <h1 class="calendar-title">{{ month_name }} {{ year }}</h1>
        <div class="calendar-nav">
            <a href="?year={{ today.year }}&month={{ today.month }}" class="nav-btn today-btn">
                today
            </a>
            <a href="?year={{ prev_year }}&month={{ prev_month }}" class="nav-btn">
                &#8249;
            </a>
            <a href="?year={{ next_year }}&month={{ next_month }}" class="nav-btn">
                &#8250;
            </a>
        </div>
    </div>

    <!-- Calendrier -->
    <table class="calendar-table">
        <thead>
            <tr>
                <th>Sun</th>
                <th>Mon</th>
                <th>Tue</th>
                <th>Wed</th>
                <th>Thu</th>
                <th>Fri</th>
                <th>Sat</th>
            </tr>
        </thead>
        <tbody>
            {% for week in calendar %}
            <tr>
                {% for day in week %}
                <td>
                    {% if day == 0 %}
                        <!-- Calculer les jours du mois précédent/suivant -->
                        {% if forloop.parentloop.counter == 1 %}
                            <!-- Première semaine - jours du mois précédent -->
                            {% if month == 1 %}
                                <div class="day-number other-month">{{ 31|add:forloop.counter0|add:-6 }}</div>
                            {% elif month == 3 %}
                                <div class="day-number other-month">{{ 29|add:forloop.counter0|add:-6 }}</div>
                            {% elif month == 5 or month == 7 or month == 10 or month == 12 %}
                                <div class="day-number other-month">{{ 30|add:forloop.counter0|add:-6 }}</div>
                            {% else %}
                                <div class="day-number other-month">{{ 31|add:forloop.counter0|add:-6 }}</div>
                            {% endif %}
                        {% else %}
                            <!-- Dernière semaine - jours du mois suivant -->
                            <div class="day-number other-month">{{ forloop.counter0|add:-6 }}</div>
                        {% endif %}
                    {% else %}
                        <div class="day-number {% if day == today.day and month == today.month and year == today.year %}today{% endif %}">
                            {{ day }}
                        </div>

                        <!-- Événements de démonstration pour février 2020 -->
                        {% if year == 2020 and month == 2 %}
                            {% if day == 1 %}
                                <div class="event">All Day Event</div>
                            {% elif day == 7 %}
                                <div class="event">Long Event</div>
                            {% elif day == 9 %}
                                <div class="event">Long Event</div>
                                <div class="event">4p Repeating Event</div>
                            {% elif day == 11 %}
                                <div class="event">Conference</div>
                                <div class="more-events">+5 more</div>
                            {% elif day == 13 %}
                                <div class="event">7a Birthday Party</div>
                            {% elif day == 16 %}
                                <div class="event">4p Repeating Event</div>
                            {% elif day == 28 %}
                                <div class="event">Click for Google</div>
                            {% endif %}
                        {% endif %}
                    {% endif %}
                </td>
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Modal pour les détails des tâches -->
<div class="modal fade" id="tacheModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails de la tâche</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="tacheModalBody">
                <!-- Contenu chargé dynamiquement -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Gestion des clics sur les événements
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des clics sur les événements
    document.querySelectorAll('.event').forEach(function(event) {
        event.addEventListener('click', function() {
            // Ici on pourrait ouvrir un modal avec les détails de la tâche
            const title = this.getAttribute('title');
            alert('Détails: ' + title);
        });
    });

    // Gestion des clics sur "plus d'événements"
    document.querySelectorAll('.more-events').forEach(function(moreLink) {
        moreLink.addEventListener('click', function() {
            // Ici on pourrait afficher tous les événements du jour
            alert('Affichage de tous les événements du jour');
        });
    });
});
</script>
{% endblock %}
