{% extends 'stagiaires/base.html' %}

{% block title %}Modifier le Stagiaire - {{ stagiaire.nom_complet }}{% endblock %}

{% block extra_css %}
<style>
    .tech-suggestions .badge:hover {
        background-color: #e9ecef !important;
    }
    
    .tech-badge {
        display: inline-block;
        padding: 0.25em 0.6em;
        font-size: 0.85em;
        font-weight: 600;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
        background-color: #f8f9fa;
        color: #212529;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        Modifier le Stagiaire : {{ stagiaire.nom_complet }}
                    </h3>
                    <div>
                        <a href="{% url 'stagiaire_detail' stagiaire.id %}" class="btn btn-light btn-sm me-2">
                            <i class="fas fa-eye me-1"></i>Voir le profil
                        </a>
                        <a href="{% url 'stagiaires_list' %}" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" id="stagiaireForm" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Messages d'erreur -->
                        {% if form.errors %}
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Erreurs de validation :</h6>
                            <ul class="mb-0">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>{{ field|capfirst }} : {{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <div class="row">
                            <!-- Informations personnelles -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>Informations Personnelles
                                </h5>
                                
                                <div class="mb-3">
                                    <label for="{{ form.nom.id_for_label }}" class="form-label">
                                        <i class="fas fa-signature me-1"></i>Nom <span class="text-danger">*</span>
                                    </label>
                                    {{ form.nom }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.prenom.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-1"></i>Prénom <span class="text-danger">*</span>
                                    </label>
                                    {{ form.prenom }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>Email <span class="text-danger">*</span>
                                    </label>
                                    {{ form.email }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.telephone.id_for_label }}" class="form-label">
                                        <i class="fas fa-phone me-1"></i>Téléphone
                                    </label>
                                    {{ form.telephone }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.date_naissance.id_for_label }}" class="form-label">
                                        <i class="fas fa-birthday-cake me-1"></i>Date de naissance
                                    </label>
                                    {{ form.date_naissance }}
                                </div>
                            </div>

                            <!-- Informations académiques -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-graduation-cap me-2"></i>Informations Académiques
                                </h5>

                                <div class="mb-3">
                                    <label for="{{ form.etablissement.id_for_label }}" class="form-label">
                                        <i class="fas fa-university me-1"></i>Établissement
                                    </label>
                                    {{ form.etablissement }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.niveau_etude.id_for_label }}" class="form-label">
                                        <i class="fas fa-level-up-alt me-1"></i>Niveau d'étude
                                    </label>
                                    {{ form.niveau_etude }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.specialite.id_for_label }}" class="form-label">
                                        <i class="fas fa-cogs me-1"></i>Spécialité
                                    </label>
                                    {{ form.specialite }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.departement.id_for_label }}" class="form-label">
                                        <i class="fas fa-building me-1"></i>Département <span class="text-danger">*</span>
                                    </label>
                                    {{ form.departement }}
                                </div>

                                {% if form.service %}
                                <div class="mb-3">
                                    <label for="{{ form.service.id_for_label }}" class="form-label">
                                        <i class="fas fa-sitemap me-1"></i>Service
                                    </label>
                                    {{ form.service }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="row">
                            <!-- Informations de stage -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-briefcase me-2"></i>Informations de Stage
                                </h5>

                                <div class="mb-3">
                                    <label for="{{ form.encadrant.id_for_label }}" class="form-label">
                                        <i class="fas fa-user-tie me-1"></i>Encadrant
                                    </label>
                                    {{ form.encadrant }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.date_debut.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-plus me-1"></i>Date de début <span class="text-danger">*</span>
                                    </label>
                                    {{ form.date_debut }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.date_fin.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-minus me-1"></i>Date de fin <span class="text-danger">*</span>
                                    </label>
                                    {{ form.date_fin }}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.statut.id_for_label }}" class="form-label">
                                        <i class="fas fa-info-circle me-1"></i>Statut
                                    </label>
                                    {{ form.statut }}
                                </div>
                            </div>

                            <!-- Informations techniques -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-code me-2"></i>Informations Techniques
                                </h5>

                                {% if form.thematique %}
                                <div class="mb-3">
                                    <label for="{{ form.thematique.id_for_label }}" class="form-label">
                                        <i class="fas fa-tags me-1"></i>Thématique
                                    </label>
                                    {{ form.thematique }}
                                </div>
                                {% endif %}

                                {% if form.sujet %}
                                <div class="mb-3">
                                    <label for="{{ form.sujet.id_for_label }}" class="form-label">
                                        <i class="fas fa-lightbulb me-1"></i>Sujet
                                    </label>
                                    {{ form.sujet }}
                                </div>
                                {% endif %}

                                <div class="mb-3">
                                    <label for="{{ form.technologies.id_for_label }}" class="form-label">
                                        <i class="fas fa-laptop-code me-1"></i>Technologies
                                    </label>
                                    {{ form.technologies }}
                                    <div class="form-text">Séparez les technologies par des virgules</div>
                                </div>

                                {% if form.description_taches %}
                                <div class="mb-3">
                                    <label for="{{ form.description_taches.id_for_label }}" class="form-label">
                                        <i class="fas fa-tasks me-1"></i>Description des tâches
                                    </label>
                                    {{ form.description_taches }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Documents (si RH) -->
                        {% if user.role == 'RH' %}
                        <hr class="my-4">
                        <div class="row">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-file-alt me-2"></i>Documents
                                </h5>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="{{ form.cv.id_for_label }}" class="form-label">
                                                <i class="fas fa-file-pdf me-1"></i>CV
                                            </label>
                                            {{ form.cv }}
                                            {% if stagiaire.cv %}
                                            <div class="form-text">
                                                <a href="{{ stagiaire.cv.url }}" target="_blank" class="text-primary">
                                                    <i class="fas fa-download me-1"></i>Télécharger le CV actuel
                                                </a>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="{{ form.convention_stage.id_for_label }}" class="form-label">
                                                <i class="fas fa-file-contract me-1"></i>Convention de stage
                                            </label>
                                            {{ form.convention_stage }}
                                            {% if stagiaire.convention_stage %}
                                            <div class="form-text">
                                                <a href="{{ stagiaire.convention_stage.url }}" target="_blank" class="text-primary">
                                                    <i class="fas fa-download me-1"></i>Télécharger la convention actuelle
                                                </a>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="{{ form.assurance.id_for_label }}" class="form-label">
                                                <i class="fas fa-shield-alt me-1"></i>Assurance
                                            </label>
                                            {{ form.assurance }}
                                            {% if stagiaire.assurance %}
                                            <div class="form-text">
                                                <a href="{{ stagiaire.assurance.url }}" target="_blank" class="text-primary">
                                                    <i class="fas fa-download me-1"></i>Télécharger l'assurance actuelle
                                                </a>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Section Rapport de Stage -->
                        <hr class="my-4">
                        <div class="row">
                            <div class="col-12">
                                <h5 class="text-success mb-3">
                                    <i class="fas fa-file-upload me-2"></i>Rapport de Stage
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.rapport_stage.id_for_label }}" class="form-label">
                                                <i class="fas fa-file-pdf me-1"></i>Fichier du rapport
                                            </label>
                                            {{ form.rapport_stage }}
                                            {% if stagiaire.rapport_stage %}
                                            <div class="form-text">
                                                <a href="{{ stagiaire.rapport_stage.url }}" target="_blank" class="text-success">
                                                    <i class="fas fa-download me-1"></i>Télécharger le rapport actuel
                                                </a>
                                                <br>
                                                <small class="text-muted">
                                                    Uploadé le {{ stagiaire.date_upload_rapport|date:"d/m/Y à H:i" }}
                                                    {% if stagiaire.rapport_uploade_par %}
                                                        par {{ stagiaire.rapport_uploade_par.get_full_name }}
                                                    {% endif %}
                                                </small>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.commentaire_rapport.id_for_label }}" class="form-label">
                                                <i class="fas fa-comment me-1"></i>Commentaire sur le rapport
                                            </label>
                                            {{ form.commentaire_rapport }}
                                            <div class="form-text">Commentaires ou observations sur le rapport de stage</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Suivi et Évaluation (pour les encadrants) -->
                        {% if user.role == 'ENCADRANT' or user.role == 'RH' or user.role == 'ADMIN' %}
                        <hr class="my-4">
                        <div class="row">
                            <div class="col-12">
                                <h5 class="text-warning mb-3">
                                    <i class="fas fa-chart-line me-2"></i>Suivi et Évaluation
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.description_taches.id_for_label }}" class="form-label">
                                                <i class="fas fa-tasks me-1"></i>Description des tâches
                                            </label>
                                            {{ form.description_taches }}
                                        </div>

                                        <div class="mb-3">
                                            <label for="{{ form.statut_taches.id_for_label }}" class="form-label">
                                                <i class="fas fa-check-circle me-1"></i>Statut des tâches
                                            </label>
                                            {{ form.statut_taches }}
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.evaluation_encadrant.id_for_label }}" class="form-label">
                                                <i class="fas fa-star me-1"></i>Évaluation de l'encadrant
                                            </label>
                                            {{ form.evaluation_encadrant }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div>
                                <a href="{% url 'stagiaires_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>Annuler
                                </a>
                            </div>
                            <div>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>Enregistrer les modifications
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Ajouter les classes Bootstrap aux champs du formulaire
    const formFields = document.querySelectorAll('input, select, textarea');
    formFields.forEach(field => {
        if (!field.classList.contains('btn')) {
            field.classList.add('form-control');
        }
    });

    // Gestion de la soumission du formulaire
    const form = document.getElementById('stagiaireForm');
    if (form) {
        form.addEventListener('submit', function() {
            // Afficher un message de chargement
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Enregistrement en cours...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>
{% endblock %}
