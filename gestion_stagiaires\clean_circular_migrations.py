#!/usr/bin/env python
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.db import connection

# Liste des migrations problématiques
problematic_migrations = [
    '0019_merge_20250710_1552',
    '0019_service_updates_and_thematique_service',
    '0025_alter_thematique_options_and_more',
    '0026_merge_migrations',
    'manual_add_service_to_customuser',
    'XXXX_add_service_to_customuser'
]

# Supprimer toutes les migrations problématiques de la base de données
with connection.cursor() as cursor:
    for migration in problematic_migrations:
        cursor.execute(f"DELETE FROM django_migrations WHERE app='stagiaires' AND name='{migration}'")
        print(f"Migration '{migration}' supprimée de la base de données (si elle existait).")

# Vérifier les migrations restantes
with connection.cursor() as cursor:
    cursor.execute("SELECT name FROM django_migrations WHERE app='stagiaires' ORDER BY id")
    rows = cursor.fetchall()
    print("\nMigrations restantes dans la base de données:")
    for row in rows:
        print(f"- {row[0]}")

print("\nScript terminé. Vous pouvez maintenant essayer de recréer vos migrations.")
