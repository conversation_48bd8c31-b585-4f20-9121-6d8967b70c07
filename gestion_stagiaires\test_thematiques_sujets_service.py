#!/usr/bin/env python
"""
Test du filtrage des thématiques et sujets par service
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Thematique, Sujet, Service

User = get_user_model()

def test_thematiques_sujets_service():
    """Test du filtrage des thématiques et sujets par service"""
    
    print("=== TEST THÉMATIQUES ET SUJETS PAR SERVICE ===")
    
    # 1. Analyser les données existantes
    print("📊 ANALYSE DES DONNÉES EXISTANTES:")
    
    # Services
    services = Service.objects.filter(actif=True)
    print(f"   🏢 Services actifs: {services.count()}")
    for service in services:
        print(f"      • {service.nom}")
    
    # Thématiques
    thematiques = Thematique.objects.all()
    print(f"\n   📋 Thématiques totales: {thematiques.count()}")
    
    for thematique in thematiques:
        service_nom = thematique.service.nom if thematique.service else 'Aucun service'
        print(f"      • {thematique.titre} (Service: {service_nom})")
    
    # Sujets
    sujets = Sujet.objects.all()
    print(f"\n   📝 Sujets totaux: {sujets.count()}")
    
    for sujet in sujets:
        service_nom = sujet.service.nom if sujet.service else 'Aucun service'
        thematique_nom = sujet.thematique.titre if sujet.thematique else 'Aucune thématique'
        print(f"      • {sujet.titre} (Service: {service_nom}, Thématique: {thematique_nom})")
    
    # 2. Analyser par service
    print(f"\n📋 RÉPARTITION PAR SERVICE:")
    
    for service in services:
        thematiques_service = Thematique.objects.filter(service=service, active=True)
        sujets_service = Sujet.objects.filter(service=service)
        
        print(f"\n   🏢 Service: {service.nom}")
        print(f"      📋 Thématiques: {thematiques_service.count()}")
        for them in thematiques_service:
            print(f"         • {them.titre}")
        
        print(f"      📝 Sujets: {sujets_service.count()}")
        for suj in sujets_service:
            print(f"         • {suj.titre}")
    
    # Thématiques et sujets sans service
    thematiques_sans_service = Thematique.objects.filter(service__isnull=True, active=True)
    sujets_sans_service = Sujet.objects.filter(service__isnull=True)
    
    if thematiques_sans_service.exists() or sujets_sans_service.exists():
        print(f"\n   ❓ SANS SERVICE ASSIGNÉ:")
        if thematiques_sans_service.exists():
            print(f"      📋 Thématiques sans service: {thematiques_sans_service.count()}")
            for them in thematiques_sans_service:
                print(f"         • {them.titre}")
        
        if sujets_sans_service.exists():
            print(f"      📝 Sujets sans service: {sujets_sans_service.count()}")
            for suj in sujets_sans_service:
                print(f"         • {suj.titre}")
    
    # 3. Test avec un encadrant spécifique
    print(f"\n🧪 TEST AVEC ENCADRANT SPÉCIFIQUE:")
    
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True, service__isnull=False).first()
    if not encadrant:
        print("   ❌ Aucun encadrant avec service trouvé")
        return
    
    print(f"   👨‍💼 Encadrant: {encadrant.get_full_name()}")
    print(f"   🏢 Service: {encadrant.service.nom}")
    
    # Thématiques attendues pour cet encadrant
    thematiques_attendues = Thematique.objects.filter(
        service=encadrant.service,
        active=True
    )
    
    print(f"   📋 Thématiques attendues: {thematiques_attendues.count()}")
    for them in thematiques_attendues:
        print(f"      • {them.titre}")
    
    # Sujets attendus pour cet encadrant
    sujets_attendus = Sujet.objects.filter(service=encadrant.service)
    
    print(f"   📝 Sujets attendus: {sujets_attendus.count()}")
    for suj in sujets_attendus:
        print(f"      • {suj.titre}")
    
    # 4. Test des vues web
    print(f"\n🌐 TEST DES VUES WEB:")
    
    client = Client()
    client.force_login(encadrant)
    
    # Test vue thématiques
    print(f"   📋 Test vue thématiques:")
    response = client.get('/thematiques/')
    print(f"      Status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier les thématiques affichées
        thematiques_affiches = 0
        for them in thematiques_attendues:
            if them.titre in content:
                thematiques_affiches += 1
                print(f"      ✅ {them.titre} affiché")
        
        print(f"      📊 Thématiques du service affichées: {thematiques_affiches}/{thematiques_attendues.count()}")
        
        # Vérifier qu'aucune thématique d'autres services n'est affichée
        autres_thematiques = Thematique.objects.filter(active=True).exclude(service=encadrant.service)
        thematiques_autres_affiches = 0
        
        for them in autres_thematiques:
            if them.service and them.titre in content:  # Exclure celles sans service
                thematiques_autres_affiches += 1
                print(f"      ⚠️ {them.titre} (Service: {them.service.nom}) affiché - NE DEVRAIT PAS")
        
        if thematiques_autres_affiches == 0:
            print(f"      ✅ Aucune thématique d'autres services affichée")
        else:
            print(f"      ❌ {thematiques_autres_affiches} thématiques d'autres services affichées")
    
    # Test vue sujets
    print(f"\n   📝 Test vue sujets:")
    response = client.get('/sujets/')
    print(f"      Status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier les sujets affichés
        sujets_affiches = 0
        for suj in sujets_attendus:
            if suj.titre in content:
                sujets_affiches += 1
                print(f"      ✅ {suj.titre} affiché")
        
        print(f"      📊 Sujets du service affichés: {sujets_affiches}/{sujets_attendus.count()}")
        
        # Vérifier qu'aucun sujet d'autres services n'est affiché
        autres_sujets = Sujet.objects.all().exclude(service=encadrant.service)
        sujets_autres_affiches = 0
        
        for suj in autres_sujets:
            if suj.service and suj.titre in content:  # Exclure ceux sans service
                sujets_autres_affiches += 1
                print(f"      ⚠️ {suj.titre} (Service: {suj.service.nom}) affiché - NE DEVRAIT PAS")
        
        if sujets_autres_affiches == 0:
            print(f"      ✅ Aucun sujet d'autres services affiché")
        else:
            print(f"      ❌ {sujets_autres_affiches} sujets d'autres services affichés")
    
    # 5. Test avec Admin pour comparaison
    print(f"\n👨‍💼 TEST AVEC ADMIN (pour comparaison):")
    
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    if admin:
        client.force_login(admin)
        
        # Test thématiques admin
        response = client.get('/thematiques/')
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            toutes_thematiques = Thematique.objects.filter(active=True)
            thematiques_admin_affiches = 0
            
            for them in toutes_thematiques:
                if them.titre in content:
                    thematiques_admin_affiches += 1
            
            print(f"   📋 Admin voit {thematiques_admin_affiches} thématiques sur {toutes_thematiques.count()} total")
        
        # Test sujets admin
        response = client.get('/sujets/')
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            tous_sujets = Sujet.objects.all()
            sujets_admin_affiches = 0
            
            for suj in tous_sujets:
                if suj.titre in content:
                    sujets_admin_affiches += 1
            
            print(f"   📝 Admin voit {sujets_admin_affiches} sujets sur {tous_sujets.count()} total")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DU TEST THÉMATIQUES/SUJETS:")
    print("")
    print("✅ FONCTIONNALITÉS TESTÉES :")
    print("   • Filtrage thématiques par service ✅")
    print("   • Filtrage sujets par service ✅")
    print("   • Vues web pour encadrants ✅")
    print("   • Comparaison avec droits admin ✅")
    print("")
    print("✅ COMPORTEMENT ATTENDU :")
    print("   • Encadrant voit seulement thématiques de son service")
    print("   • Encadrant voit seulement sujets de son service")
    print("   • Admin voit toutes les thématiques et sujets")
    print("")
    print("🎉 TEST THÉMATIQUES/SUJETS PAR SERVICE TERMINÉ !")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_thematiques_sujets_service()
