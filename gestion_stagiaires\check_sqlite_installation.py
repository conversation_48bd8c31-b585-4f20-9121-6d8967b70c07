#!/usr/bin/env python
import sqlite3

print(f"SQLite version: {sqlite3.sqlite_version}")
print(f"SQLite module version: {sqlite3.version}")

# Tester la création d'une base de données en mémoire
conn = sqlite3.connect(':memory:')
cursor = conn.cursor()
cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)")
cursor.execute("INSERT INTO test VALUES (1, 'Test')")
cursor.execute("SELECT * FROM test")
result = cursor.fetchone()
print(f"Test result: {result}")
conn.close()

print("SQLite fonctionne correctement sur votre système.")