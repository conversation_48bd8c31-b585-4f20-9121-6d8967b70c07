#!/usr/bin/env python
"""
Test de l'ajout de stagiaires par service pour les encadrants
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service, Thematique, Sujet

User = get_user_model()

def test_encadrant_ajout_service():
    """Test de l'ajout de stagiaires par service pour les encadrants"""
    
    print("=== TEST AJOUT STAGIAIRES PAR SERVICE POUR ENCADRANTS ===")
    
    # 1. Préparation
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not encadrant:
        print("❌ Aucun encadrant trouvé")
        return
    
    print(f"✅ Encadrant: {encadrant.get_full_name()}")
    print(f"   Service: {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    # 2. Vérifier les thématiques et sujets disponibles
    print(f"\n📋 Thématiques et sujets disponibles:")
    
    if encadrant.service:
        # Thématiques du service
        thematiques_service = Thematique.objects.filter(
            service=encadrant.service, active=True
        )
        print(f"   Thématiques du service {encadrant.service.nom}: {thematiques_service.count()}")
        for thematique in thematiques_service:
            print(f"     • {thematique.titre}")
        
        # Sujets du service
        sujets_service = Sujet.objects.filter(
            service=encadrant.service, actif=True
        )
        print(f"   Sujets du service {encadrant.service.nom}: {sujets_service.count()}")
        for sujet in sujets_service:
            print(f"     • {sujet.titre}")
    else:
        print("   ⚠️ Encadrant sans service assigné")
    
    # 3. Test d'accès au formulaire d'ajout
    print(f"\n📝 Test du formulaire d'ajout:")
    
    client = Client()
    client.force_login(encadrant)
    
    response = client.get('/stagiaires/add/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifications du contenu
        checks = [
            ('service_info', 'Champ service_info'),
            ('Thématiques et Sujets de votre service', 'Alerte informative'),
            ('de votre service', 'Indication service'),
            ('form.thematique', 'Champ thématique'),
            ('form.sujet', 'Champ sujet'),
        ]
        
        for check, desc in checks:
            if check in content:
                print(f"      ✅ {desc}")
            else:
                print(f"      ⚠️ {desc} non trouvé")
        
        # Vérifier que le département est caché
        if 'type="hidden"' in content and 'departement' in content:
            print("      ✅ Département en champ caché")
        else:
            print("      ⚠️ Département non caché")
    
    # 4. Test d'ajout d'un stagiaire
    print(f"\n➕ Test d'ajout d'un stagiaire:")
    
    if encadrant.service:
        # Données de test
        from datetime import date, timedelta
        
        test_data = {
            'nom': 'TESTSERVICE',
            'prenom': 'ENCADRANT',
            'email': '<EMAIL>',
            'date_naissance': '2000-01-01',
            'date_debut': date.today().strftime('%Y-%m-%d'),
            'date_fin': (date.today() + timedelta(days=60)).strftime('%Y-%m-%d'),
            'etablissement': 'Test Service',
            'niveau_etude': 'Master',
            'specialite': 'Test Service',
            'technologies': 'Python, Django',
        }
        
        # Ajouter thématique et sujet si disponibles
        if thematiques_service.exists():
            test_data['thematique'] = thematiques_service.first().id
        
        if sujets_service.exists():
            test_data['sujet'] = sujets_service.first().id
        
        # Supprimer le stagiaire de test s'il existe
        Stagiaire.objects.filter(email='<EMAIL>').delete()
        
        count_avant = Stagiaire.objects.count()
        
        try:
            response = client.post('/stagiaires/add/', test_data)
            print(f"   Status POST: {response.status_code}")
            
            if response.status_code == 302:
                print("   ✅ Ajout réussi (redirection)")
                
                # Vérifier que le stagiaire a été créé
                stagiaire_cree = Stagiaire.objects.filter(email='<EMAIL>').first()
                
                if stagiaire_cree:
                    print(f"   ✅ Stagiaire créé: {stagiaire_cree.nom_complet}")
                    print(f"      Service: {stagiaire_cree.service.nom if stagiaire_cree.service else 'Aucun'}")
                    print(f"      Département: {stagiaire_cree.get_departement_display()}")
                    print(f"      Encadrant: {stagiaire_cree.encadrant.get_full_name() if stagiaire_cree.encadrant else 'Aucun'}")
                    print(f"      Créé par: {stagiaire_cree.cree_par.get_full_name() if stagiaire_cree.cree_par else 'Aucun'}")
                    
                    # Vérifications importantes
                    if stagiaire_cree.service == encadrant.service:
                        print("      ✅ Service correctement assigné")
                    else:
                        print("      ❌ Service mal assigné")
                    
                    if stagiaire_cree.encadrant == encadrant:
                        print("      ✅ Encadrant correctement assigné")
                    else:
                        print("      ❌ Encadrant mal assigné")
                    
                    if stagiaire_cree.cree_par == encadrant:
                        print("      ✅ Créateur correctement assigné")
                    else:
                        print("      ❌ Créateur mal assigné")
                    
                    # Nettoyer
                    stagiaire_cree.delete()
                    print("      🧹 Stagiaire de test supprimé")
                else:
                    print("   ❌ Stagiaire non créé")
            else:
                print(f"   ❌ Échec de l'ajout - Status: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Erreur lors de l'ajout: {e}")
    else:
        print("   ⚠️ Test impossible - Encadrant sans service")
    
    # 5. Test avec différents rôles pour comparaison
    print(f"\n👥 Test avec différents rôles:")
    
    # Test avec Admin
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    if admin:
        client.force_login(admin)
        response = client.get('/stagiaires/add/')
        print(f"   Admin - Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            if 'service_info' in content:
                print("      ⚠️ Champ service_info visible pour admin (ne devrait pas)")
            else:
                print("      ✅ Champ service_info caché pour admin (correct)")
    
    # Test avec RH
    user_rh = User.objects.filter(role='RH', is_active=True).first()
    if user_rh:
        client.force_login(user_rh)
        response = client.get('/stagiaires/add/')
        print(f"   RH - Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            if 'service_info' in content:
                print("      ⚠️ Champ service_info visible pour RH (ne devrait pas)")
            else:
                print("      ✅ Champ service_info caché pour RH (correct)")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DES FONCTIONNALITÉS IMPLÉMENTÉES:")
    print("")
    print("✅ POUR LES ENCADRANTS :")
    print("   • Ajout limité aux stagiaires de leur service")
    print("   • Service et département automatiquement assignés")
    print("   • Thématiques filtrées par service")
    print("   • Sujets filtrés par service")
    print("   • Encadrant automatiquement assigné")
    print("   • Interface adaptée avec informations contextuelles")
    print("")
    print("✅ POUR LES AUTRES RÔLES :")
    print("   • Admin/RH : Accès complet à tous les services")
    print("   • Interface normale maintenue")
    print("")
    print("🎯 UTILISATION :")
    print("   1. Connectez-vous en tant qu'encadrant")
    print("   2. Allez sur /stagiaires/add/")
    print("   3. Le service est pré-rempli et verrouillé")
    print("   4. Seules vos thématiques/sujets sont disponibles")
    print("   5. Le stagiaire sera automatiquement assigné à vous")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_encadrant_ajout_service()
