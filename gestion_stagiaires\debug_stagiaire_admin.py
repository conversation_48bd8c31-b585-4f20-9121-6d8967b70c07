#!/usr/bin/env python
"""
Script de diagnostic pour identifier le problème avec l'ajout de stagiaires dans l'admin Django
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service
from django.core.exceptions import ValidationError
from datetime import date, timedelta

User = get_user_model()

def debug_stagiaire_creation():
    """Diagnostiquer les problèmes de création de stagiaires"""
    
    print("=== Diagnostic de création de stagiaires ===")
    
    # 1. Vérifier les utilisateurs admin disponibles
    print("\n👤 Vérification des utilisateurs admin:")
    admins = User.objects.filter(is_superuser=True)
    print(f"   Nombre d'admins: {admins.count()}")
    
    if admins.count() == 0:
        print("   ❌ Aucun administrateur trouvé!")
        return
    
    admin_user = admins.first()
    print(f"   ✅ Admin trouvé: {admin_user.username}")
    
    # 2. Vérifier les encadrants disponibles
    print("\n👨‍🏫 Vérification des encadrants:")
    encadrants = User.objects.filter(role='ENCADRANT')
    print(f"   Nombre d'encadrants: {encadrants.count()}")
    
    if encadrants.count() == 0:
        print("   ⚠️ Aucun encadrant trouvé - création d'un encadrant de test")
        # Créer un service de test
        service_test, created = Service.objects.get_or_create(
            code_service='TEST',
            defaults={
                'nom': 'Service Test',
                'description': 'Service pour les tests',
                'actif': True
            }
        )
        
        # Créer un encadrant de test
        encadrant_test, created = User.objects.get_or_create(
            username='encadrant_test',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'Encadrant',
                'role': 'ENCADRANT',
                'service': service_test,
                'is_active': True
            }
        )
        if created:
            encadrant_test.set_password('testpassword')
            encadrant_test.save()
        print(f"   ✅ Encadrant de test créé: {encadrant_test.username}")
    else:
        encadrant_test = encadrants.first()
        print(f"   ✅ Encadrant trouvé: {encadrant_test.username}")
    
    # 3. Tester la création d'un stagiaire
    print("\n🧪 Test de création d'un stagiaire:")
    
    # Données de test
    stagiaire_data = {
        'nom': 'Test',
        'prenom': 'Stagiaire',
        'email': '<EMAIL>',
        'telephone': '0123456789',
        'date_naissance': date(2000, 1, 1),
        'departement': 'IT',
        'encadrant': encadrant_test,
        'date_debut': date.today(),
        'date_fin': date.today() + timedelta(days=90),
        'etablissement': 'Université Test',
        'niveau_etude': 'Master 2',
        'specialite': 'Informatique',
        'statut': 'EN_COURS',
        'cree_par': admin_user
    }
    
    try:
        # Supprimer le stagiaire de test s'il existe déjà
        Stagiaire.objects.filter(email='<EMAIL>').delete()
        
        # Créer le stagiaire
        stagiaire = Stagiaire.objects.create(**stagiaire_data)
        print(f"   ✅ Stagiaire créé avec succès: {stagiaire.nom_complet}")
        print(f"      ID: {stagiaire.id}")
        print(f"      Email: {stagiaire.email}")
        print(f"      Encadrant: {stagiaire.encadrant}")
        print(f"      Créé par: {stagiaire.cree_par}")
        
        # Nettoyer après le test
        stagiaire.delete()
        print(f"   🧹 Stagiaire de test supprimé")
        
    except ValidationError as e:
        print(f"   ❌ Erreur de validation: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Erreur lors de la création: {e}")
        return False
    
    # 4. Vérifier les contraintes du modèle
    print("\n🔍 Vérification des contraintes du modèle:")
    
    # Vérifier l'unicité de l'email
    try:
        stagiaire1 = Stagiaire.objects.create(
            nom='Test1', prenom='Stagiaire1', email='<EMAIL>',
            date_naissance=date(2000, 1, 1), departement='IT',
            date_debut=date.today(), date_fin=date.today() + timedelta(days=30),
            etablissement='Test', niveau_etude='Test', specialite='Test',
            cree_par=admin_user
        )
        
        # Essayer de créer un autre stagiaire avec le même email
        stagiaire2 = Stagiaire.objects.create(
            nom='Test2', prenom='Stagiaire2', email='<EMAIL>',
            date_naissance=date(2000, 1, 1), departement='IT',
            date_debut=date.today(), date_fin=date.today() + timedelta(days=30),
            etablissement='Test', niveau_etude='Test', specialite='Test',
            cree_par=admin_user
        )
        
        print("   ❌ Problème: L'unicité de l'email n'est pas respectée!")
        stagiaire1.delete()
        stagiaire2.delete()
        
    except Exception as e:
        print(f"   ✅ Contrainte d'unicité de l'email fonctionne: {type(e).__name__}")
        # Nettoyer
        Stagiaire.objects.filter(email='<EMAIL>').delete()
    
    # 5. Tester les champs obligatoires
    print("\n📋 Test des champs obligatoires:")
    
    required_fields = ['nom', 'prenom', 'email', 'date_naissance', 'departement', 
                      'date_debut', 'date_fin', 'etablissement', 'niveau_etude', 'specialite']
    
    for field in required_fields:
        try:
            test_data = stagiaire_data.copy()
            test_data.pop(field, None)  # Retirer le champ
            
            stagiaire_test = Stagiaire(**test_data)
            stagiaire_test.full_clean()  # Validation
            print(f"   ⚠️ Champ '{field}' semble optionnel")
            
        except ValidationError as e:
            if field in str(e):
                print(f"   ✅ Champ '{field}' est obligatoire")
            else:
                print(f"   ❓ Champ '{field}': {e}")
        except Exception as e:
            print(f"   ❌ Erreur pour '{field}': {e}")
    
    # 6. Vérifier les permissions dans l'admin
    print("\n🔐 Vérification des permissions admin:")
    
    from stagiaires.admin import StagiaireAdmin
    from django.contrib import admin
    
    stagiaire_admin = StagiaireAdmin(Stagiaire, admin.site)
    
    # Simuler une requête admin
    from django.test import RequestFactory
    factory = RequestFactory()
    request = factory.get('/admin/stagiaires/stagiaire/add/')
    request.user = admin_user
    
    # Vérifier les permissions
    has_add = stagiaire_admin.has_add_permission(request)
    has_change = stagiaire_admin.has_change_permission(request)
    has_delete = stagiaire_admin.has_delete_permission(request)
    
    print(f"   Ajouter: {'✅' if has_add else '❌'}")
    print(f"   Modifier: {'✅' if has_change else '❌'}")
    print(f"   Supprimer: {'✅' if has_delete else '❌'}")
    
    # 7. Vérifier les actions disponibles
    actions = stagiaire_admin.get_actions(request)
    print(f"\n🎬 Actions disponibles: {len(actions)}")
    for action_name in actions.keys():
        print(f"   • {action_name}")
    
    print("\n=== Diagnostic terminé ===")
    return True

if __name__ == '__main__':
    debug_stagiaire_creation()
