#!/usr/bin/env python
"""
Script de test pour la mise à jour des statuts de tâches
"""

import os
import sys
import django
from datetime import date, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser, Stagiaire, TacheStage

def test_tache_status_workflow():
    """Test du workflow de mise à jour des statuts de tâches"""
    print("=== Test du workflow des statuts de tâches ===")
    
    # Récupérer ou créer un encadrant
    encadrant, created = CustomUser.objects.get_or_create(
        username='encadrant_test',
        defaults={
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': 'Du<PERSON>',
            'role': 'ENCADRANT'
        }
    )
    if created:
        encadrant.set_password('password123')
        encadrant.save()
        print(f"✓ Encadrant créé : {encadrant.get_full_name()}")
    else:
        print(f"✓ Encadrant existant : {encadrant.get_full_name()}")
    
    # Récupérer ou créer un stagiaire
    stagiaire, created = Stagiaire.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'nom': 'Martin',
            'prenom': 'Alice',
            'date_naissance': date(2000, 5, 15),
            'telephone': '0123456789',
            'departement': 'Informatique',
            'encadrant': encadrant,
            'date_debut': date.today(),
            'date_fin': date.today() + timedelta(days=90),
            'etablissement': 'Université Test',
            'niveau_etude': 'Master 2',
            'specialite': 'Développement Web',
            'cree_par': encadrant
        }
    )
    if created:
        print(f"✓ Stagiaire créé : {stagiaire.nom_complet}")
    else:
        print(f"✓ Stagiaire existant : {stagiaire.nom_complet}")
    
    # Créer une tâche de test
    tache, created = TacheStage.objects.get_or_create(
        titre='Développement interface utilisateur',
        stagiaire=stagiaire,
        defaults={
            'description': 'Créer une interface utilisateur moderne avec Bootstrap',
            'date_debut_prevue': date.today() + timedelta(days=1),
            'date_fin_prevue': date.today() + timedelta(days=15),
            'priorite': 2,
            'statut': 'NON_COMMENCEE',
            'creee_par': encadrant
        }
    )
    if created:
        print(f"✓ Tâche créée : {tache.titre}")
    else:
        print(f"✓ Tâche existante : {tache.titre}")
    
    print(f"  - Statut initial : {tache.get_statut_display()}")
    print(f"  - Date début prévue : {tache.date_debut_prevue}")
    print(f"  - Date fin prévue : {tache.date_fin_prevue}")
    
    return tache

def test_status_transitions(tache):
    """Test des transitions de statut"""
    print("\n=== Test des transitions de statut ===")
    
    # 1. Démarrer la tâche
    print("1. Démarrage de la tâche...")
    ancien_statut = tache.statut
    tache.statut = 'EN_COURS'
    tache.date_debut_reelle = date.today()
    tache.save()
    
    print(f"   ✓ Statut : {ancien_statut} → {tache.get_statut_display()}")
    print(f"   ✓ Date début réelle : {tache.date_debut_reelle}")
    
    # 2. Terminer la tâche
    print("\n2. Fin de la tâche...")
    ancien_statut = tache.statut
    tache.statut = 'TERMINEE'
    tache.date_fin_reelle = date.today()
    tache.save()
    
    print(f"   ✓ Statut : {ancien_statut} → {tache.get_statut_display()}")
    print(f"   ✓ Date fin réelle : {tache.date_fin_reelle}")
    
    # 3. Valider la tâche
    print("\n3. Validation de la tâche...")
    ancien_statut = tache.statut
    tache.statut = 'VALIDEE'
    tache.note = 18.0
    tache.commentaire_encadrant = 'Excellent travail, interface très réussie'
    tache.save()
    
    print(f"   ✓ Statut : {ancien_statut} → {tache.get_statut_display()}")
    print(f"   ✓ Note attribuée : {tache.note}/20")
    print(f"   ✓ Commentaire : {tache.commentaire_encadrant}")

def test_tache_properties(tache):
    """Test des propriétés calculées de la tâche"""
    print("\n=== Test des propriétés calculées ===")
    
    # Durée prévue
    if tache.date_debut_prevue and tache.date_fin_prevue:
        duree_prevue = (tache.date_fin_prevue - tache.date_debut_prevue).days
        print(f"✓ Durée prévue : {duree_prevue} jours")
    
    # Durée réelle
    if tache.date_debut_reelle and tache.date_fin_reelle:
        duree_reelle = (tache.date_fin_reelle - tache.date_debut_reelle).days
        print(f"✓ Durée réelle : {duree_reelle} jours")
    
    # Vérifier si en retard
    en_retard = tache.en_retard if hasattr(tache, 'en_retard') else False
    print(f"✓ En retard : {'Oui' if en_retard else 'Non'}")

def test_data_integrity():
    """Test de l'intégrité des données"""
    print("\n=== Test de l'intégrité des données ===")
    
    # Compter les tâches par statut
    statuts = {}
    for statut_code, statut_label in TacheStage.STATUT_TACHE_CHOICES:
        count = TacheStage.objects.filter(statut=statut_code).count()
        statuts[statut_label] = count
        print(f"✓ Tâches '{statut_label}' : {count}")
    
    # Vérifier les relations
    total_taches = TacheStage.objects.count()
    print(f"✓ Total des tâches : {total_taches}")
    
    # Tâches avec encadrant
    taches_avec_encadrant = TacheStage.objects.filter(
        stagiaire__encadrant__isnull=False
    ).count()
    print(f"✓ Tâches avec encadrant assigné : {taches_avec_encadrant}")

def test_permissions():
    """Test des permissions d'accès"""
    print("\n=== Test des permissions ===")
    
    # Compter les utilisateurs par rôle
    roles = {}
    for role_code, role_label in CustomUser.ROLE_CHOICES:
        count = CustomUser.objects.filter(role=role_code).count()
        roles[role_label] = count
        print(f"✓ Utilisateurs '{role_label}' : {count}")
    
    # Vérifier les encadrants avec stagiaires
    encadrants_actifs = CustomUser.objects.filter(
        role='ENCADRANT',
        stagiaire__isnull=False
    ).distinct().count()
    print(f"✓ Encadrants avec stagiaires : {encadrants_actifs}")

def main():
    """Fonction principale de test"""
    print("🚀 Démarrage des tests pour les statuts de tâches")
    print("=" * 60)
    
    try:
        # Tests séquentiels
        tache = test_tache_status_workflow()
        test_status_transitions(tache)
        test_tache_properties(tache)
        test_data_integrity()
        test_permissions()
        
        print("\n" + "=" * 60)
        print("✅ Tous les tests ont été exécutés avec succès !")
        print("🎉 Le système de mise à jour des statuts de tâches est opérationnel.")
        print("\n📋 Instructions pour tester l'interface :")
        print("1. Connectez-vous avec un compte encadrant")
        print("2. Allez dans la liste des stagiaires")
        print("3. Cliquez sur 'Tâches' pour un stagiaire")
        print("4. Utilisez les boutons 'Démarrer' et 'Terminer' pour tester")
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests : {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
