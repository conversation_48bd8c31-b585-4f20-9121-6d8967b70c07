#!/usr/bin/env python
import os
import shutil
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.db import connection

# 1. Supprimer toutes les migrations problématiques de la base de données
with connection.cursor() as cursor:
    cursor.execute("DELETE FROM django_migrations WHERE app='stagiaires' AND name LIKE '00%'")
    cursor.execute("DELETE FROM django_migrations WHERE app='stagiaires' AND name LIKE 'manual%'")
    cursor.execute("DELETE FROM django_migrations WHERE app='stagiaires' AND name LIKE '%merge%'")
    print("Migrations problématiques supprimées de la base de données.")

# 2. Vérifier les migrations restantes
with connection.cursor() as cursor:
    cursor.execute("SELECT name FROM django_migrations WHERE app='stagiaires' ORDER BY id")
    rows = cursor.fetchall()
    print("\nMigrations restantes dans la base de données:")
    for row in rows:
        print(f"- {row[0]}")

# 3. Créer un dossier de sauvegarde pour les fichiers de migration
backup_dir = os.path.join('gestion_stagiaires', 'stagiaires', 'migrations', 'backup')
os.makedirs(backup_dir, exist_ok=True)

# 4. Déplacer tous les fichiers de migration problématiques vers le dossier de sauvegarde
migrations_dir = os.path.join('gestion_stagiaires', 'stagiaires', 'migrations')
for filename in os.listdir(migrations_dir):
    if filename.startswith(('00', '0019', '0020', '0021', '0022', '0023', '0024', '0025', '0026')) or 'merge' in filename or 'manual' in filename:
        if filename != '__init__.py' and filename.endswith('.py'):
            src = os.path.join(migrations_dir, filename)
            dst = os.path.join(backup_dir, filename)
            try:
                shutil.move(src, dst)
                print(f"Fichier de migration déplacé: {filename}")
            except Exception as e:
                print(f"Erreur lors du déplacement de {filename}: {e}")

print("\nScript terminé. Vous pouvez maintenant créer une nouvelle migration propre.")