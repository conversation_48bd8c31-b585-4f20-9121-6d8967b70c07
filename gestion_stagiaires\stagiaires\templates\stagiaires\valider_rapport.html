{% extends 'stagiaires/base.html' %}

{% block title %}Validation du rapport - {{ rapport.titre }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            Validation du rapport
                        </h4>
                        <a href="{% url 'rapports_stagiaire' rapport.stagiaire.id %}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-1"></i>Retour aux rapports
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Informations du rapport -->
                    <div class="card border-info mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">{{ rapport.titre }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Stagiaire :</strong> {{ rapport.stagiaire.nom_complet }}</p>
                                    <p><strong>Date de soumission :</strong> {{ rapport.date_soumission|date:"d/m/Y H:i" }}</p>
                                    <p><strong>Statut actuel :</strong> 
                                        {% if rapport.statut == 'BROUILLON' %}
                                            <span class="badge bg-secondary">Brouillon</span>
                                        {% elif rapport.statut == 'SOUMIS' %}
                                            <span class="badge bg-warning">Soumis</span>
                                        {% elif rapport.statut == 'EN_REVISION' %}
                                            <span class="badge bg-info">En révision</span>
                                        {% elif rapport.statut == 'VALIDE' %}
                                            <span class="badge bg-success">Validé</span>
                                        {% elif rapport.statut == 'REJETE' %}
                                            <span class="badge bg-danger">Rejeté</span>
                                        {% endif %}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    {% if rapport.mission %}
                                        <p><strong>Mission liée :</strong> {{ rapport.mission.titre }}</p>
                                        <p><strong>Statut de la mission :</strong> 
                                            <span class="badge bg-info">{{ rapport.mission.get_statut_display }}</span>
                                        </p>
                                    {% else %}
                                        <p><strong>Mission liée :</strong> <span class="text-muted">Aucune</span></p>
                                    {% endif %}
                                    <p><strong>Fichier :</strong> 
                                        <a href="{{ rapport.fichier_rapport.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-download me-1"></i>Télécharger
                                        </a>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <strong>Description :</strong>
                                <p class="text-muted">{{ rapport.description }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Formulaire de validation -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.statut.id_for_label }}" class="form-label">
                                        <i class="fas fa-flag me-1"></i>Décision de validation *
                                    </label>
                                    {{ form.statut }}
                                    {% if form.statut.errors %}
                                        <div class="text-danger small">{{ form.statut.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.note_rapport.id_for_label }}" class="form-label">
                                        <i class="fas fa-star me-1"></i>Note du rapport (sur 20)
                                    </label>
                                    {{ form.note_rapport }}
                                    {% if form.note_rapport.errors %}
                                        <div class="text-danger small">{{ form.note_rapport.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">Attribuez une note entre 0 et 20 (optionnel)</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.commentaires_validation.id_for_label }}" class="form-label">
                                <i class="fas fa-comment me-1"></i>Commentaires de validation
                            </label>
                            {{ form.commentaires_validation }}
                            {% if form.commentaires_validation.errors %}
                                <div class="text-danger small">{{ form.commentaires_validation.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">Fournissez des commentaires détaillés sur le rapport (points forts, points à améliorer, suggestions...)</div>
                        </div>

                        <!-- Affichage des erreurs générales -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Grille d'évaluation suggérée -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h6 class="card-title text-primary">
                                    <i class="fas fa-clipboard-list me-1"></i>Grille d'évaluation suggérée
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="small mb-0">
                                            <li><strong>Contenu (40%) :</strong> Pertinence, exhaustivité, analyse</li>
                                            <li><strong>Structure (20%) :</strong> Organisation, logique, clarté</li>
                                            <li><strong>Rédaction (20%) :</strong> Orthographe, syntaxe, style</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="small mb-0">
                                            <li><strong>Présentation (10%) :</strong> Mise en page, illustrations</li>
                                            <li><strong>Réflexion (10%) :</strong> Esprit critique, apprentissages</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Historique de validation -->
                        {% if rapport.valide_par %}
                        <div class="alert alert-info">
                            <h6><i class="fas fa-history me-1"></i>Historique de validation</h6>
                            <p class="mb-1"><strong>Précédemment validé par :</strong> {{ rapport.valide_par.get_full_name }}</p>
                            <p class="mb-1"><strong>Date :</strong> {{ rapport.date_validation|date:"d/m/Y H:i" }}</p>
                            {% if rapport.note_rapport %}
                                <p class="mb-1"><strong>Note précédente :</strong> {{ rapport.note_rapport }}/20</p>
                            {% endif %}
                            {% if rapport.commentaires_validation %}
                                <p class="mb-0"><strong>Commentaires précédents :</strong> {{ rapport.commentaires_validation }}</p>
                            {% endif %}
                        </div>
                        {% endif %}

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'rapports_stagiaire' rapport.stagiaire.id %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check me-1"></i>Valider le rapport
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Panneau latéral avec aperçu du fichier -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Aperçu du rapport
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-file-pdf fa-3x text-danger mb-2"></i>
                        <h6>{{ rapport.fichier_rapport.name|basename }}</h6>
                        <small class="text-muted">
                            Taille : {{ rapport.fichier_rapport.size|filesizeformat }}
                        </small>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <a href="{{ rapport.fichier_rapport.url }}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-1"></i>Ouvrir dans un nouvel onglet
                        </a>
                        <a href="{{ rapport.fichier_rapport.url }}" download class="btn btn-outline-primary">
                            <i class="fas fa-download me-1"></i>Télécharger
                        </a>
                    </div>
                    
                    <!-- Informations supplémentaires -->
                    <div class="mt-4">
                        <h6 class="text-secondary">Informations du stagiaire</h6>
                        <p class="small mb-1"><strong>Email :</strong> {{ rapport.stagiaire.email }}</p>
                        <p class="small mb-1"><strong>Période :</strong> {{ rapport.stagiaire.date_debut|date:"d/m/Y" }} - {{ rapport.stagiaire.date_fin|date:"d/m/Y" }}</p>
                        {% if rapport.stagiaire.encadrant %}
                            <p class="small mb-0"><strong>Encadrant :</strong> {{ rapport.stagiaire.encadrant.get_full_name }}</p>
                        {% endif %}
                    </div>
                    
                    {% if rapport.mission %}
                    <div class="mt-3">
                        <h6 class="text-secondary">Mission associée</h6>
                        <p class="small mb-1"><strong>Titre :</strong> {{ rapport.mission.titre }}</p>
                        <p class="small mb-1"><strong>Avancement :</strong> {{ rapport.mission.pourcentage_avancement }}%</p>
                        <div class="progress" style="height: 15px;">
                            <div class="progress-bar bg-info" role="progressbar" style="width: {{ rapport.mission.pourcentage_avancement }}%"></div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Validation de la note
document.getElementById('{{ form.note_rapport.id_for_label }}').addEventListener('input', function() {
    const value = parseFloat(this.value);
    if (value < 0) this.value = 0;
    if (value > 20) this.value = 20;
});

// Mise à jour automatique des commentaires selon le statut
document.getElementById('{{ form.statut.id_for_label }}').addEventListener('change', function() {
    const commentairesField = document.getElementById('{{ form.commentaires_validation.id_for_label }}');
    const statut = this.value;
    
    // Suggestions de commentaires selon le statut
    if (statut === 'VALIDE' && !commentairesField.value.trim()) {
        commentairesField.placeholder = "Exemple : Excellent travail, rapport complet et bien structuré. Les objectifs sont atteints.";
    } else if (statut === 'REJETE' && !commentairesField.value.trim()) {
        commentairesField.placeholder = "Exemple : Le rapport nécessite des améliorations importantes. Veuillez revoir la structure et approfondir l'analyse.";
    } else if (statut === 'EN_REVISION' && !commentairesField.value.trim()) {
        commentairesField.placeholder = "Exemple : Bon travail dans l'ensemble, quelques points à améliorer avant validation finale.";
    }
});

// Confirmation avant soumission
document.querySelector('form').addEventListener('submit', function(e) {
    const statut = document.getElementById('{{ form.statut.id_for_label }}').value;
    const note = document.getElementById('{{ form.note_rapport.id_for_label }}').value;
    
    let message = `Êtes-vous sûr de vouloir ${statut === 'VALIDE' ? 'valider' : statut === 'REJETE' ? 'rejeter' : 'mettre en révision'} ce rapport ?`;
    
    if (note) {
        message += `\nNote attribuée : ${note}/20`;
    }
    
    if (!confirm(message)) {
        e.preventDefault();
        return;
    }
    
    // Afficher un indicateur de chargement
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Validation en cours...';
    submitBtn.disabled = true;
});
</script>
{% endblock %}
