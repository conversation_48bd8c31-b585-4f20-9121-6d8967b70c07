{% extends 'stagiaires/base.html' %}

{% block title %}Suivi de mission - {{ mission.titre }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Suivi d'avancement
                        </h4>
                        <a href="{% url 'missions_stagiaire' mission.stagiaire.id %}" class="btn btn-outline-dark">
                            <i class="fas fa-arrow-left me-1"></i>Retour aux missions
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Informations de la mission -->
                    <div class="card border-info mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">{{ mission.titre }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Stagiaire :</strong> {{ mission.stagiaire.nom_complet }}</p>
                                    <p><strong>Créée par :</strong> {{ mission.creee_par.get_full_name }}</p>
                                    <p><strong>Date de création :</strong> {{ mission.date_creation|date:"d/m/Y H:i" }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Priorité :</strong> 
                                        {% if mission.priorite == 1 %}
                                            <span class="badge bg-danger">Très haute</span>
                                        {% elif mission.priorite == 2 %}
                                            <span class="badge bg-warning">Haute</span>
                                        {% elif mission.priorite == 3 %}
                                            <span class="badge bg-info">Moyenne</span>
                                        {% elif mission.priorite == 4 %}
                                            <span class="badge bg-secondary">Basse</span>
                                        {% else %}
                                            <span class="badge bg-light text-dark">Très basse</span>
                                        {% endif %}
                                    </p>
                                    <p><strong>Statut actuel :</strong> 
                                        {% if mission.statut == 'PLANIFIEE' %}
                                            <span class="badge bg-secondary">Planifiée</span>
                                        {% elif mission.statut == 'EN_COURS' %}
                                            <span class="badge bg-warning">En cours</span>
                                        {% elif mission.statut == 'TERMINEE' %}
                                            <span class="badge bg-success">Terminée</span>
                                        {% elif mission.statut == 'VALIDEE' %}
                                            <span class="badge bg-primary">Validée</span>
                                        {% elif mission.statut == 'REJETEE' %}
                                            <span class="badge bg-danger">Rejetée</span>
                                        {% endif %}
                                    </p>
                                    <p><strong>Période prévue :</strong><br>
                                        {{ mission.date_debut_prevue|date:"d/m/Y" }} - {{ mission.date_fin_prevue|date:"d/m/Y" }}
                                        <small class="text-muted">({{ mission.duree_prevue }} jours)</small>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <strong>Description :</strong>
                                <p class="text-muted">{{ mission.description }}</p>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Objectifs :</strong>
                                    <p class="text-muted small">{{ mission.objectifs }}</p>
                                </div>
                                <div class="col-md-6">
                                    <strong>Livrables attendus :</strong>
                                    <p class="text-muted small">{{ mission.livrables_attendus }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Formulaire de suivi -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.pourcentage_avancement.id_for_label }}" class="form-label">
                                        <i class="fas fa-percentage me-1"></i>Pourcentage d'avancement *
                                    </label>
                                    {{ form.pourcentage_avancement }}
                                    {% if form.pourcentage_avancement.errors %}
                                        <div class="text-danger small">{{ form.pourcentage_avancement.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">Indiquez le pourcentage de completion (0-100%)</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.statut.id_for_label }}" class="form-label">
                                        <i class="fas fa-flag me-1"></i>Statut de la mission *
                                    </label>
                                    {{ form.statut }}
                                    {% if form.statut.errors %}
                                        <div class="text-danger small">{{ form.statut.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.commentaire_avancement.id_for_label }}" class="form-label">
                                <i class="fas fa-comment me-1"></i>Commentaire sur l'avancement
                            </label>
                            {{ form.commentaire_avancement }}
                            {% if form.commentaire_avancement.errors %}
                                <div class="text-danger small">{{ form.commentaire_avancement.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">Décrivez les progrès réalisés, les difficultés rencontrées, les prochaines étapes...</div>
                        </div>

                        <!-- Affichage des erreurs générales -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Informations sur les dates réelles -->
                        {% if mission.date_debut_reelle or mission.date_fin_reelle %}
                        <div class="alert alert-info">
                            <h6><i class="fas fa-calendar-check me-1"></i>Dates réelles :</h6>
                            {% if mission.date_debut_reelle %}
                                <p class="mb-1"><strong>Début réel :</strong> {{ mission.date_debut_reelle|date:"d/m/Y" }}</p>
                            {% endif %}
                            {% if mission.date_fin_reelle %}
                                <p class="mb-1"><strong>Fin réelle :</strong> {{ mission.date_fin_reelle|date:"d/m/Y" }}</p>
                                {% if mission.duree_reelle %}
                                    <p class="mb-0"><strong>Durée réelle :</strong> {{ mission.duree_reelle }} jours</p>
                                {% endif %}
                            {% endif %}
                        </div>
                        {% endif %}

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'missions_stagiaire' mission.stagiaire.id %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>Mettre à jour l'avancement
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Panneau latéral avec historique -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Historique des mises à jour
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Mission créée</h6>
                                <p class="timeline-text">{{ mission.date_creation|date:"d/m/Y H:i" }}</p>
                                <small class="text-muted">Par {{ mission.creee_par.get_full_name }}</small>
                            </div>
                        </div>
                        
                        {% if mission.date_debut_reelle %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Mission démarrée</h6>
                                <p class="timeline-text">{{ mission.date_debut_reelle|date:"d/m/Y" }}</p>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if mission.date_fin_reelle %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Mission terminée</h6>
                                <p class="timeline-text">{{ mission.date_fin_reelle|date:"d/m/Y" }}</p>
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Dernière mise à jour</h6>
                                <p class="timeline-text">{{ mission.derniere_mise_a_jour|date:"d/m/Y H:i" }}</p>
                                <small class="text-muted">Avancement : {{ mission.pourcentage_avancement }}%</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Indicateurs de performance -->
            <div class="card shadow mt-3">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Indicateurs
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Avancement actuel</label>
                        <div class="progress" style="height: 25px;">
                            <div class="progress-bar 
                                {% if mission.pourcentage_avancement < 30 %}bg-danger
                                {% elif mission.pourcentage_avancement < 70 %}bg-warning
                                {% else %}bg-success{% endif %}" 
                                role="progressbar" 
                                style="width: {{ mission.pourcentage_avancement }}%">
                                {{ mission.pourcentage_avancement }}%
                            </div>
                        </div>
                    </div>
                    
                    {% if mission.en_retard %}
                    <div class="alert alert-warning small">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Cette mission est en retard par rapport à la date prévue.
                    </div>
                    {% endif %}
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <h6 class="text-primary">{{ mission.duree_prevue }}</h6>
                                <small class="text-muted">Jours prévus</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <h6 class="text-success">{{ mission.duree_reelle|default:"--" }}</h6>
                                <small class="text-muted">Jours réels</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 0.9rem;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 5px;
    font-size: 0.85rem;
}
</style>

<script>
// Mise à jour automatique du pourcentage selon le statut
document.getElementById('{{ form.statut.id_for_label }}').addEventListener('change', function() {
    const pourcentageField = document.getElementById('{{ form.pourcentage_avancement.id_for_label }}');
    const statut = this.value;
    
    if (statut === 'TERMINEE' || statut === 'VALIDEE') {
        pourcentageField.value = 100;
    } else if (statut === 'PLANIFIEE') {
        pourcentageField.value = 0;
    }
});

// Validation du pourcentage
document.getElementById('{{ form.pourcentage_avancement.id_for_label }}').addEventListener('input', function() {
    const value = parseInt(this.value);
    if (value < 0) this.value = 0;
    if (value > 100) this.value = 100;
});
</script>
{% endblock %}
