#!/usr/bin/env python
"""
Script de test pour vérifier l'affichage du menu Administration
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import RequestFactory
from django.template import Context, Template

User = get_user_model()

def test_admin_display():
    """Test de l'affichage du menu Administration"""
    
    print("=== Test d'affichage du menu Administration ===")
    
    # Récupérer l'utilisateur admin
    try:
        admin_user = User.objects.get(username='admin')
        print(f"✅ Utilisateur admin trouvé: {admin_user.username}")
    except User.DoesNotExist:
        print("❌ Aucun utilisateur admin trouvé")
        return
    
    print(f"📋 Informations de l'admin:")
    print(f"   Username: {admin_user.username}")
    print(f"   Role: {admin_user.role}")
    print(f"   is_superuser: {admin_user.is_superuser}")
    print(f"   is_staff: {admin_user.is_staff}")
    print(f"   is_active: {admin_user.is_active}")
    print(f"   Propriété is_admin: {admin_user.is_admin}")
    
    # Test des conditions d'affichage
    print(f"\n🔍 Test des conditions:")
    
    # Condition actuelle dans le template
    condition1 = admin_user.role == 'ADMIN'
    condition2 = admin_user.is_superuser
    combined_condition = condition1 and condition2
    
    print(f"   user.role == 'ADMIN': {condition1}")
    print(f"   user.is_superuser: {condition2}")
    print(f"   Condition combinée: {combined_condition}")
    print(f"   Propriété is_admin: {admin_user.is_admin}")
    
    # Test avec template Django
    print(f"\n🎨 Test avec template Django:")
    
    template_content = """
    {% if user.role == 'ADMIN' and user.is_superuser %}
    MENU_ADMIN_VISIBLE
    {% else %}
    MENU_ADMIN_CACHE
    {% endif %}
    """
    
    template = Template(template_content)
    context = Context({'user': admin_user})
    result = template.render(context).strip()
    
    print(f"   Résultat du template: {result}")
    
    if result == "MENU_ADMIN_VISIBLE":
        print("   ✅ Le menu Administration sera affiché")
    else:
        print("   ❌ Le menu Administration sera caché")
    
    # Test avec un utilisateur non-admin
    print(f"\n👤 Test avec utilisateur non-admin:")
    
    # Créer un utilisateur RH pour comparaison
    rh_user, created = User.objects.get_or_create(
        username='test_rh',
        defaults={
            'email': '<EMAIL>',
            'role': 'RH',
            'is_superuser': False,
            'is_staff': False
        }
    )
    
    print(f"   Utilisateur RH: {rh_user.username}")
    print(f"   Role: {rh_user.role}")
    print(f"   is_superuser: {rh_user.is_superuser}")
    
    rh_condition = rh_user.role == 'ADMIN' and rh_user.is_superuser
    print(f"   Condition pour RH: {rh_condition}")
    
    context_rh = Context({'user': rh_user})
    result_rh = template.render(context_rh).strip()
    print(f"   Résultat template RH: {result_rh}")
    
    # Test avec un admin sans is_superuser
    print(f"\n⚠️  Test avec admin sans is_superuser:")
    
    fake_admin, created = User.objects.get_or_create(
        username='fake_admin',
        defaults={
            'email': '<EMAIL>',
            'role': 'ADMIN',
            'is_superuser': False,  # Pas de superuser
            'is_staff': False
        }
    )
    
    print(f"   Fake admin: {fake_admin.username}")
    print(f"   Role: {fake_admin.role}")
    print(f"   is_superuser: {fake_admin.is_superuser}")
    print(f"   Propriété is_admin: {fake_admin.is_admin}")
    
    fake_condition = fake_admin.role == 'ADMIN' and fake_admin.is_superuser
    print(f"   Condition pour fake admin: {fake_condition}")
    
    context_fake = Context({'user': fake_admin})
    result_fake = template.render(context_fake).strip()
    print(f"   Résultat template fake admin: {result_fake}")
    
    if result_fake == "MENU_ADMIN_CACHE":
        print("   ✅ Le menu est correctement caché pour les faux admins")
    else:
        print("   ❌ Problème: le menu s'affiche pour les faux admins")
    
    # Résumé
    print(f"\n📊 Résumé:")
    print(f"   ✅ Admin réel ({admin_user.username}): Menu {'visible' if result == 'MENU_ADMIN_VISIBLE' else 'caché'}")
    print(f"   ✅ Utilisateur RH ({rh_user.username}): Menu {'visible' if result_rh == 'MENU_ADMIN_VISIBLE' else 'caché'}")
    print(f"   ✅ Faux admin ({fake_admin.username}): Menu {'visible' if result_fake == 'MENU_ADMIN_VISIBLE' else 'caché'}")
    
    # Nettoyer les utilisateurs de test
    if created:
        rh_user.delete()
        fake_admin.delete()
        print(f"\n🧹 Utilisateurs de test supprimés")
    
    print(f"\n=== Test terminé ===")

if __name__ == '__main__':
    test_admin_display()
