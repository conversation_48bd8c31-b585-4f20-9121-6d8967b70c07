# Diagrammes PlantUML - Système de Gestion des Stagiaires

## 1. Diagramme de Classes

```plantuml
@startuml Diagramme_Classes_Gestion_Stagiaires

!define ENTITY class
!define ENUM enum

' Définition des couleurs
skinparam class {
    BackgroundColor<<User>> LightBlue
    BackgroundColor<<Core>> LightGreen
    BackgroundColor<<Document>> LightYellow
    BackgroundColor<<Task>> LightPink
    BackgroundColor<<Enum>> LightGray
}

' ===== UTILISATEURS =====
ENTITY CustomUser <<User>> {
    - id: Integer
    - username: String
    - email: String
    - first_name: String
    - last_name: String
    - role: String
    - service: ForeignKey(Service)
    - date_creation: DateTime
    - is_active: Boolean
    - is_staff: Boolean
    - is_superuser: Boolean
    --
    + get_role_display(): String
    + get_full_name(): String
}

ENUM RoleChoices <<Enum>> {
    ADMIN
    RH
    ENCADRANT
    STAGIAIRE
}

' ===== SERVICES =====
ENTITY Service <<Core>> {
    - id: Integer
    - nom: String
    - code_service: String
    - description: Text
    - responsable: ForeignKey(CustomUser)
    - actif: Boolean
    - date_creation: DateTime
    - cree_par: ForeignKey(CustomUser)
    --
    + __str__(): String
    + nombre_stagiaires(): Integer
    + stagiaires_actifs(): Integer
}

' ===== THÉMATIQUES ET SUJETS =====
ENTITY Thematique <<Core>> {
    - id: Integer
    - nom: String
    - description: Text
    - service: ForeignKey(Service)
    - active: Boolean
    - date_creation: DateTime
    - cree_par: ForeignKey(CustomUser)
    --
    + __str__(): String
}

ENTITY Sujet <<Core>> {
    - id: Integer
    - titre: String
    - description: Text
    - thematique: ForeignKey(Thematique)
    - service: ForeignKey(Service)
    - encadrant: ForeignKey(CustomUser)
    - technologies_requises: Text
    - niveau_difficulte: String
    - duree_estimee: Integer
    - actif: Boolean
    - date_creation: DateTime
    - cree_par: ForeignKey(CustomUser)
    --
    + __str__(): String
    + est_disponible(): Boolean
}

' ===== STAGIAIRES =====
ENTITY Stagiaire <<Core>> {
    - id: Integer
    - nom: String
    - prenom: String
    - email: String (unique)
    - telephone: String
    - date_naissance: Date
    - departement: String
    - service: ForeignKey(Service)
    - encadrant: ForeignKey(CustomUser)
    - date_debut: Date
    - date_fin: Date
    - statut: String
    - etablissement: String
    - niveau_etude: String
    - specialite: String
    - thematique: ForeignKey(Thematique)
    - sujet: ForeignKey(Sujet)
    - duree_estimee: Integer
    - technologies: Text
    - cv: FileField
    - assurance: FileField
    - convention_stage: FileField
    - statut_convention: String
    - date_validation_convention: DateTime
    - validee_par: ForeignKey(CustomUser)
    - commentaire_convention: Text
    - description_taches: Text
    - statut_taches: String
    - evaluation_encadrant: Text
    - note_finale: String
    - attestation_fin_stage: FileField
    - date_generation_attestation: DateTime
    - attestation_generee_par: ForeignKey(CustomUser)
    - date_creation: DateTime
    - cree_par: ForeignKey(CustomUser)
    --
    + nom_complet(): String
    + duree_stage(): Integer
    + stage_termine(): Boolean
    + peut_generer_attestation(): Boolean
    + convention_validee(): Boolean
    + taches_accomplies(): Boolean
    + peut_valider_convention(user): Boolean
    + peut_generer_attestation_user(user): Boolean
    + get_progress_info(): Dict
}

ENUM StatutChoices <<Enum>> {
    EN_COURS
    TERMINE
    SUSPENDU
    ANNULE
}

ENUM DepartementChoices <<Enum>> {
    IT
    MARKETING
    RH
    FINANCE
    COMMERCIAL
    PRODUCTION
}

ENUM StatutConventionChoices <<Enum>> {
    EN_ATTENTE
    VALIDEE
    REJETEE
    MODIFIEE
}

ENUM StatutTachesChoices <<Enum>> {
    NON_COMMENCEES
    EN_COURS
    PARTIELLEMENT_ACCOMPLIES
    ACCOMPLIES
}

' ===== TÂCHES =====
ENTITY Tache <<Task>> {
    - id: Integer
    - titre: String
    - description: Text
    - stagiaire: ForeignKey(Stagiaire)
    - statut: String
    - priorite: String
    - date_debut: Date
    - date_fin_prevue: Date
    - date_debut_reelle: Date
    - date_fin_reelle: Date
    - creee_par: ForeignKey(CustomUser)
    - date_creation: DateTime
    - date_modification: DateTime
    --
    + __str__(): String
    + est_en_retard(): Boolean
    + duree_prevue(): Integer
    + duree_reelle(): Integer
}

' ===== MISSIONS =====
ENTITY Mission <<Task>> {
    - id: Integer
    - stagiaire: ForeignKey(Stagiaire)
    - titre: String
    - description: Text
    - objectifs: Text
    - livrables_attendus: Text
    - date_debut_prevue: Date
    - date_fin_prevue: Date
    - date_debut_reelle: Date
    - date_fin_reelle: Date
    - priorite: Integer
    - statut: String
    - pourcentage_avancement: Integer
    - commentaire_avancement: Text
    - derniere_mise_a_jour: DateTime
    - creee_par: ForeignKey(CustomUser)
    - date_creation: DateTime
    --
    + __str__(): String
    + duree_prevue(): Integer
    + duree_reelle(): Integer
    + en_retard(): Boolean
}

' ===== RAPPORTS =====
ENTITY RapportStage <<Document>> {
    - id: Integer
    - stagiaire: ForeignKey(Stagiaire)
    - mission: ForeignKey(Mission)
    - titre: String
    - contenu: Text
    - fichier_rapport: FileField
    - statut: String
    - date_soumission: DateTime
    - date_validation: DateTime
    - validé_par: ForeignKey(CustomUser)
    - commentaires_validation: Text
    - note_rapport: Decimal
    - date_creation: DateTime
    --
    + __str__(): String
    + est_valide(): Boolean
    + peut_etre_valide(): Boolean
}

' ===== CONTRATS =====
ENTITY ContratStage <<Document>> {
    - id: Integer
    - reference: String (unique)
    - stagiaire: ForeignKey(Stagiaire)
    - type_contrat: String
    - statut: String
    - titre_stage: String
    - description_missions: Text
    - objectifs_pedagogiques: Text
    - competences_acquises: Text
    - duree_hebdomadaire: Integer
    - gratification_mensuelle: Decimal
    - avantages: Text
    - signature_rh: Boolean
    - date_signature_rh: DateTime
    - signataire_rh: ForeignKey(CustomUser)
    - fichier_contrat: FileField
    - date_creation: DateTime
    - cree_par: ForeignKey(CustomUser)
    - date_modification: DateTime
    - date_expiration: Date
    - commentaires_admin: Text
    - notes_internes: Text
    --
    + __str__(): String
    + est_expire(): Boolean
    + peut_etre_signe(): Boolean
}

' ===== RELATIONS =====
CustomUser ||--o{ Service : "responsable"
CustomUser ||--o{ Stagiaire : "encadrant"
CustomUser ||--o{ Stagiaire : "cree_par"
CustomUser ||--o{ Stagiaire : "validee_par"
CustomUser ||--o{ Stagiaire : "attestation_generee_par"
CustomUser ||--o{ Thematique : "cree_par"
CustomUser ||--o{ Sujet : "encadrant"
CustomUser ||--o{ Sujet : "cree_par"
CustomUser ||--o{ Tache : "creee_par"
CustomUser ||--o{ Mission : "creee_par"
CustomUser ||--o{ RapportStage : "validé_par"
CustomUser ||--o{ ContratStage : "cree_par"
CustomUser ||--o{ ContratStage : "signataire_rh"
CustomUser }o--|| Service : "service"

Service ||--o{ Thematique : "service"
Service ||--o{ Sujet : "service"
Service ||--o{ Stagiaire : "service"

Thematique ||--o{ Sujet : "thematique"
Thematique ||--o{ Stagiaire : "thematique"

Sujet ||--o{ Stagiaire : "sujet"

Stagiaire ||--o{ Tache : "stagiaire"
Stagiaire ||--o{ Mission : "stagiaire"
Stagiaire ||--o{ RapportStage : "stagiaire"
Stagiaire ||--o{ ContratStage : "stagiaire"

Mission ||--o{ RapportStage : "mission"

' Relations avec les énumérations
CustomUser -- RoleChoices
Stagiaire -- StatutChoices
Stagiaire -- DepartementChoices
Stagiaire -- StatutConventionChoices
Stagiaire -- StatutTachesChoices

@enduml
```

## 2. Diagramme de Cas d'Usage

```plantuml
@startuml Diagramme_Cas_Usage_Gestion_Stagiaires

!define ACTOR actor
!define USECASE usecase
!define SYSTEM rectangle

' Définition des couleurs
skinparam actor {
    BackgroundColor<<Admin>> Red
    BackgroundColor<<RH>> Orange
    BackgroundColor<<Encadrant>> Blue
    BackgroundColor<<Stagiaire>> Green
}

skinparam usecase {
    BackgroundColor<<Core>> LightBlue
    BackgroundColor<<Management>> LightGreen
    BackgroundColor<<Document>> LightYellow
    BackgroundColor<<Report>> LightPink
}

' ===== ACTEURS =====
ACTOR "Administrateur" as Admin <<Admin>>
ACTOR "RH Manager" as RH <<RH>>
ACTOR "Encadrant" as Encadrant <<Encadrant>>
ACTOR "Stagiaire" as Stagiaire <<Stagiaire>>

SYSTEM "Système de Gestion des Stagiaires" {

    ' ===== CAS D'USAGE ADMINISTRATEUR =====
    USECASE "Gérer les utilisateurs" as UC_Admin_Users <<Management>>
    USECASE "Gérer les services" as UC_Admin_Services <<Management>>
    USECASE "Configurer le système" as UC_Admin_Config <<Management>>
    USECASE "Consulter tous les rapports" as UC_Admin_Reports <<Report>>
    USECASE "Gérer les thématiques" as UC_Admin_Themes <<Management>>
    USECASE "Gérer les sujets" as UC_Admin_Subjects <<Management>>
    USECASE "Supprimer des données" as UC_Admin_Delete <<Management>>

    ' ===== CAS D'USAGE RH =====
    USECASE "Ajouter un stagiaire" as UC_RH_Add_Intern <<Core>>
    USECASE "Modifier un stagiaire" as UC_RH_Edit_Intern <<Core>>
    USECASE "Valider convention de stage" as UC_RH_Validate_Convention <<Document>>
    USECASE "Créer contrat de stage" as UC_RH_Create_Contract <<Document>>
    USECASE "Signer contrat de stage" as UC_RH_Sign_Contract <<Document>>
    USECASE "Générer attestation de fin" as UC_RH_Generate_Certificate <<Document>>
    USECASE "Consulter liste stagiaires" as UC_RH_View_Interns <<Core>>
    USECASE "Gérer documents stagiaires" as UC_RH_Manage_Docs <<Document>>
    USECASE "Exporter données" as UC_RH_Export <<Report>>

    ' ===== CAS D'USAGE ENCADRANT =====
    USECASE "Consulter ses stagiaires" as UC_Enc_View_Interns <<Core>>
    USECASE "Planifier missions" as UC_Enc_Plan_Missions <<Management>>
    USECASE "Assigner tâches" as UC_Enc_Assign_Tasks <<Management>>
    USECASE "Suivre avancement" as UC_Enc_Track_Progress <<Management>>
    USECASE "Valider rapports" as UC_Enc_Validate_Reports <<Document>>
    USECASE "Évaluer stagiaire" as UC_Enc_Evaluate <<Management>>
    USECASE "Consulter calendrier" as UC_Enc_Calendar <<Core>>
    USECASE "Créer thématiques" as UC_Enc_Create_Themes <<Management>>
    USECASE "Créer sujets" as UC_Enc_Create_Subjects <<Management>>

    ' ===== CAS D'USAGE STAGIAIRE =====
    USECASE "Consulter son profil" as UC_Stg_View_Profile <<Core>>
    USECASE "Consulter ses missions" as UC_Stg_View_Missions <<Core>>
    USECASE "Consulter ses tâches" as UC_Stg_View_Tasks <<Core>>
    USECASE "Soumettre rapport" as UC_Stg_Submit_Report <<Document>>
    USECASE "Télécharger documents" as UC_Stg_Download_Docs <<Document>>
    USECASE "Mettre à jour avancement" as UC_Stg_Update_Progress <<Management>>

    ' ===== CAS D'USAGE COMMUNS =====
    USECASE "Se connecter" as UC_Login <<Core>>
    USECASE "Se déconnecter" as UC_Logout <<Core>>
    USECASE "Modifier mot de passe" as UC_Change_Password <<Core>>
    USECASE "Consulter dashboard" as UC_Dashboard <<Core>>

}

' ===== RELATIONS ADMINISTRATEUR =====
Admin --> UC_Admin_Users
Admin --> UC_Admin_Services
Admin --> UC_Admin_Config
Admin --> UC_Admin_Reports
Admin --> UC_Admin_Themes
Admin --> UC_Admin_Subjects
Admin --> UC_Admin_Delete
Admin --> UC_Login
Admin --> UC_Logout
Admin --> UC_Change_Password
Admin --> UC_Dashboard

' ===== RELATIONS RH =====
RH --> UC_RH_Add_Intern
RH --> UC_RH_Edit_Intern
RH --> UC_RH_Validate_Convention
RH --> UC_RH_Create_Contract
RH --> UC_RH_Sign_Contract
RH --> UC_RH_Generate_Certificate
RH --> UC_RH_View_Interns
RH --> UC_RH_Manage_Docs
RH --> UC_RH_Export
RH --> UC_Login
RH --> UC_Logout
RH --> UC_Change_Password
RH --> UC_Dashboard

' ===== RELATIONS ENCADRANT =====
Encadrant --> UC_Enc_View_Interns
Encadrant --> UC_Enc_Plan_Missions
Encadrant --> UC_Enc_Assign_Tasks
Encadrant --> UC_Enc_Track_Progress
Encadrant --> UC_Enc_Validate_Reports
Encadrant --> UC_Enc_Evaluate
Encadrant --> UC_Enc_Calendar
Encadrant --> UC_Enc_Create_Themes
Encadrant --> UC_Enc_Create_Subjects
Encadrant --> UC_Login
Encadrant --> UC_Logout
Encadrant --> UC_Change_Password
Encadrant --> UC_Dashboard

' ===== RELATIONS STAGIAIRE =====
Stagiaire --> UC_Stg_View_Profile
Stagiaire --> UC_Stg_View_Missions
Stagiaire --> UC_Stg_View_Tasks
Stagiaire --> UC_Stg_Submit_Report
Stagiaire --> UC_Stg_Download_Docs
Stagiaire --> UC_Stg_Update_Progress
Stagiaire --> UC_Login
Stagiaire --> UC_Logout
Stagiaire --> UC_Change_Password
Stagiaire --> UC_Dashboard

' ===== RELATIONS D'INCLUSION =====
UC_RH_Add_Intern ..> UC_RH_Manage_Docs : <<include>>
UC_RH_Create_Contract ..> UC_RH_Validate_Convention : <<include>>
UC_Enc_Plan_Missions ..> UC_Enc_View_Interns : <<include>>
UC_Enc_Assign_Tasks ..> UC_Enc_View_Interns : <<include>>
UC_RH_Generate_Certificate ..> UC_RH_Validate_Convention : <<include>>

' ===== RELATIONS D'EXTENSION =====
UC_RH_Export ..> UC_RH_View_Interns : <<extend>>
UC_Enc_Evaluate ..> UC_Enc_Track_Progress : <<extend>>
UC_Admin_Delete ..> UC_Admin_Users : <<extend>>

@enduml
```

## 3. Diagramme de Séquence - Processus d'Ajout de Stagiaire

```plantuml
@startuml Sequence_Ajout_Stagiaire

participant "RH Manager" as RH
participant "Interface Web" as UI
participant "StagiaireView" as View
participant "StagiaireForm" as Form
participant "Stagiaire Model" as Model
participant "Base de Données" as DB
participant "Email Service" as Email

RH -> UI : Accéder à "Ajouter Stagiaire"
UI -> View : GET /stagiaires/add/
View -> Form : Créer formulaire vide
Form -> UI : Afficher formulaire
UI -> RH : Formulaire d'ajout

RH -> UI : Remplir et soumettre formulaire
UI -> View : POST /stagiaires/add/ (données)
View -> Form : Valider données
Form -> Form : Vérifier contraintes

alt Données valides
    Form -> Model : Créer instance Stagiaire
    Model -> DB : INSERT stagiaire
    DB -> Model : Confirmation création
    Model -> View : Stagiaire créé

    View -> Email : Envoyer notification encadrant
    Email -> View : Email envoyé

    View -> UI : Redirection vers liste
    UI -> RH : Message de succès
else Données invalides
    Form -> View : Erreurs de validation
    View -> UI : Formulaire avec erreurs
    UI -> RH : Afficher erreurs
end

@enduml
```

## 4. Diagramme de Séquence - Processus de Validation de Convention

```plantuml
@startuml Sequence_Validation_Convention

participant "Stagiaire" as STG
participant "RH Manager" as RH
participant "Interface Web" as UI
participant "StagiaireView" as View
participant "Stagiaire Model" as Model
participant "Base de Données" as DB
participant "File System" as FS
participant "Email Service" as Email

STG -> UI : Télécharger convention signée
UI -> View : POST /stagiaires/{id}/upload-convention/
View -> FS : Sauvegarder fichier
FS -> View : Fichier sauvegardé
View -> Model : Mettre à jour convention_stage
Model -> DB : UPDATE stagiaire
View -> UI : Confirmation upload
UI -> STG : Convention téléchargée

RH -> UI : Accéder à liste stagiaires
UI -> View : GET /stagiaires/
View -> Model : Récupérer stagiaires avec conventions
Model -> DB : SELECT stagiaires
DB -> Model : Données stagiaires
Model -> View : Liste stagiaires
View -> UI : Afficher liste
UI -> RH : Liste avec conventions à valider

RH -> UI : Cliquer "Valider convention"
UI -> View : POST /stagiaires/{id}/validate-convention/
View -> Model : Vérifier permissions RH

alt RH autorisé
    View -> Model : Mettre à jour statut_convention = 'VALIDEE'
    Model -> DB : UPDATE stagiaire
    View -> Model : Enregistrer validateur et date
    Model -> DB : UPDATE validation info

    View -> Email : Notifier stagiaire et encadrant
    Email -> View : Notifications envoyées

    View -> UI : Redirection avec succès
    UI -> RH : Message "Convention validée"
else RH non autorisé
    View -> UI : Erreur 403
    UI -> RH : "Accès refusé"
end

@enduml
```

## 5. Diagramme de Séquence - Filtrage des Stagiaires par Service

```plantuml
@startuml Sequence_Filtrage_Stagiaires

participant "Encadrant" as ENC
participant "Interface Web" as UI
participant "StagiaireView" as View
participant "Filter Function" as Filter
participant "Stagiaire Model" as Model
participant "Service Model" as ServiceModel
participant "Base de Données" as DB

ENC -> UI : Se connecter
UI -> View : Authentification
View -> UI : Session créée
UI -> ENC : Dashboard encadrant

ENC -> UI : Accéder à "Liste Stagiaires"
UI -> View : GET /stagiaires/
View -> Filter : filter_stagiaires_by_user_role(user)

Filter -> ServiceModel : Récupérer service de l'encadrant
ServiceModel -> DB : SELECT service WHERE user_id = {encadrant_id}
DB -> ServiceModel : Service "informatique"
ServiceModel -> Filter : Service récupéré

Filter -> Filter : Mapper service → département
note right : "informatique" → "IT"

Filter -> Model : Filtrer stagiaires par département
Model -> DB : SELECT * FROM stagiaires WHERE departement = 'IT'
DB -> Model : Stagiaires du département IT
Model -> Filter : Liste filtrée

Filter -> View : Stagiaires autorisés
View -> UI : Afficher stagiaires filtrés
UI -> ENC : Liste des stagiaires IT uniquement

note over ENC, DB
    L'encadrant ne voit que les stagiaires
    de son département correspondant
end note

@enduml
```

## 6. Diagramme d'Activité - Processus Complet de Gestion d'un Stage

```plantuml
@startuml Activite_Gestion_Stage

start

:RH ajoute un nouveau stagiaire;
:Système envoie notification à l'encadrant;

fork
    :Stagiaire télécharge convention de stage;
    :RH valide la convention;
fork again
    :Encadrant crée thématiques et sujets;
    :Encadrant assigne sujet au stagiaire;
end fork

:RH crée le contrat de stage;
:RH signe le contrat;

:Stage commence;

fork
    :Encadrant planifie les missions;
    :Encadrant assigne les tâches;

    repeat
        :Stagiaire travaille sur les tâches;
        :Encadrant suit l'avancement;
        :Stagiaire met à jour le statut;
    repeat while (Tâches non terminées?)

fork again
    repeat
        :Stagiaire rédige rapport;
        :Stagiaire soumet rapport;
        :Encadrant valide rapport;
    repeat while (Rapports à soumettre?)
end fork

:Encadrant évalue le stagiaire;

if (Toutes les tâches accomplies?) then (oui)
    :Statut tâches = "ACCOMPLIES";
else (non)
    :Statut tâches = "PARTIELLEMENT_ACCOMPLIES";
endif

:Stage se termine;

if (Stage terminé ET tâches accomplies ET convention validée?) then (oui)
    :RH peut générer l'attestation;
    :RH génère l'attestation de fin de stage;
    :Système envoie attestation au stagiaire;
else (non)
    :Attestation non disponible;
endif

stop

@enduml
```

## 7. Diagramme d'État - Cycle de Vie d'un Stagiaire

```plantuml
@startuml Etats_Stagiaire

[*] --> Créé : RH ajoute stagiaire

state Créé {
    Créé : statut = EN_COURS
    Créé : convention = EN_ATTENTE
    Créé : tâches = NON_COMMENCEES
}

Créé --> ConventionEnAttente : Convention téléchargée

state ConventionEnAttente {
    ConventionEnAttente : statut = EN_COURS
    ConventionEnAttente : convention = EN_ATTENTE
    ConventionEnAttente : tâches = NON_COMMENCEES
}

ConventionEnAttente --> ConventionValidée : RH valide convention
ConventionEnAttente --> ConventionRejetée : RH rejette convention

state ConventionRejetée {
    ConventionRejetée : statut = EN_COURS
    ConventionRejetée : convention = REJETEE
    ConventionRejetée : tâches = NON_COMMENCEES
}

ConventionRejetée --> ConventionEnAttente : Nouvelle convention

state ConventionValidée {
    ConventionValidée : statut = EN_COURS
    ConventionValidée : convention = VALIDEE
    ConventionValidée : tâches = NON_COMMENCEES
}

ConventionValidée --> TâchesEnCours : Encadrant assigne tâches

state TâchesEnCours {
    TâchesEnCours : statut = EN_COURS
    TâchesEnCours : convention = VALIDEE
    TâchesEnCours : tâches = EN_COURS
}

TâchesEnCours --> TâchesPartielles : Avancement partiel
TâchesEnCours --> TâchesAccomplies : Toutes tâches terminées

state TâchesPartielles {
    TâchesPartielles : statut = EN_COURS
    TâchesPartielles : convention = VALIDEE
    TâchesPartielles : tâches = PARTIELLEMENT_ACCOMPLIES
}

TâchesPartielles --> TâchesEnCours : Reprise du travail
TâchesPartielles --> TâchesAccomplies : Finalisation

state TâchesAccomplies {
    TâchesAccomplies : statut = EN_COURS
    TâchesAccomplies : convention = VALIDEE
    TâchesAccomplies : tâches = ACCOMPLIES
}

TâchesAccomplies --> StageTerminé : Date de fin atteinte

state StageTerminé {
    StageTerminé : statut = TERMINE
    StageTerminé : convention = VALIDEE
    StageTerminé : tâches = ACCOMPLIES
}

StageTerminé --> AttestationGénérée : RH génère attestation

state AttestationGénérée {
    AttestationGénérée : statut = TERMINE
    AttestationGénérée : convention = VALIDEE
    AttestationGénérée : tâches = ACCOMPLIES
    AttestationGénérée : attestation = GENEREE
}

' États d'exception
Créé --> StageSuspendu : Suspension
ConventionValidée --> StageSuspendu : Suspension
TâchesEnCours --> StageSuspendu : Suspension
TâchesPartielles --> StageSuspendu : Suspension

state StageSuspendu {
    StageSuspendu : statut = SUSPENDU
}

StageSuspendu --> TâchesEnCours : Reprise du stage
StageSuspendu --> StageAnnulé : Annulation définitive

Créé --> StageAnnulé : Annulation
ConventionEnAttente --> StageAnnulé : Annulation
ConventionRejetée --> StageAnnulé : Annulation

state StageAnnulé {
    StageAnnulé : statut = ANNULE
}

AttestationGénérée --> [*]
StageAnnulé --> [*]

@enduml
```
