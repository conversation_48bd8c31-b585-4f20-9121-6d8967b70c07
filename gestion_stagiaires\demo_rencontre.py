#!/usr/bin/env python
"""
Démonstration complète de la fonctionnalité de rencontre avec les stagiaires
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service, Tache
from django.utils import timezone
from datetime import date, timedelta

User = get_user_model()

def demo_rencontre_functionality():
    """Démonstration complète de la fonctionnalité de rencontre"""
    
    print("🎯 DÉMONSTRATION - FONCTIONNALITÉ RENCONTRE ENCADRANT-STAGIAIRE")
    print("=" * 70)
    
    # 1. Récupérer l'encadrant de test
    try:
        encadrant = User.objects.get(username='encadrant_test')
        print(f"👤 Encadrant connecté : {encadrant.get_full_name()} ({encadrant.username})")
        print(f"   Service : {encadrant.service.nom}")
        print(f"   Email : {encadrant.email}")
    except User.DoesNotExist:
        print("❌ Encadrant de test non trouvé. Exécutez d'abord create_test_user.py")
        return
    
    # 2. Récupérer les stagiaires de son service
    stagiaires_service = Stagiaire.objects.filter(service=encadrant.service)
    print(f"\n📊 Stagiaires du service {encadrant.service.nom} : {stagiaires_service.count()}")
    
    if not stagiaires_service.exists():
        print("   Aucun stagiaire dans ce service. Création d'un stagiaire de test...")
        stagiaire = Stagiaire.objects.create(
            nom='Alami',
            prenom='Fatima',
            email='<EMAIL>',
            telephone='0612345678',
            date_naissance=date(2001, 5, 15),
            departement='IT',
            service=encadrant.service,
            encadrant=encadrant,
            date_debut=date.today(),
            date_fin=date.today() + timedelta(days=60),
            etablissement='ENSIAS',
            niveau_etude='Ingénieur',
            specialite='Génie Informatique',
            statut='EN_COURS'
        )
        print(f"   ✅ Stagiaire créé : {stagiaire.nom_complet}")
    else:
        stagiaire = stagiaires_service.first()
        print(f"   📝 Stagiaire sélectionné : {stagiaire.nom_complet}")
    
    # 3. Afficher les informations du stagiaire
    print(f"\n👩‍🎓 INFORMATIONS DU STAGIAIRE :")
    print(f"   • Nom complet : {stagiaire.nom_complet}")
    print(f"   • Email : {stagiaire.email}")
    print(f"   • Service : {stagiaire.service.nom}")
    print(f"   • Encadrant : {stagiaire.encadrant.get_full_name() if stagiaire.encadrant else 'Non assigné'}")
    print(f"   • Période : {stagiaire.date_debut} → {stagiaire.date_fin}")
    print(f"   • Statut : {stagiaire.get_statut_display()}")
    
    # 4. Créer des tâches pour la rencontre
    print(f"\n📝 CRÉATION DE TÂCHES LORS DE LA RENCONTRE :")
    
    taches_rencontre = [
        {
            'titre': 'Prise en main de l\'environnement',
            'description': 'Se familiariser avec les outils et l\'environnement de développement de l\'équipe',
            'priorite': 'HAUTE',
            'date_fin_prevue': date.today() + timedelta(days=3)
        },
        {
            'titre': 'Étude du cahier des charges',
            'description': 'Analyser et comprendre les spécifications du projet assigné',
            'priorite': 'HAUTE',
            'date_fin_prevue': date.today() + timedelta(days=5)
        },
        {
            'titre': 'Conception de la base de données',
            'description': 'Concevoir le modèle de données pour le module assigné',
            'priorite': 'NORMALE',
            'date_fin_prevue': date.today() + timedelta(days=10)
        },
        {
            'titre': 'Développement du prototype',
            'description': 'Développer un premier prototype fonctionnel',
            'priorite': 'NORMALE',
            'date_fin_prevue': date.today() + timedelta(days=20)
        },
        {
            'titre': 'Rédaction de la documentation',
            'description': 'Documenter le code et les procédures développées',
            'priorite': 'BASSE',
            'date_fin_prevue': date.today() + timedelta(days=25)
        }
    ]
    
    # Supprimer les anciennes tâches pour cette démo
    Tache.objects.filter(stagiaire=stagiaire, creee_par=encadrant).delete()
    
    taches_creees = []
    for i, tache_data in enumerate(taches_rencontre, 1):
        tache = Tache.objects.create(
            stagiaire=stagiaire,
            creee_par=encadrant,
            **tache_data
        )
        taches_creees.append(tache)
        
        priorite_icon = "🔴" if tache.priorite == "HAUTE" else "🟡" if tache.priorite == "NORMALE" else "🟢"
        print(f"   {i}. {priorite_icon} {tache.titre}")
        print(f"      📅 Échéance : {tache.date_fin_prevue.strftime('%d/%m/%Y')}")
        print(f"      📝 {tache.description[:60]}...")
        print()
    
    # 5. Résumé des tâches
    total_taches = Tache.objects.filter(stagiaire=stagiaire).count()
    taches_haute_priorite = Tache.objects.filter(stagiaire=stagiaire, priorite='HAUTE').count()
    
    print(f"📊 RÉSUMÉ DES TÂCHES :")
    print(f"   • Total des tâches : {total_taches}")
    print(f"   • Tâches haute priorité : {taches_haute_priorite}")
    print(f"   • Tâches créées aujourd'hui : {len(taches_creees)}")
    
    # 6. Simulation d'envoi d'email
    print(f"\n📧 SIMULATION D'ENVOI D'EMAIL :")
    print(f"   Destinataire : {stagiaire.email}")
    print(f"   Expéditeur : {encadrant.email}")
    print(f"   Sujet : Tâches assignées - Stage {stagiaire.nom_complet}")
    print(f"   Contenu :")
    print(f"   ┌─────────────────────────────────────────────────────────────┐")
    print(f"   │ Bonjour {stagiaire.prenom},                                  │")
    print(f"   │                                                             │")
    print(f"   │ Voici les tâches qui vous ont été assignées lors de        │")
    print(f"   │ notre rencontre :                                           │")
    print(f"   │                                                             │")
    for i, tache in enumerate(taches_creees, 1):
        print(f"   │ {i}. {tache.titre:<50} │")
        if tache.date_fin_prevue:
            print(f"   │    Échéance : {tache.date_fin_prevue.strftime('%d/%m/%Y'):<40} │")
    print(f"   │                                                             │")
    print(f"   │ Cordialement,                                               │")
    print(f"   │ {encadrant.get_full_name():<50} │")
    print(f"   │ Encadrant de stage                                          │")
    print(f"   └─────────────────────────────────────────────────────────────┘")
    
    # 7. URLs importantes
    print(f"\n🔗 URLS POUR TESTER LA FONCTIONNALITÉ :")
    print(f"   • Connexion : http://127.0.0.1:8000/login/")
    print(f"     Username: {encadrant.username}")
    print(f"     Password: test123")
    print(f"   • Liste des stagiaires : http://127.0.0.1:8000/stagiaires/")
    print(f"   • Rencontre avec {stagiaire.prenom} : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/rencontre/")
    print(f"   • Détail du stagiaire : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/")
    
    print(f"\n✅ DÉMONSTRATION TERMINÉE AVEC SUCCÈS !")
    print(f"🎯 La fonctionnalité de rencontre est maintenant opérationnelle.")
    
    return {
        'encadrant': encadrant,
        'stagiaire': stagiaire,
        'taches': taches_creees
    }

if __name__ == '__main__':
    try:
        demo_rencontre_functionality()
    except Exception as e:
        print(f"❌ Erreur lors de la démonstration : {e}")
        import traceback
        traceback.print_exc()
