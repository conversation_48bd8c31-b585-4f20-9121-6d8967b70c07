#!/usr/bin/env python
"""
Debug de la vue sujets pour comprendre pourquoi le sujet "js" s'affiche
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import RequestFactory
from stagiaires.models import Sujet
from stagiaires.views import sujets_list_view

User = get_user_model()

def debug_vue_sujets():
    """Debug de la vue sujets"""
    
    print("=== DEBUG VUE SUJETS ===")
    
    # Récupérer l'encadrant Marketing
    encadrant_marketing = User.objects.filter(
        role='ENCADRANT',
        service__nom__icontains='marketing'
    ).first()
    
    print(f"👨‍💼 Encadrant: {encadrant_marketing.get_full_name()}")
    print(f"🏢 Service: {encadrant_marketing.service.nom}")
    
    # Créer une requête factice
    factory = RequestFactory()
    request = factory.get('/sujets/')
    request.user = encadrant_marketing
    
    # Simuler la logique de la vue
    print(f"\n🔍 SIMULATION LOGIQUE VUE:")
    print(f"   User role: {request.user.role}")
    
    if request.user.role == 'ENCADRANT':
        if hasattr(request.user, 'service') and request.user.service:
            sujets = Sujet.objects.filter(service=request.user.service).order_by('-date_creation')
            print(f"   Filtrage: service={request.user.service.nom}")
        else:
            sujets = Sujet.objects.filter(encadrant=request.user).order_by('-date_creation')
            print(f"   Filtrage: encadrant={request.user.get_full_name()}")
    else:
        sujets = Sujet.objects.all().order_by('-date_creation')
        print(f"   Filtrage: tous les sujets")
    
    print(f"   Sujets trouvés: {sujets.count()}")
    
    # Afficher les sujets
    print(f"\n📝 SUJETS RETOURNÉS:")
    for sujet in sujets:
        print(f"   • {sujet.titre} (Service: {sujet.service.nom if sujet.service else 'Aucun'})")
    
    # Vérifier spécifiquement le sujet "js"
    sujet_js = Sujet.objects.filter(titre__icontains='js').first()
    if sujet_js:
        print(f"\n🔍 ANALYSE SUJET 'JS':")
        print(f"   Titre: {sujet_js.titre}")
        print(f"   Service: {sujet_js.service.nom if sujet_js.service else 'Aucun'}")
        print(f"   Service ID: {sujet_js.service.id if sujet_js.service else 'Aucun'}")
        print(f"   Encadrant service: {encadrant_marketing.service.nom}")
        print(f"   Encadrant service ID: {encadrant_marketing.service.id}")
        
        if sujet_js in sujets:
            print(f"   ❌ INCLUS dans les sujets retournés")
            
            # Vérifier pourquoi
            if sujet_js.service == encadrant_marketing.service:
                print(f"   Raison: Même service")
            else:
                print(f"   Raison: INCONNUE - Services différents mais inclus quand même")
        else:
            print(f"   ✅ PAS INCLUS dans les sujets retournés")
    
    # Test direct de la requête
    print(f"\n🧪 TEST REQUÊTE DIRECTE:")
    
    sujets_marketing = Sujet.objects.filter(service=encadrant_marketing.service)
    print(f"   Sujets service Marketing: {sujets_marketing.count()}")
    
    for sujet in sujets_marketing:
        print(f"      • {sujet.titre}")
    
    # Vérifier si le sujet "js" est dans cette requête
    if sujet_js and sujet_js in sujets_marketing:
        print(f"   ❌ Sujet 'js' inclus dans la requête directe")
        
        # Vérifier les données du sujet
        print(f"\n🔍 DONNÉES SUJET 'JS':")
        print(f"   ID: {sujet_js.id}")
        print(f"   Service: {sujet_js.service}")
        print(f"   Service ID: {sujet_js.service.id}")
        print(f"   Service nom: {sujet_js.service.nom}")
        
        # Vérifier les données du service Marketing
        print(f"\n🔍 DONNÉES SERVICE MARKETING:")
        print(f"   Service: {encadrant_marketing.service}")
        print(f"   Service ID: {encadrant_marketing.service.id}")
        print(f"   Service nom: {encadrant_marketing.service.nom}")
        
        # Comparaison directe
        print(f"\n🔍 COMPARAISON:")
        print(f"   sujet_js.service == encadrant_marketing.service: {sujet_js.service == encadrant_marketing.service}")
        print(f"   sujet_js.service.id == encadrant_marketing.service.id: {sujet_js.service.id == encadrant_marketing.service.id}")
        
    else:
        print(f"   ✅ Sujet 'js' PAS inclus dans la requête directe")

if __name__ == '__main__':
    debug_vue_sujets()
