#!/usr/bin/env python
"""
Test en temps réel de l'interface d'administration avec un vrai navigateur
"""

import os
import sys
import django
import time
from datetime import date, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service
from django.test import Client

User = get_user_model()

def test_real_browser():
    """Test avec un vrai navigateur"""
    
    print("=== Test en temps réel de l'interface d'administration ===")
    print("🌐 Serveur Django démarré sur http://127.0.0.1:8000")
    print()
    
    # Informations de connexion
    admin_user = User.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ Aucun administrateur trouvé")
        return
    
    print(f"👤 Utilisateur admin disponible:")
    print(f"   Username: {admin_user.username}")
    print(f"   Email: {admin_user.email}")
    print(f"   is_superuser: {admin_user.is_superuser}")
    print()
    
    # Informations sur les encadrants
    encadrants = User.objects.filter(role='ENCADRANT', is_active=True)
    print(f"👨‍🏫 Encadrants disponibles: {encadrants.count()}")
    for enc in encadrants[:3]:
        service_nom = enc.service.nom if enc.service else "Aucun service"
        print(f"   • {enc.get_full_name()} ({enc.username}) - {service_nom}")
    print()
    
    # Informations sur les services
    services = Service.objects.filter(actif=True)
    print(f"🏢 Services disponibles: {services.count()}")
    for service in services:
        print(f"   • {service.nom} ({service.code_service})")
    print()
    
    # Générer des données de test uniques
    timestamp = int(time.time())
    test_email = f"test.real.{timestamp}@example.com"
    
    print(f"📧 Email de test unique: {test_email}")
    print()
    
    # Instructions pour le test manuel
    print("📋 INSTRUCTIONS POUR LE TEST MANUEL:")
    print("=" * 50)
    print()
    print("1. Ouvrez votre navigateur et allez sur:")
    print("   http://127.0.0.1:8000/admin/")
    print()
    print("2. Connectez-vous avec:")
    print(f"   Username: {admin_user.username}")
    print("   Password: (votre mot de passe admin)")
    print()
    print("3. Cliquez sur 'Stagiaires' puis 'Ajouter'")
    print()
    print("4. Remplissez le formulaire avec ces données EXACTES:")
    print("   " + "-" * 45)
    print(f"   Nom: TestReal{timestamp}")
    print(f"   Prénom: Stagiaire")
    print(f"   Email: {test_email}")
    print(f"   Téléphone: 0123456789")
    print(f"   Date de naissance: 01/01/2000")
    print(f"   Département: IT")
    if encadrants.exists():
        enc = encadrants.first()
        print(f"   Service: {enc.service.nom if enc.service else '(laisser vide)'}")
        print(f"   Encadrant: {enc.get_full_name()}")
    print(f"   Date de début: {date.today().strftime('%d/%m/%Y')}")
    print(f"   Date de fin: {(date.today() + timedelta(days=90)).strftime('%d/%m/%Y')}")
    print(f"   Statut: En cours")
    print(f"   Établissement: Université Test Real")
    print(f"   Niveau d'étude: Master")
    print(f"   Spécialité: Informatique")
    print("   " + "-" * 45)
    print()
    print("5. Laissez TOUS les autres champs vides")
    print()
    print("6. Cliquez sur 'Enregistrer'")
    print()
    print("7. Observez ce qui se passe:")
    print("   ✅ Si ça marche: vous serez redirigé vers la liste des stagiaires")
    print("   ❌ Si ça ne marche pas: notez EXACTEMENT les erreurs affichées")
    print()
    print("8. Appuyez sur F12 pour ouvrir la console du navigateur")
    print("   et vérifiez s'il y a des erreurs JavaScript")
    print()
    
    # Attendre et vérifier
    print("⏳ Attendez que vous ayez testé, puis appuyez sur Entrée...")
    input()
    
    # Vérifier si le stagiaire a été créé
    print("\n🔍 Vérification en base de données...")
    
    stagiaire_cree = Stagiaire.objects.filter(email=test_email).first()
    if stagiaire_cree:
        print("✅ SUCCÈS! Le stagiaire a été créé:")
        print(f"   ID: {stagiaire_cree.id}")
        print(f"   Nom complet: {stagiaire_cree.nom_complet}")
        print(f"   Email: {stagiaire_cree.email}")
        print(f"   Créé par: {stagiaire_cree.cree_par}")
        print(f"   Date création: {stagiaire_cree.date_creation}")
        print(f"   Encadrant: {stagiaire_cree.encadrant}")
        print(f"   Service: {stagiaire_cree.service}")
        
        # Proposer de nettoyer
        print(f"\n🧹 Voulez-vous supprimer ce stagiaire de test? (y/N)")
        response = input().lower()
        if response == 'y':
            stagiaire_cree.delete()
            print("✅ Stagiaire de test supprimé")
        else:
            print("ℹ️ Stagiaire de test conservé")
    else:
        print("❌ ÉCHEC! Le stagiaire n'a pas été créé en base de données")
        print()
        print("🔍 Diagnostics supplémentaires:")
        
        # Vérifier les derniers stagiaires créés
        derniers_stagiaires = Stagiaire.objects.order_by('-date_creation')[:5]
        print(f"   Derniers stagiaires créés ({derniers_stagiaires.count()}):")
        for s in derniers_stagiaires:
            print(f"   • {s.nom_complet} - {s.email} - {s.date_creation.strftime('%Y-%m-%d %H:%M')}")
        
        # Vérifier s'il y a des stagiaires avec un email similaire
        stagiaires_similaires = Stagiaire.objects.filter(email__contains='test.real')
        print(f"   Stagiaires de test existants ({stagiaires_similaires.count()}):")
        for s in stagiaires_similaires:
            print(f"   • {s.nom_complet} - {s.email}")
    
    print(f"\n{'='*50}")
    print("📊 RÉSUMÉ DU TEST:")
    if stagiaire_cree:
        print("🎉 L'interface d'administration fonctionne correctement!")
        print("   Le problème pourrait être lié à:")
        print("   • Des données spécifiques que vous utilisez")
        print("   • Des erreurs JavaScript dans votre navigateur")
        print("   • Des extensions de navigateur qui interfèrent")
    else:
        print("❌ L'interface d'administration a encore un problème")
        print("   Actions recommandées:")
        print("   • Vérifiez les erreurs exactes affichées dans le formulaire")
        print("   • Vérifiez la console JavaScript (F12)")
        print("   • Essayez avec un navigateur différent")
        print("   • Vérifiez que tous les champs obligatoires sont remplis")
    print(f"{'='*50}")

if __name__ == '__main__':
    test_real_browser()
