{% extends 'stagiaires/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<!-- Bootstrap CSS -->
<link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.0/css/bootstrap.min.css" rel="stylesheet">
<!-- FullCalendar CSS -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/1.6.4/fullcalendar.min.css" rel="stylesheet">
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css?family=Roboto:100,100i,300,300i,400,400i,500,500i,700,700i,900,900i" rel="stylesheet">

<style>
    body {
        margin-bottom: 40px;
        margin-top: 40px;
        text-align: center;
        font-size: 14px;
        font-family: 'Roboto', sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    #wrap {
        width: 95%;
        max-width: 1400px;
        margin: 0 auto;
    }

    #calendar {
        margin: 0 auto;
        width: 100%;
        background-color: #FFFFFF;
        border-radius: 10px;
        box-shadow: 0px 0px 25px 5px rgba(0,0,0,0.2);
        -webkit-box-shadow: 0px 0px 25px 5px rgba(0,0,0,0.2);
        -moz-box-shadow: 0px 0px 25px 5px rgba(0,0,0,0.2);
        overflow: hidden;
    }

    /* FullCalendar Custom Styles */
    td.fc-day {
        background: #FFF !important;
        font-family: 'Roboto', sans-serif;
        height: 120px !important;
    }

    td.fc-today {
        background: #FFF !important;
        position: relative;
    }

    .fc-first th {
        font-family: 'Roboto', sans-serif;
        background: #667eea !important;
        color: #FFF;
        font-size: 16px !important;
        font-weight: 500 !important;
        padding: 15px !important;
    }

    .fc-event-inner {
        font-family: 'Roboto', sans-serif;
        color: #FFF !important;
        font-size: 13px !important;
        font-weight: 600 !important;
        padding: 6px 8px !important;
        border-radius: 4px !important;
    }

    .fc-header-title h2 {
        margin-top: 0;
        white-space: nowrap;
        font-size: 36px;
        font-weight: 300;
        margin-bottom: 10px;
        font-family: 'Roboto', sans-serif;
        color: #333;
    }

    span.fc-button {
        font-family: 'Roboto', sans-serif;
        border-color: #667eea;
        color: #667eea;
        font-size: 14px;
        padding: 8px 16px;
    }

    .fc-state-down, .fc-state-active {
        background-color: #667eea !important;
        color: #FFF !important;
    }

    .fc-state-default {
        border-color: #667eea;
        color: #667eea;
    }

    .fc-state-hover {
        color: #667eea;
        background-color: rgba(102, 126, 234, 0.1);
    }

    .fc-week .fc-day > div .fc-day-number {
        font-size: 18px;
        margin: 6px;
        min-width: 28px;
        padding: 8px;
        text-align: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        transition: all 0.2s;
        font-weight: 600;
    }

    .fc-week .fc-day:hover .fc-day-number {
        background-color: #667eea;
        color: #FFFFFF;
        transform: scale(1.1);
    }

    .fc-state-highlight > div > div.fc-day-number {
        background-color: #ff6b8a;
        color: #FFFFFF;
        border-radius: 50%;
        margin: 6px;
    }

    /* Styles pour les événements stagiaires */
    .stagiaire-event {
        border-radius: 6px !important;
        border: none !important;
        font-weight: 600 !important;
        font-size: 12px !important;
        padding: 4px 8px !important;
        margin: 1px 0 !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
    }

    .stagiaire-event:hover {
        transform: translateY(-1px) scale(1.02) !important;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;
    }

    /* Légende des stagiaires */
    .stagiaires-legend {
        background: white;
        padding: 25px;
        margin-top: 25px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .legend-title {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }

    .legend-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 15px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 8px;
        background: #f8f9fa;
        transition: all 0.2s;
        border-left: 4px solid transparent;
    }

    .legend-item:hover {
        background: #e9ecef;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .legend-color {
        width: 28px;
        height: 28px;
        border-radius: 8px;
        margin-right: 15px;
        flex-shrink: 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .legend-text {
        font-size: 15px;
        color: #333;
        font-weight: 600;
    }

    .legend-details {
        font-size: 13px;
        color: #666;
        margin-top: 3px;
    }

    /* Animation pour le chargement */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    #calendar {
        animation: fadeInUp 0.6s ease-out;
    }

    .stagiaires-legend {
        animation: fadeInUp 0.8s ease-out;
    }

    /* Responsive */
    @media (max-width: 768px) {
        #wrap {
            width: 98%;
        }

        .fc-header-title h2 {
            font-size: 24px;
        }

        .legend-grid {
            grid-template-columns: 1fr;
        }

        td.fc-day {
            height: 80px !important;
        }

        .fc-week .fc-day > div .fc-day-number {
            font-size: 14px;
            width: 28px;
            height: 28px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-1.11.1.min.js"></script>
<!-- Bootstrap JS -->
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.0/js/bootstrap.min.js"></script>
<!-- FullCalendar JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/1.6.4/fullcalendar.min.js"></script>

<script>
$(document).ready(function() {
    var date = new Date();
    var d = date.getDate();
    var m = date.getMonth();
    var y = date.getFullYear();

    // Données des stagiaires depuis Django avec indication du service
    var stagiaireEvents = [
        {% for data in stagiaires_data %}
        {
            title: '{{ data.stagiaire.prenom }} {{ data.stagiaire.nom }}{% if data.stagiaire.service %} ({{ data.stagiaire.service.nom }}){% endif %}',
            start: new Date('{{ data.stagiaire.date_debut|date:"Y-m-d" }}'),
            end: new Date('{{ data.stagiaire.date_fin|date:"Y-m-d" }}'),
            backgroundColor: '{{ data.color }}',
            borderColor: '{{ data.color }}',
            textColor: '#FFFFFF',
            className: 'stagiaire-event',
            description: '{{ data.stagiaire.service.nom|default:"Aucun service" }} - {{ data.stagiaire.etablissement }}',
            serviceName: '{{ data.stagiaire.service.nom|default:"Sans service" }}',
            encadrant: '{{ data.stagiaire.encadrant.get_full_name|default:"Aucun encadrant" }}',
            etablissement: '{{ data.stagiaire.etablissement|default:"Non renseigné" }}',
            niveau: '{{ data.stagiaire.niveau_etude|default:"Non renseigné" }}',
            specialite: '{{ data.stagiaire.specialite|default:"Non renseigné" }}'
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];

    // Initialiser le calendrier
    var calendar = $('#calendar').fullCalendar({
        header: {
            left: 'title',
            center: '',
            right: 'prev,next today month'
        },
        editable: false,
        firstDay: 1, // Lundi
        selectable: false,
        defaultView: 'month',
        height: 700,
        
        columnFormat: {
            month: 'ddd',    // Lun
            week: 'ddd d',   // Lun 7
            day: 'dddd M/d'  // Lundi 9/7
        },
        titleFormat: {
            month: 'MMMM yyyy', // Janvier 2025
            week: "MMMM yyyy",  // Janvier 2025
            day: 'MMMM yyyy'    // Janvier 2025
        },
        
        monthNames: ['Janvier','Février','Mars','Avril','Mai','Juin',
                    'Juillet','Août','Septembre','Octobre','Novembre','Décembre'],
        monthNamesShort: ['Jan','Fév','Mar','Avr','Mai','Jun',
                         'Jul','Aoû','Sep','Oct','Nov','Déc'],
        dayNames: ['Dimanche','Lundi','Mardi','Mercredi','Jeudi','Vendredi','Samedi'],
        dayNamesShort: ['Dim','Lun','Mar','Mer','Jeu','Ven','Sam'],
        
        buttonText: {
            today: 'Aujourd\'hui',
            month: 'Mois',
            week: 'Semaine',
            day: 'Jour'
        },
        
        events: stagiaireEvents,
        
        eventRender: function(event, element) {
            // Ajouter tooltip avec détails complets
            var tooltipText = event.title +
                            '\n🏢 Service: ' + event.serviceName +
                            '\n👨‍💼 Encadrant: ' + event.encadrant +
                            '\n🎓 Établissement: ' + event.etablissement +
                            '\n📚 Niveau: ' + event.niveau +
                            '\n🔬 Spécialité: ' + event.specialite +
                            '\n📅 Période: ' + event.start.toLocaleDateString('fr-FR') +
                            ' au ' + event.end.toLocaleDateString('fr-FR');

            element.attr('title', tooltipText);

            // Animation d'apparition
            element.hide().fadeIn(300);
        },
        
        eventClick: function(event) {
            // Modal avec détails du stagiaire
            showStagiaireModal(event);
        }
    });

    // Fonction pour afficher le modal avec toutes les informations
    function showStagiaireModal(event) {
        var stagiaireNom = event.title.split(' (')[0]; // Enlever le service du titre
        var dureeJours = Math.ceil((event.end - event.start) / (1000 * 60 * 60 * 24));

        var modalHtml = `
            <div class="modal fade" id="stagiaireModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header" style="background: ${event.backgroundColor}; color: white; border-radius: 6px 6px 0 0;">
                            <button type="button" class="close" data-dismiss="modal" style="color: white; opacity: 1;">
                                <span style="font-size: 28px;">&times;</span>
                            </button>
                            <h4 class="modal-title" style="font-size: 20px; font-weight: 600;">
                                📋 Profil Complet du Stagiaire
                            </h4>
                        </div>
                        <div class="modal-body" style="padding: 25px;">
                            <!-- En-tête avec avatar -->
                            <div style="text-align: center; margin-bottom: 25px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                                <div style="width: 80px; height: 80px; background: ${event.backgroundColor};
                                           border-radius: 50%; margin: 0 auto 15px; display: flex;
                                           align-items: center; justify-content: center; color: white;
                                           font-size: 28px; font-weight: bold; box-shadow: 0 4px 12px rgba(0,0,0,0.2);">
                                    ${stagiaireNom.split(' ').map(n => n[0]).join('')}
                                </div>
                                <h3 style="margin: 0; color: #333; font-size: 24px; font-weight: 600;">${stagiaireNom}</h3>
                                <p style="margin: 5px 0 0 0; color: #666; font-size: 16px;">${event.serviceName}</p>
                            </div>

                            <!-- Informations détaillées -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid ${event.backgroundColor}; margin-bottom: 15px;">
                                        <h5 style="color: #333; margin-bottom: 15px; font-weight: 600;">📊 Informations Générales</h5>
                                        <p style="margin: 8px 0; line-height: 1.6;"><strong>🏢 Service :</strong> ${event.serviceName}</p>
                                        <p style="margin: 8px 0; line-height: 1.6;"><strong>👨‍💼 Encadrant :</strong> ${event.encadrant}</p>
                                        <p style="margin: 8px 0; line-height: 1.6;"><strong>📅 Période :</strong> ${event.start.toLocaleDateString('fr-FR')} → ${event.end.toLocaleDateString('fr-FR')}</p>
                                        <p style="margin: 8px 0; line-height: 1.6;"><strong>⏱️ Durée :</strong> ${dureeJours} jours</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid ${event.backgroundColor}; margin-bottom: 15px;">
                                        <h5 style="color: #333; margin-bottom: 15px; font-weight: 600;">🎓 Formation</h5>
                                        <p style="margin: 8px 0; line-height: 1.6;"><strong>🏫 Établissement :</strong> ${event.etablissement}</p>
                                        <p style="margin: 8px 0; line-height: 1.6;"><strong>📚 Niveau :</strong> ${event.niveau}</p>
                                        <p style="margin: 8px 0; line-height: 1.6;"><strong>🔬 Spécialité :</strong> ${event.specialite}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Barre de progression du stage -->
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 15px;">
                                <h5 style="color: #333; margin-bottom: 15px; font-weight: 600;">📈 Progression du Stage</h5>
                                <div style="background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden;">
                                    <div style="background: ${event.backgroundColor}; height: 100%; width: ${getProgressPercentage(event)}%;
                                               transition: width 0.3s ease; border-radius: 10px;"></div>
                                </div>
                                <p style="text-align: center; margin: 10px 0 0 0; font-size: 14px; color: #666;">
                                    ${getProgressPercentage(event)}% terminé
                                </p>
                            </div>
                        </div>
                        <div class="modal-footer" style="padding: 20px; background: #f8f9fa;">
                            <button type="button" class="btn btn-default" data-dismiss="modal" style="padding: 10px 20px;">
                                Fermer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Supprimer le modal existant et ajouter le nouveau
        $('#stagiaireModal').remove();
        $('body').append(modalHtml);
        $('#stagiaireModal').modal('show');
    }

    // Fonction pour calculer le pourcentage de progression
    function getProgressPercentage(event) {
        var now = new Date();
        var start = event.start;
        var end = event.end;

        if (now < start) return 0;
        if (now > end) return 100;

        var total = end - start;
        var elapsed = now - start;
        return Math.round((elapsed / total) * 100);
    }
});
</script>
{% endblock %}

{% block content %}
<div id='wrap'>
    <div id='calendar'></div>
    
    <!-- Légende des stagiaires organisée par service -->
    <div class="stagiaires-legend">
        <h3 class="legend-title">📊 Tous les Stagiaires par Service ({{ stagiaires_data|length }} stagiaires actifs)</h3>

        <!-- Statistiques par service -->
        <div style="text-align: center; margin-bottom: 25px;">
            {% regroup stagiaires_data by stagiaire.service.nom as services_grouped %}
            {% for service in services_grouped %}
                <span style="display: inline-block; margin: 5px 10px; padding: 8px 15px; background: #f8f9fa; border-radius: 20px; font-size: 14px; font-weight: 600; color: #333;">
                    🏢 {{ service.grouper|default:"Sans service" }} ({{ service.list|length }})
                </span>
            {% endfor %}
        </div>

        <!-- Légende organisée par service -->
        {% regroup stagiaires_data by stagiaire.service.nom as services_grouped %}
        {% for service in services_grouped %}
        <div style="margin-bottom: 30px;">
            <h4 style="color: #667eea; font-size: 18px; font-weight: 600; margin-bottom: 15px; padding-bottom: 8px; border-bottom: 2px solid #667eea;">
                🏢 {{ service.grouper|default:"Sans service assigné" }} ({{ service.list|length }} stagiaire{{ service.list|length|pluralize }})
            </h4>
            <div class="legend-grid">
                {% for data in service.list %}
                <div class="legend-item" style="border-left-color: {{ data.color }};">
                    <div class="legend-color" style="background-color: {{ data.color }};"></div>
                    <div>
                        <div class="legend-text">{{ data.stagiaire.prenom }} {{ data.stagiaire.nom }}</div>
                        <div class="legend-details">
                            📅 {{ data.stagiaire.date_debut|date:"d/m/Y" }} - {{ data.stagiaire.date_fin|date:"d/m/Y" }} •
                            🎓 {{ data.stagiaire.etablissement|default:"Établissement non renseigné" }}
                            {% if data.stagiaire.encadrant %}
                                <br>👨‍💼 Encadrant: {{ data.stagiaire.encadrant.get_full_name }}
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}
