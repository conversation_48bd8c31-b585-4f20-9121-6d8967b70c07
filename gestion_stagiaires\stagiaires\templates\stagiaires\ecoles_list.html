{% extends 'stagiaires/base.html' %}

{% block title %}{{ title }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-university me-2"></i>
                        Établissements
                    </h3>
                    {% if user.role == 'ADMIN' %}
                    <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#addEcoleModal">
                        <i class="fas fa-plus me-1"></i>Ajouter un établissement
                    </button>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if ecoles %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Établissement</th>
                                    <th>Nombre de stagiaires</th>
                                    {% if user.role == 'ADMIN' %}
                                    <th>Actions</th>
                                    {% endif %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for ecole in ecoles %}
                                <tr>
                                    <td>{{ ecole.etablissement }}</td>
                                    <td>{{ ecole.stagiaires_count }}</td>
                                    {% if user.role == 'ADMIN' %}
                                    <td>
                                        <button class="btn btn-sm btn-danger" onclick="confirmDeleteEcole('{{ ecole.etablissement }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                    {% endif %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Aucun établissement n'a été enregistré pour le moment.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour ajouter un établissement -->
{% if user.role == 'ADMIN' %}
<div class="modal fade" id="addEcoleModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Ajouter un établissement</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'add_ecole' %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="etablissement" class="form-label">Nom de l'établissement</label>
                        <input type="text" class="form-control" id="etablissement" name="etablissement" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function confirmDeleteEcole(ecole) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'établissement "${ecole}" ?`)) {
        // Créer un formulaire pour envoyer une requête POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = "{% url 'delete_ecole' %}";
        
        // Ajouter le token CSRF
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = '{{ csrf_token }}';
        form.appendChild(csrfToken);
        
        // Ajouter l'établissement à supprimer
        const ecoleInput = document.createElement('input');
        ecoleInput.type = 'hidden';
        ecoleInput.name = 'etablissement';
        ecoleInput.value = ecole;
        form.appendChild(ecoleInput);
        
        // Ajouter le formulaire au document et le soumettre
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endif %}
{% endblock %}

