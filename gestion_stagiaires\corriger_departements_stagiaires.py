#!/usr/bin/env python
"""
Script pour corriger les départements des stagiaires selon le service de leur encadrant
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire

User = get_user_model()

def corriger_departements_stagiaires():
    """Corriger les départements des stagiaires selon le service de leur encadrant"""
    
    print("=== CORRECTION DES DÉPARTEMENTS DES STAGIAIRES ===")
    
    # Mapping service -> département
    service_to_departement = {
        'informatique': 'IT',
        'marketing': 'MARKETING', 
        'ressources humaines': 'RH',
        'rh': 'RH',
        'finance': 'FINANCE',
        'commercial': 'COMMERCIAL',
        'production': 'PRODUCTION'
    }
    
    print("📋 Mapping Service → Département:")
    for service, dept in service_to_departement.items():
        print(f"   {service} → {dept}")
    
    # 1. Analyser les corrections nécessaires
    print(f"\n🔍 ANALYSE DES CORRECTIONS NÉCESSAIRES:")
    
    corrections_a_faire = []
    encadrants = User.objects.filter(role='ENCADRANT', is_active=True)
    
    for encadrant in encadrants:
        if encadrant.service:
            service_nom = encadrant.service.nom.lower()
            dept_attendu = service_to_departement.get(service_nom, None)
            
            if dept_attendu:
                # Stagiaires de cet encadrant
                stagiaires_encadrant = Stagiaire.objects.filter(encadrant=encadrant)
                
                for stagiaire in stagiaires_encadrant:
                    if stagiaire.departement != dept_attendu:
                        corrections_a_faire.append({
                            'stagiaire': stagiaire,
                            'encadrant': encadrant,
                            'service': encadrant.service.nom,
                            'dept_actuel': stagiaire.departement,
                            'dept_attendu': dept_attendu
                        })
    
    print(f"   Total corrections nécessaires: {len(corrections_a_faire)}")
    
    if not corrections_a_faire:
        print("   ✅ Aucune correction nécessaire - Départements déjà cohérents")
        return
    
    # 2. Afficher le détail des corrections
    print(f"\n📋 DÉTAIL DES CORRECTIONS:")
    
    for i, correction in enumerate(corrections_a_faire, 1):
        print(f"\n   {i}. {correction['stagiaire'].nom_complet}")
        print(f"      👨‍💼 Encadrant: {correction['encadrant'].get_full_name()}")
        print(f"      🏢 Service encadrant: {correction['service']}")
        print(f"      📊 Département actuel: {correction['dept_actuel']}")
        print(f"      📊 Département attendu: {correction['dept_attendu']}")
        print(f"      🔄 Action: {correction['dept_actuel']} → {correction['dept_attendu']}")
    
    # 3. Demander confirmation
    print(f"\n" + "="*60)
    print("⚠️  ATTENTION: Cette opération va modifier les départements des stagiaires.")
    print("   Les départements seront alignés sur les services de leurs encadrants.")
    print("   Cette action est irréversible.")
    print("")
    
    confirmation = input("Voulez-vous continuer avec les corrections ? (oui/non): ").lower().strip()
    
    if confirmation not in ['oui', 'o', 'yes', 'y']:
        print("❌ Opération annulée.")
        return
    
    # 4. Effectuer les corrections
    print(f"\n🔧 CORRECTION EN COURS:")
    
    corrections_reussies = 0
    corrections_echouees = 0
    
    for correction in corrections_a_faire:
        try:
            stagiaire = correction['stagiaire']
            ancien_dept = stagiaire.departement
            nouveau_dept = correction['dept_attendu']
            
            stagiaire.departement = nouveau_dept
            stagiaire.save()
            
            corrections_reussies += 1
            print(f"   ✅ {stagiaire.nom_complet}: {ancien_dept} → {nouveau_dept}")
            
        except Exception as e:
            corrections_echouees += 1
            print(f"   ❌ Erreur pour {correction['stagiaire'].nom_complet}: {str(e)}")
    
    # 5. Vérification finale
    print(f"\n🔍 VÉRIFICATION FINALE:")
    
    # Recompter les corrections nécessaires
    corrections_restantes = []
    for encadrant in encadrants:
        if encadrant.service:
            service_nom = encadrant.service.nom.lower()
            dept_attendu = service_to_departement.get(service_nom, None)
            
            if dept_attendu:
                stagiaires_encadrant = Stagiaire.objects.filter(encadrant=encadrant)
                stagiaires_mauvais_dept = stagiaires_encadrant.exclude(departement=dept_attendu)
                corrections_restantes.extend(stagiaires_mauvais_dept)
    
    print(f"   Corrections restantes: {len(corrections_restantes)}")
    
    if len(corrections_restantes) == 0:
        print(f"   ✅ Tous les départements sont maintenant cohérents !")
    else:
        print(f"   ⚠️ {len(corrections_restantes)} corrections n'ont pas pu être effectuées")
    
    # 6. Statistiques finales
    print(f"\n📊 STATISTIQUES FINALES:")
    
    for dept in ['IT', 'MARKETING', 'RH', 'FINANCE', 'COMMERCIAL', 'PRODUCTION']:
        count = Stagiaire.objects.filter(departement=dept).count()
        print(f"   🏢 Département {dept}: {count} stagiaires")
    
    # Afficher par encadrant
    print(f"\n👥 RÉPARTITION PAR ENCADRANT:")
    
    for encadrant in encadrants:
        if encadrant.service:
            service_nom = encadrant.service.nom.lower()
            dept_attendu = service_to_departement.get(service_nom, None)
            
            if dept_attendu:
                stagiaires_dept = Stagiaire.objects.filter(
                    encadrant=encadrant,
                    departement=dept_attendu
                ).count()
                
                total_stagiaires = Stagiaire.objects.filter(encadrant=encadrant).count()
                
                print(f"   👨‍💼 {encadrant.get_full_name()} (Service: {encadrant.service.nom})")
                print(f"      📊 {stagiaires_dept}/{total_stagiaires} stagiaires avec le bon département ({dept_attendu})")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DE LA CORRECTION:")
    print("")
    print(f"✅ CORRECTIONS EFFECTUÉES :")
    print(f"   • Corrections réussies: {corrections_reussies}")
    print(f"   • Corrections échouées: {corrections_echouees}")
    print(f"   • Total traité: {len(corrections_a_faire)}")
    print("")
    print("✅ RÉSULTAT :")
    if len(corrections_restantes) == 0:
        print("   • Tous les départements sont cohérents ✅")
        print("   • Le filtrage par département fonctionnera parfaitement ✅")
    else:
        print(f"   • {len(corrections_restantes)} corrections restantes ⚠️")
        print("   • Vérification manuelle requise ⚠️")
    print("")
    print("🎉 CORRECTION DES DÉPARTEMENTS TERMINÉE !")
    print(f"{'='*60}")

if __name__ == '__main__':
    corriger_departements_stagiaires()
