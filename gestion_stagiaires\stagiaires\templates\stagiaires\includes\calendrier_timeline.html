<div class="calendar-view">
    {% if stagiaires %}
    <div class="mb-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <button id="prevMonth" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-chevron-left"></i> Mois précédent
            </button>
            <h4 id="currentMonthYear" class="mb-0 text-center">{{ current_month_year }}</h4>
            <button id="nextMonth" class="btn btn-sm btn-outline-secondary">
                Mois suivant <i class="fas fa-chevron-right"></i>
            </button>
        </div>
        
        <!-- Calendrier mensuel -->
        <div class="table-responsive">
            <table class="table table-bordered calendar-table">
                <thead class="bg-light">
                    <tr>
                        <th class="text-center">Lun</th>
                        <th class="text-center">Mar</th>
                        <th class="text-center">Mer</th>
                        <th class="text-center">Jeu</th>
                        <th class="text-center">Ven</th>
                        <th class="text-center">Sam</th>
                        <th class="text-center">Dim</th>
                    </tr>
                </thead>
                <tbody id="calendarBody">
                    <!-- Le contenu du calendrier sera généré par JavaScript -->
                </tbody>
            </table>
        </div>
        
        <!-- Liste des stagiaires avec leurs périodes -->
        <div class="mt-4">
            <h5>Stagiaires pour la période sélectionnée</h5>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 25%;">Stagiaire</th>
                            <th style="width: 15%;">Service</th>
                            <th style="width: 15%;">Encadrant</th>
                            <th style="width: 45%;">Période de stage</th>
                        </tr>
                    </thead>
                    <tbody id="stagiairesListe">
                        {% for stagiaire in stagiaires %}
                        <tr class="stagiaire-row" 
                            data-debut="{{ stagiaire.date_debut|date:'Y-m-d' }}" 
                            data-fin="{{ stagiaire.date_fin|date:'Y-m-d' }}"
                            data-statut="{{ stagiaire.statut }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-primary text-white me-2">
                                        {{ stagiaire.nom.0 }}{{ stagiaire.prenom.0 }}
                                    </div>
                                    <div>
                                        <strong>{{ stagiaire.nom }} {{ stagiaire.prenom }}</strong>
                                        <br>
                                        <small class="text-muted">{{ stagiaire.specialite }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if stagiaire.service %}
                                    <span class="badge bg-secondary">{{ stagiaire.service.nom }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ stagiaire.get_departement_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if stagiaire.encadrant %}
                                    {{ stagiaire.encadrant.get_full_name }}
                                {% else %}
                                    <span class="text-muted">Non assigné</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <small class="me-2">{{ stagiaire.date_debut|date:"d/m/Y" }} - {{ stagiaire.date_fin|date:"d/m/Y" }}</small>
                                    <div class="progress flex-grow-1" style="height: 20px;">
                                        {% with today=stagiaire.get_progress_info %}
                                        <div class="progress-bar 
                                            {% if stagiaire.statut == 'EN_COURS' %}
                                                bg-success
                                            {% elif stagiaire.statut == 'TERMINE' %}
                                                bg-secondary
                                            {% elif stagiaire.statut == 'ANNULE' or stagiaire.statut == 'SUSPENDU' %}
                                                bg-danger
                                            {% elif today.days_until_start <= 30 and today.days_until_start > 0 %}
                                                bg-warning
                                            {% else %}
                                                bg-info
                                            {% endif %}"
                                            role="progressbar" 
                                            style="width: {{ today.progress_percentage }}%;" 
                                            aria-valuenow="{{ today.progress_percentage }}" 
                                            aria-valuemin="0" 
                                            aria-valuemax="100">
                                            {% if today.progress_percentage > 0 and today.progress_percentage < 100 %}
                                                {{ today.progress_percentage }}%
                                            {% endif %}
                                        </div>
                                        {% endwith %}
                                    </div>
                                </div>
                                <small class="text-muted">
                                    {% with today=stagiaire.get_progress_info %}
                                        {% if stagiaire.statut == 'EN_COURS' %}
                                            En cours ({{ today.days_elapsed }}/{{ today.total_days }} jours)
                                        {% elif stagiaire.statut == 'TERMINE' %}
                                            Terminé
                                        {% elif stagiaire.statut == 'ANNULE' %}
                                            Annulé
                                        {% elif stagiaire.statut == 'SUSPENDU' %}
                                            Suspendu
                                        {% elif today.days_until_start <= 0 %}
                                            Commence aujourd'hui
                                        {% elif today.days_until_start == 1 %}
                                            Commence demain
                                        {% elif today.days_until_start <= 30 %}
                                            Commence dans {{ today.days_until_start }} jours
                                        {% else %}
                                            Commence le {{ stagiaire.date_debut|date:"d/m/Y" }}
                                        {% endif %}
                                    {% endwith %}
                                </small>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">Aucun stagiaire trouvé pour les critères sélectionnés</h5>
    </div>
    {% endif %}
</div>

<style>
.calendar-table {
    table-layout: fixed;
}

.calendar-table th, .calendar-table td {
    text-align: center;
    height: 80px;
    vertical-align: top;
    padding: 5px;
}

.calendar-day {
    position: relative;
    min-height: 70px;
}

.day-number {
    font-weight: bold;
    position: absolute;
    top: 2px;
    left: 5px;
}

.day-events {
    margin-top: 20px;
    font-size: 0.8em;
}

.event-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 3px;
}

.today {
    background-color: #f8f9fa;
    border: 2px solid #0d6efd;
}

.other-month {
    background-color: #f5f5f5;
    color: #aaa;
}

.has-events {
    background-color: #e8f4ff;
}

.event-tooltip {
    position: absolute;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    z-index: 1000;
    display: none;
    max-width: 250px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Données des stagiaires
    const stagiaires = [];
    document.querySelectorAll('.stagiaire-row').forEach(row => {
        stagiaires.push({
            nom: row.querySelector('strong').textContent,
            debut: new Date(row.dataset.debut),
            fin: new Date(row.dataset.fin),
            statut: row.dataset.statut,
            element: row
        });
    });
    
    // Date actuelle
    let currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();
    
    // Générer le calendrier
    generateCalendar(currentMonth, currentYear);
    
    // Boutons de navigation
    document.getElementById('prevMonth').addEventListener('click', function() {
        currentMonth--;
        if (currentMonth < 0) {
            currentMonth = 11;
            currentYear--;
        }
        generateCalendar(currentMonth, currentYear);
    });
    
    document.getElementById('nextMonth').addEventListener('click', function() {
        currentMonth++;
        if (currentMonth > 11) {
            currentMonth = 0;
            currentYear++;
        }
        generateCalendar(currentMonth, currentYear);
    });
    
    // Fonction pour générer le calendrier
    function generateCalendar(month, year) {
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        
        // Mettre à jour le titre du mois
        const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
        document.getElementById('currentMonthYear').textContent = `${monthNames[month]} ${year}`;
        
        // Obtenir le jour de la semaine du premier jour (0 = Dimanche, 1 = Lundi, etc.)
        let firstDayOfWeek = firstDay.getDay();
        if (firstDayOfWeek === 0) firstDayOfWeek = 7; // Convertir Dimanche (0) en 7
        
        // Créer le corps du calendrier
        const calendarBody = document.getElementById('calendarBody');
        calendarBody.innerHTML = '';
        
        let date = 1;
        
        // Créer les semaines
        for (let i = 0; i < 6; i++) {
            // Créer une ligne
            const row = document.createElement('tr');
            
            // Créer les cellules individuelles
            for (let j = 1; j <= 7; j++) {
                const cell = document.createElement('td');
                
                if (i === 0 && j < firstDayOfWeek) {
                    // Cellules vides avant le premier jour du mois
                    const prevMonth = month === 0 ? 11 : month - 1;
                    const prevYear = month === 0 ? year - 1 : year;
                    const daysInPrevMonth = new Date(prevYear, prevMonth + 1, 0).getDate();
                    const prevDate = daysInPrevMonth - (firstDayOfWeek - j - 1);
                    
                    cell.innerHTML = `<div class="calendar-day other-month">
                        <div class="day-number">${prevDate}</div>
                        <div class="day-events"></div>
                    </div>`;
                    cell.classList.add('other-month');
                } else if (date > daysInMonth) {
                    // Cellules vides après le dernier jour du mois
                    const nextDate = date - daysInMonth;
                    cell.innerHTML = `<div class="calendar-day other-month">
                        <div class="day-number">${nextDate}</div>
                        <div class="day-events"></div>
                    </div>`;
                    cell.classList.add('other-month');
                    date++;
                } else {
                    // Jours du mois actuel
                    const currentDateObj = new Date(year, month, date);
                    const isToday = currentDateObj.toDateString() === new Date().toDateString();
                    
                    // Vérifier si des stagiaires ont un stage ce jour-là
                    const eventsToday = stagiaires.filter(stagiaire => {
                        return currentDateObj >= stagiaire.debut && currentDateObj <= stagiaire.fin;
                    });
                    
                    let cellClass = isToday ? 'today' : '';
                    if (eventsToday.length > 0) cellClass += ' has-events';
                    
                    let eventsHtml = '';
                    if (eventsToday.length > 0) {
                        eventsHtml = '<div class="day-events">';
                        const maxToShow = Math.min(3, eventsToday.length);
                        for (let k = 0; k < maxToShow; k++) {
                            let dotColor = 'bg-info';
                            if (eventsToday[k].statut === 'EN_COURS') dotColor = 'bg-success';
                            else if (eventsToday[k].statut === 'TERMINE') dotColor = 'bg-secondary';
                            else if (['ANNULE', 'SUSPENDU'].includes(eventsToday[k].statut)) dotColor = 'bg-danger';
                            
                            eventsHtml += `<div><span class="event-dot ${dotColor}"></span></div>`;
                        }
                        if (eventsToday.length > maxToShow) {
                            eventsHtml += `<div>+${eventsToday.length - maxToShow} autres</div>`;
                        }
                        eventsHtml += '</div>';
                    }
                    
                    cell.innerHTML = `<div class="calendar-day ${cellClass}">
                        <div class="day-number">${date}</div>
                        ${eventsHtml}
                    </div>`;
                    
                    // Ajouter des données pour le tooltip
                    if (eventsToday.length > 0) {
                        cell.dataset.hasEvents = 'true';
                        cell.dataset.date = `${year}-${month+1}-${date}`;
                        
                        // Ajouter un événement pour afficher les stagiaires de ce jour
                        cell.addEventListener('click', function() {
                            highlightStagiaires(currentDateObj);
                        });
                    }
                    
                    date++;
                }
                
                row.appendChild(cell);
            }
            
            calendarBody.appendChild(row);
            
            // Arrêter si nous avons dépassé le nombre de jours du mois
            if (date > daysInMonth) {
                break;
            }
        }
        
        // Mettre à jour la liste des stagiaires pour le mois actuel
        updateStagiairesList(month, year);
    }
    
    // Fonction pour mettre à jour la liste des stagiaires pour le mois sélectionné
    function updateStagiairesList(month, year) {
        const startOfMonth = new Date(year, month, 1);
        const endOfMonth = new Date(year, month + 1, 0);
        
        stagiaires.forEach(stagiaire => {
            // Vérifier si le stage chevauche le mois actuel
            const isVisible = !(stagiaire.fin < startOfMonth || stagiaire.debut > endOfMonth);
            stagiaire.element.style.display = isVisible ? '' : 'none';
        });
    }
    
    // Fonction pour mettre en évidence les stagiaires d'une date spécifique
    function highlightStagiaires(date) {
        // Réinitialiser toutes les lignes
        stagiaires.forEach(stagiaire => {
            stagiaire.element.classList.remove('table-primary');
        });
        
        // Mettre en évidence les stagiaires de cette date
        const stagiairesDuJour = stagiaires.filter(stagiaire => {
            return date >= stagiaire.debut && date <= stagiaire.fin;
        });
        
        stagiairesDuJour.forEach(stagiaire => {
            stagiaire.element.classList.add('table-primary');
        });
    }
});
</script>
