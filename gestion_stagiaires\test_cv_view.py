#!/usr/bin/env python
"""
Script pour tester la vue de consultation CV
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire

User = get_user_model()

def test_cv_view():
    """Tester la vue de consultation CV"""
    
    print("🧪 TEST DE LA VUE CONSULTATION CV")
    print("=" * 40)
    
    # Créer un client de test
    client = Client()
    
    # 1. Test sans connexion
    print("1. Test sans connexion...")
    response = client.get('/stagiaires/2/cv/')
    print(f"   Status: {response.status_code}")
    if response.status_code == 302:
        print("   ✅ Redirection vers login (normal)")
    else:
        print("   ❌ Problème de redirection")
    
    # 2. Test avec connexion encadrant
    try:
        encadrant = User.objects.get(username='encadrant_test')
        print(f"\n2. Test avec encadrant connecté ({encadrant.username})...")
        
        # Se connecter
        login_success = client.login(username='encadrant_test', password='test123')
        print(f"   Connexion: {'✅ Réussie' if login_success else '❌ Échouée'}")
        
        if login_success:
            # Tester l'accès au CV
            response = client.get('/stagiaires/2/cv/')
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ Accès autorisé")
                # Vérifier le contenu
                content = response.content.decode('utf-8')
                if 'CV de' in content:
                    print("   ✅ Contenu CV présent")
                else:
                    print("   ❌ Contenu CV manquant")
            elif response.status_code == 302:
                print("   ⚠️ Redirection (permissions?)")
            else:
                print(f"   ❌ Erreur: {response.status_code}")
                
    except User.DoesNotExist:
        print("   ❌ Encadrant de test non trouvé")
    
    # 3. Vérifier le stagiaire
    try:
        stagiaire = Stagiaire.objects.get(id=2)
        print(f"\n3. Vérification stagiaire ID 2...")
        print(f"   Nom: {stagiaire.nom_complet}")
        print(f"   CV: {'✅ Disponible' if stagiaire.cv else '❌ Manquant'}")
        print(f"   Service: {stagiaire.service.nom if stagiaire.service else 'Aucun'}")
        
    except Stagiaire.DoesNotExist:
        print("   ❌ Stagiaire ID 2 non trouvé")
    
    # 4. Test avec admin
    try:
        admin_users = User.objects.filter(role='ADMIN', is_active=True)
        if admin_users.exists():
            admin = admin_users.first()
            print(f"\n4. Test avec admin ({admin.username})...")
            
            # Se connecter en tant qu'admin
            client.logout()
            login_success = client.login(username=admin.username, password='admin123')
            
            if not login_success:
                # Essayer avec un mot de passe par défaut
                admin.set_password('admin123')
                admin.save()
                login_success = client.login(username=admin.username, password='admin123')
            
            print(f"   Connexion: {'✅ Réussie' if login_success else '❌ Échouée'}")
            
            if login_success:
                response = client.get('/stagiaires/2/cv/')
                print(f"   Status: {response.status_code}")
                if response.status_code == 200:
                    print("   ✅ Accès admin autorisé")
                else:
                    print(f"   ❌ Problème accès admin: {response.status_code}")
        else:
            print("\n4. Aucun admin trouvé pour le test")
            
    except Exception as e:
        print(f"\n4. Erreur test admin: {e}")
    
    print(f"\n✅ Tests terminés")

if __name__ == '__main__':
    test_cv_view()
