#!/usr/bin/env python
"""
Test des permissions d'édition pour les encadrants et upload de rapport
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service

User = get_user_model()

def test_encadrant_permissions():
    """Test des permissions d'édition pour les encadrants"""
    
    print("=== TEST DES PERMISSIONS D'ÉDITION POUR LES ENCADRANTS ===")
    
    # 1. Préparation
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not encadrant:
        print("❌ Aucun encadrant trouvé")
        return
    
    print(f"✅ Encadrant: {encadrant.get_full_name()}")
    print(f"   Service: {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    # 2. Récupérer les stagiaires
    tous_stagiaires = Stagiaire.objects.all()
    print(f"   Total stagiaires: {tous_stagiaires.count()}")
    
    # Stagiaires du service de l'encadrant
    from stagiaires.views import filter_stagiaires_by_user_role
    mes_stagiaires = filter_stagiaires_by_user_role(encadrant, tous_stagiaires)
    print(f"   Mes stagiaires: {mes_stagiaires.count()}")
    
    # 3. Test d'accès à l'édition
    print(f"\n✏️ Test d'accès à l'édition:")
    
    client = Client()
    client.force_login(encadrant)
    
    # Test avec un stagiaire de son service
    if mes_stagiaires.exists():
        stagiaire_autorise = mes_stagiaires.first()
        print(f"   Test avec stagiaire autorisé: {stagiaire_autorise.nom_complet}")
        
        response = client.get(f'/stagiaires/{stagiaire_autorise.id}/edit/')
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Accès autorisé à l'édition")
            
            content = response.content.decode('utf-8')
            
            # Vérifier la présence des sections
            checks = [
                ('Rapport de Stage', 'Section rapport'),
                ('rapport_stage', 'Champ upload rapport'),
                ('commentaire_rapport', 'Champ commentaire rapport'),
                ('Suivi et Évaluation', 'Section évaluation'),
                ('evaluation_encadrant', 'Champ évaluation'),
                ('statut_taches', 'Champ statut tâches'),
            ]
            
            for check, desc in checks:
                if check in content:
                    print(f"      ✅ {desc}")
                else:
                    print(f"      ❌ {desc} manquant")
        
        elif response.status_code == 403:
            print("   ❌ Accès refusé (403)")
        elif response.status_code == 302:
            print("   ⚠️ Redirection (302) - Vérifier les permissions")
        else:
            print(f"   ⚠️ Status inattendu: {response.status_code}")
    
    # Test avec un stagiaire d'un autre service
    autres_stagiaires = tous_stagiaires.exclude(id__in=mes_stagiaires.values_list('id', flat=True))
    if autres_stagiaires.exists():
        stagiaire_non_autorise = autres_stagiaires.first()
        print(f"\n   Test avec stagiaire non autorisé: {stagiaire_non_autorise.nom_complet}")
        
        response = client.get(f'/stagiaires/{stagiaire_non_autorise.id}/edit/')
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 302:
            print("   ✅ Accès refusé correctement (redirection)")
        elif response.status_code == 403:
            print("   ✅ Accès refusé correctement (403)")
        elif response.status_code == 200:
            print("   ❌ Accès autorisé alors qu'il ne devrait pas l'être")
        else:
            print(f"   ⚠️ Status inattendu: {response.status_code}")
    
    # 4. Test de l'upload de rapport
    print(f"\n📄 Test de l'upload de rapport:")
    
    if mes_stagiaires.exists():
        stagiaire_test = mes_stagiaires.first()
        
        # Données de test pour l'upload
        test_data = {
            'nom': stagiaire_test.nom,
            'prenom': stagiaire_test.prenom,
            'email': stagiaire_test.email,
            'date_naissance': stagiaire_test.date_naissance,
            'departement': stagiaire_test.departement,
            'date_debut': stagiaire_test.date_debut,
            'date_fin': stagiaire_test.date_fin,
            'etablissement': stagiaire_test.etablissement,
            'niveau_etude': stagiaire_test.niveau_etude,
            'specialite': stagiaire_test.specialite,
            'statut': stagiaire_test.statut,
            'commentaire_rapport': 'Test de commentaire sur le rapport',
            'evaluation_encadrant': 'Évaluation de test par l\'encadrant',
            'statut_taches': 'EN_COURS',
        }
        
        # Simuler un fichier de rapport (contenu texte simple)
        from django.core.files.uploadedfile import SimpleUploadedFile
        rapport_file = SimpleUploadedFile(
            "rapport_test.txt",
            b"Contenu du rapport de test",
            content_type="text/plain"
        )
        
        try:
            response = client.post(
                f'/stagiaires/{stagiaire_test.id}/edit/',
                data=test_data,
                files={'rapport_stage': rapport_file}
            )
            
            print(f"   Status POST: {response.status_code}")
            
            if response.status_code == 302:
                print("   ✅ Upload réussi (redirection)")
                
                # Vérifier que le rapport a été sauvegardé
                stagiaire_test.refresh_from_db()
                if stagiaire_test.rapport_stage:
                    print("   ✅ Rapport sauvegardé en base")
                    print(f"      Fichier: {stagiaire_test.rapport_stage.name}")
                    print(f"      Date upload: {stagiaire_test.date_upload_rapport}")
                    print(f"      Uploadé par: {stagiaire_test.rapport_uploade_par}")
                else:
                    print("   ❌ Rapport non sauvegardé")
            else:
                print(f"   ⚠️ Upload échoué - Status: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Erreur lors de l'upload: {e}")
    
    # 5. Test avec différents rôles
    print(f"\n👥 Test avec différents rôles:")
    
    # Test avec Admin
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    if admin and tous_stagiaires.exists():
        client.force_login(admin)
        stagiaire_test = tous_stagiaires.first()
        response = client.get(f'/stagiaires/{stagiaire_test.id}/edit/')
        print(f"   Admin - Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Admin peut modifier tous les stagiaires")
        else:
            print("   ⚠️ Admin ne peut pas modifier")
    
    # Test avec RH
    user_rh = User.objects.filter(role='RH', is_active=True).first()
    if user_rh and tous_stagiaires.exists():
        client.force_login(user_rh)
        stagiaire_test = tous_stagiaires.first()
        response = client.get(f'/stagiaires/{stagiaire_test.id}/edit/')
        print(f"   RH - Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ RH peut modifier tous les stagiaires")
        else:
            print("   ⚠️ RH ne peut pas modifier")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DES FONCTIONNALITÉS IMPLÉMENTÉES:")
    print("")
    print("✅ PERMISSIONS D'ÉDITION :")
    print("   • Encadrants peuvent modifier les stagiaires de leur service")
    print("   • Admin/RH peuvent modifier tous les stagiaires")
    print("   • Accès refusé pour les stagiaires d'autres services")
    print("")
    print("✅ UPLOAD DE RAPPORT :")
    print("   • Section dédiée dans le formulaire d'édition")
    print("   • Champ de commentaire sur le rapport")
    print("   • Métadonnées d'upload (date, utilisateur)")
    print("   • Affichage dans les détails du stagiaire")
    print("")
    print("✅ SUIVI ET ÉVALUATION :")
    print("   • Champ d'évaluation de l'encadrant")
    print("   • Gestion du statut des tâches")
    print("   • Description des tâches")
    print("")
    print("🎯 UTILISATION :")
    print("   1. Connectez-vous en tant qu'encadrant")
    print("   2. Allez sur la liste des stagiaires")
    print("   3. Cliquez sur 'Modifier' pour un stagiaire de votre service")
    print("   4. Utilisez la section 'Rapport de Stage' pour uploader")
    print("   5. Remplissez l'évaluation dans 'Suivi et Évaluation'")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_encadrant_permissions()
