#!/usr/bin/env python
"""
Script de test pour les nouvelles fonctionnalités de missions et rapports
"""

import os
import sys
import django
from datetime import date, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser, Stagiaire, Mission, RapportStage

def test_mission_creation():
    """Test de création d'une mission"""
    print("=== Test de création d'une mission ===")
    
    # Créer un encadrant de test
    encadrant, created = CustomUser.objects.get_or_create(
        username='encadrant_test',
        defaults={
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON><PERSON>',
            'role': 'ENCADRANT'
        }
    )
    if created:
        encadrant.set_password('password123')
        encadrant.save()
        print(f"✓ Encadrant créé : {encadrant.get_full_name()}")
    else:
        print(f"✓ Encadrant existant : {encadrant.get_full_name()}")
    
    # Créer un stagiaire de test
    stagiaire, created = Stagiaire.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'nom': 'Martin',
            'prenom': 'Alice',
            'date_naissance': date(2000, 5, 15),  # Ajout de la date de naissance
            'telephone': '0123456789',
            'departement': 'Informatique',
            'encadrant': encadrant,
            'date_debut': date.today(),
            'date_fin': date.today() + timedelta(days=90),
            'etablissement': 'Université Test',
            'niveau_etude': 'Master 2',
            'specialite': 'Développement Web',
            'cree_par': encadrant
        }
    )
    if created:
        print(f"✓ Stagiaire créé : {stagiaire.nom_complet}")
    else:
        print(f"✓ Stagiaire existant : {stagiaire.nom_complet}")
    
    # Créer une mission de test
    mission, created = Mission.objects.get_or_create(
        titre='Développement d\'une application web',
        stagiaire=stagiaire,
        defaults={
            'description': 'Développer une application web complète avec Django',
            'objectifs': 'Apprendre Django, HTML/CSS, JavaScript',
            'livrables_attendus': 'Application fonctionnelle, documentation technique',
            'date_debut_prevue': date.today() + timedelta(days=7),
            'date_fin_prevue': date.today() + timedelta(days=37),
            'priorite': 2,
            'statut': 'PLANIFIEE',
            'creee_par': encadrant
        }
    )
    if created:
        print(f"✓ Mission créée : {mission.titre}")
    else:
        print(f"✓ Mission existante : {mission.titre}")
    
    # Vérifier les propriétés calculées
    print(f"  - Durée prévue : {mission.duree_prevue} jours")
    print(f"  - En retard : {mission.en_retard}")
    print(f"  - Avancement : {mission.pourcentage_avancement}%")
    
    return mission

def test_rapport_creation(mission):
    """Test de création d'un rapport"""
    print("\n=== Test de création d'un rapport ===")
    
    # Créer un rapport de test
    rapport, created = RapportStage.objects.get_or_create(
        titre='Rapport de mission - Développement web',
        stagiaire=mission.stagiaire,
        defaults={
            'mission': mission,
            'description': 'Rapport détaillé sur le développement de l\'application web',
            'statut': 'SOUMIS',
            'date_soumission': date.today()
        }
    )
    if created:
        print(f"✓ Rapport créé : {rapport.titre}")
    else:
        print(f"✓ Rapport existant : {rapport.titre}")
    
    print(f"  - Stagiaire : {rapport.stagiaire.nom_complet}")
    print(f"  - Mission liée : {rapport.mission.titre if rapport.mission else 'Aucune'}")
    print(f"  - Statut : {rapport.get_statut_display()}")
    
    return rapport

def test_mission_workflow(mission):
    """Test du workflow de mission"""
    print("\n=== Test du workflow de mission ===")
    
    # Démarrer la mission
    mission.statut = 'EN_COURS'
    mission.pourcentage_avancement = 25
    mission.commentaire_avancement = 'Mission démarrée, premiers développements en cours'
    mission.save()
    print(f"✓ Mission démarrée : {mission.pourcentage_avancement}% d'avancement")
    
    # Progression
    mission.pourcentage_avancement = 75
    mission.commentaire_avancement = 'Développement avancé, tests en cours'
    mission.save()
    print(f"✓ Mission en progression : {mission.pourcentage_avancement}% d'avancement")
    
    # Terminer la mission
    mission.statut = 'TERMINEE'
    mission.pourcentage_avancement = 100
    mission.commentaire_avancement = 'Mission terminée avec succès'
    mission.save()
    print(f"✓ Mission terminée : {mission.pourcentage_avancement}% d'avancement")

def test_rapport_validation(rapport):
    """Test de validation d'un rapport"""
    print("\n=== Test de validation d'un rapport ===")
    
    # Valider le rapport
    rapport.statut = 'VALIDE'
    rapport.note_rapport = 16.5
    rapport.commentaires_validation = 'Excellent travail, rapport très complet et bien structuré'
    rapport.valide_par = rapport.stagiaire.encadrant
    rapport.date_validation = date.today()
    rapport.save()
    
    print(f"✓ Rapport validé avec la note : {rapport.note_rapport}/20")
    print(f"  - Validé par : {rapport.valide_par.get_full_name()}")
    print(f"  - Commentaires : {rapport.commentaires_validation}")

def test_data_integrity():
    """Test de l'intégrité des données"""
    print("\n=== Test de l'intégrité des données ===")
    
    # Compter les objets créés
    missions_count = Mission.objects.count()
    rapports_count = RapportStage.objects.count()
    stagiaires_count = Stagiaire.objects.count()
    
    print(f"✓ Missions dans la base : {missions_count}")
    print(f"✓ Rapports dans la base : {rapports_count}")
    print(f"✓ Stagiaires dans la base : {stagiaires_count}")
    
    # Vérifier les relations
    for mission in Mission.objects.all():
        print(f"  - Mission '{mission.titre}' assignée à {mission.stagiaire.nom_complet}")
        rapports_lies = mission.rapports.count()
        print(f"    → {rapports_lies} rapport(s) lié(s)")

def main():
    """Fonction principale de test"""
    print("🚀 Démarrage des tests pour les missions et rapports")
    print("=" * 60)
    
    try:
        # Tests séquentiels
        mission = test_mission_creation()
        rapport = test_rapport_creation(mission)
        test_mission_workflow(mission)
        test_rapport_validation(rapport)
        test_data_integrity()
        
        print("\n" + "=" * 60)
        print("✅ Tous les tests ont été exécutés avec succès !")
        print("🎉 Les fonctionnalités de missions et rapports sont opérationnelles.")
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests : {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
