#!/usr/bin/env python
"""
Script pour corriger le service de l'encadrant salma rahmani
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Service

User = get_user_model()

def corriger_service_encadrant():
    """Corriger le service de l'encadrant salma rahmani"""
    
    print("=== CORRECTION SERVICE ENCADRANT ===")
    
    # Récupérer l'encadrant
    encadrant = User.objects.filter(username='salma rahmani').first()
    if not encadrant:
        encadrant = User.objects.filter(first_name='salma', last_name='rahmani').first()
    if not encadrant:
        encadrant = User.objects.filter(email__icontains='salma').first()
    
    if not encadrant:
        print("❌ Encadrant salma rahmani non trouvé")
        return
    
    print(f"✅ Encadrant trouvé: {encadrant.get_full_name()}")
    print(f"   Email: {encadrant.email}")
    print(f"   Service actuel: {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    # Récupérer le service Marketing
    service_marketing = Service.objects.filter(nom__icontains='marketing').first()
    if not service_marketing:
        service_marketing = Service.objects.filter(nom__icontains='Marketing').first()
    
    if not service_marketing:
        print("❌ Service Marketing non trouvé")
        return
    
    print(f"✅ Service Marketing trouvé: {service_marketing.nom}")
    
    # Assigner le service
    encadrant.service = service_marketing
    encadrant.save()
    
    print(f"✅ Service assigné: {encadrant.get_full_name()} → {service_marketing.nom}")
    
    # Vérification
    encadrant.refresh_from_db()
    print(f"🔍 Vérification: Service actuel = {encadrant.service.nom if encadrant.service else 'Aucun'}")

if __name__ == '__main__':
    corriger_service_encadrant()
