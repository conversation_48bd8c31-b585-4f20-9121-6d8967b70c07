#!/usr/bin/env python
"""
Script de test pour l'interface de suppression d'utilisateurs
"""

import os
import sys
import django
from datetime import date

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser, Stagiaire
from django.test import Client
from django.urls import reverse
import json

def create_test_users():
    """Créer des utilisateurs de test"""
    print("🧪 Création d'utilisateurs de test...")
    
    # Créer un admin de test
    admin_user, created = CustomUser.objects.get_or_create(
        username='test_admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Admin',
            'last_name': 'Test',
            'role': 'ADMIN',
            'is_active': True,
            'is_staff': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print(f"   ✓ Admin créé : {admin_user.get_full_name()}")
    else:
        print(f"   ✓ Admin existant : {admin_user.get_full_name()}")
    
    # Créer un utilisateur sans dépendances (peut être supprimé)
    user_deletable, created = CustomUser.objects.get_or_create(
        username='user_deletable',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'User',
            'last_name': 'Deletable',
            'role': 'ENCADRANT',
            'is_active': True
        }
    )
    if created:
        user_deletable.set_password('test123')
        user_deletable.save()
        print(f"   ✓ Utilisateur supprimable créé : {user_deletable.get_full_name()}")
    else:
        print(f"   ✓ Utilisateur supprimable existant : {user_deletable.get_full_name()}")
    
    # Créer un utilisateur avec dépendances (ne peut pas être supprimé)
    user_with_deps, created = CustomUser.objects.get_or_create(
        username='user_with_deps',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'User',
            'last_name': 'WithDeps',
            'role': 'ENCADRANT',
            'is_active': True
        }
    )
    if created:
        user_with_deps.set_password('test123')
        user_with_deps.save()
        print(f"   ✓ Utilisateur avec dépendances créé : {user_with_deps.get_full_name()}")
        
        # Créer un stagiaire pour créer une dépendance
        stagiaire = Stagiaire.objects.create(
            nom='TestStagiaire',
            prenom='Dependance',
            email='<EMAIL>',
            date_naissance=date(2000, 1, 1),
            telephone='0123456789',
            departement='Test',
            encadrant=user_with_deps,
            date_debut=date.today(),
            date_fin=date.today(),
            etablissement='Test University',
            niveau_etude='Master',
            specialite='Test',
            cree_par=user_with_deps
        )
        print(f"   ✓ Stagiaire de dépendance créé : {stagiaire.nom_complet}")
    else:
        print(f"   ✓ Utilisateur avec dépendances existant : {user_with_deps.get_full_name()}")
    
    return admin_user, user_deletable, user_with_deps

def test_user_deletion_api():
    """Tester l'API de suppression d'utilisateurs"""
    print("\n🔧 Test de l'API de suppression...")
    
    admin_user, user_deletable, user_with_deps = create_test_users()
    
    # Créer un client de test
    client = Client()
    
    # Se connecter en tant qu'admin
    login_success = client.login(username='test_admin', password='admin123')
    if not login_success:
        print("   ❌ Échec de connexion admin")
        return False
    
    print("   ✓ Connexion admin réussie")
    
    # Test 1: Tenter de supprimer un utilisateur avec dépendances
    print("\n   Test 1: Suppression avec dépendances...")
    response = client.post(f'/users/{user_with_deps.id}/delete/')
    
    if response.status_code == 200:
        data = json.loads(response.content)
        if not data['success']:
            print(f"   ✅ Suppression bloquée correctement : {data['error']}")
            if 'dependencies' in data:
                print(f"   ✓ Dépendances détectées : {data['dependencies']}")
        else:
            print("   ❌ Erreur: Suppression autorisée malgré les dépendances")
            return False
    else:
        print(f"   ❌ Erreur HTTP: {response.status_code}")
        return False
    
    # Test 2: Supprimer un utilisateur sans dépendances
    print("\n   Test 2: Suppression sans dépendances...")
    user_id_to_delete = user_deletable.id
    response = client.post(f'/users/{user_id_to_delete}/delete/')
    
    if response.status_code == 200:
        data = json.loads(response.content)
        if data['success']:
            print(f"   ✅ Suppression réussie : {data['message']}")
            
            # Vérifier que l'utilisateur n'existe plus
            try:
                CustomUser.objects.get(id=user_id_to_delete)
                print("   ❌ Erreur: L'utilisateur existe encore")
                return False
            except CustomUser.DoesNotExist:
                print("   ✓ Confirmation: L'utilisateur a bien été supprimé")
        else:
            print(f"   ❌ Suppression échouée : {data['error']}")
            return False
    else:
        print(f"   ❌ Erreur HTTP: {response.status_code}")
        return False
    
    # Test 3: Tester l'activation/désactivation
    print("\n   Test 3: Activation/désactivation...")
    
    # Désactiver l'utilisateur avec dépendances
    response = client.post(
        f'/users/{user_with_deps.id}/toggle-status/',
        data=json.dumps({'activate': False}),
        content_type='application/json'
    )
    
    if response.status_code == 200:
        data = json.loads(response.content)
        if data['success']:
            print(f"   ✅ Désactivation réussie : {data['message']}")
            
            # Vérifier le statut
            user_with_deps.refresh_from_db()
            if not user_with_deps.is_active:
                print("   ✓ Statut mis à jour correctement")
            else:
                print("   ❌ Statut non mis à jour")
                return False
        else:
            print(f"   ❌ Désactivation échouée : {data['error']}")
            return False
    else:
        print(f"   ❌ Erreur HTTP: {response.status_code}")
        return False
    
    print("\n   ✅ Tous les tests API réussis !")
    return True

def test_permissions():
    """Tester les permissions"""
    print("\n🔒 Test des permissions...")
    
    # Créer un utilisateur non-admin
    non_admin, created = CustomUser.objects.get_or_create(
        username='test_rh',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'RH',
            'last_name': 'Test',
            'role': 'RH',
            'is_active': True
        }
    )
    if created:
        non_admin.set_password('rh123')
        non_admin.save()
    
    client = Client()
    
    # Se connecter en tant que non-admin
    login_success = client.login(username='test_rh', password='rh123')
    if not login_success:
        print("   ❌ Échec de connexion RH")
        return False
    
    print("   ✓ Connexion RH réussie")
    
    # Tenter de supprimer un utilisateur (doit échouer)
    admin_user = CustomUser.objects.filter(role='ADMIN').first()
    if admin_user:
        response = client.post(f'/users/{admin_user.id}/delete/')
        
        if response.status_code == 200:
            data = json.loads(response.content)
            if not data['success'] and 'non autorisé' in data['error'].lower():
                print("   ✅ Accès refusé correctement pour utilisateur RH")
                return True
            else:
                print(f"   ❌ Erreur: Accès autorisé pour RH : {data}")
                return False
        else:
            print(f"   ❌ Erreur HTTP: {response.status_code}")
            return False
    else:
        print("   ❌ Aucun admin trouvé pour le test")
        return False

def cleanup_test_data():
    """Nettoyer les données de test"""
    print("\n🧹 Nettoyage des données de test...")
    
    # Supprimer les stagiaires de test
    test_stagiaires = Stagiaire.objects.filter(nom__in=['TestStagiaire'])
    count_stagiaires = test_stagiaires.count()
    test_stagiaires.delete()
    print(f"   ✓ {count_stagiaires} stagiaire(s) de test supprimé(s)")
    
    # Supprimer les utilisateurs de test
    test_users = CustomUser.objects.filter(username__in=[
        'test_admin', 'user_deletable', 'user_with_deps', 'test_rh'
    ])
    count_users = test_users.count()
    test_users.delete()
    print(f"   ✓ {count_users} utilisateur(s) de test supprimé(s)")

def main():
    """Fonction principale"""
    print("🧪 TEST DE L'INTERFACE DE SUPPRESSION D'UTILISATEURS")
    print("=" * 70)
    
    try:
        # Tests
        api_success = test_user_deletion_api()
        permissions_success = test_permissions()
        
        # Résultats
        print("\n" + "=" * 70)
        print("📊 RÉSULTATS DES TESTS")
        print("=" * 70)
        
        print(f"   API de suppression : {'✅ RÉUSSI' if api_success else '❌ ÉCHEC'}")
        print(f"   Permissions : {'✅ RÉUSSI' if permissions_success else '❌ ÉCHEC'}")
        
        overall_success = api_success and permissions_success
        
        if overall_success:
            print("\n🎉 TOUS LES TESTS RÉUSSIS !")
            print("\n📋 FONCTIONNALITÉS DISPONIBLES :")
            print("   ✅ Suppression d'utilisateurs sans dépendances")
            print("   ✅ Blocage de suppression avec dépendances")
            print("   ✅ Activation/désactivation d'utilisateurs")
            print("   ✅ Contrôle des permissions (ADMIN uniquement)")
            print("   ✅ Messages d'erreur détaillés")
            print("   ✅ Interface AJAX fonctionnelle")
            
            print("\n🚀 INSTRUCTIONS D'UTILISATION :")
            print("   1. Connectez-vous en tant qu'administrateur")
            print("   2. Allez sur : http://127.0.0.1:8000/users/")
            print("   3. Utilisez les boutons d'action pour :")
            print("      - 👁️ Voir les détails d'un utilisateur")
            print("      - ✏️ Modifier un utilisateur")
            print("      - 🔄 Activer/désactiver un utilisateur")
            print("      - 🗑️ Supprimer un utilisateur")
        else:
            print("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
            print("   Vérifiez les erreurs ci-dessus")
        
        return overall_success
        
    except Exception as e:
        print(f"\n❌ Erreur générale : {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Nettoyer les données de test
        cleanup_test_data()

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
