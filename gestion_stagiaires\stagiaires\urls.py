from django.urls import path
from . import views
from . import export_utils

urlpatterns = [
    # Page d'accueil
    path('', views.home_view, name='home'),
    
    # Authentification
    path('dashboard/', views.dashboard_view, name='dashboard'),
    path('login/', views.CustomLoginView.as_view(), name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('register/', views.RegisterView.as_view(), name='register'),
    
    # Gestion des utilisateurs
    path('users/', views.user_management_view, name='user_management'),
    path('users/add/', views.add_user_admin_view, name='add_user_admin'),
    path('users/<int:user_id>/edit/', views.edit_user_view, name='edit_user'),
    
    # Stagiaires
    path('stagiaires/', views.stagiaires_list_view, name='stagiaires_list'),
    path('stagiaires/add/', views.add_stagiaire_view, name='add_stagiaire'),
    path('stagiaires/add/encadrant/', views.add_stagiaire_encadrant_view, name='add_stagiaire_encadrant'),
    path('stagiaires/<int:stagiaire_id>/', views.stagiaire_detail_view, name='stagiaire_detail'),
    path('stagiaires/<int:stagiaire_id>/edit/', views.edit_stagiaire_view, name='edit_stagiaire'),
    path('calendrier/', views.calendrier_encadrant_view, name='calendrier_encadrant'),
    path('calendrier-simple/', views.calendrier_simple_view, name='calendrier_simple'),
    path('stagiaires/<int:stagiaire_id>/evaluation/', views.evaluation_stagiaire_view, name='evaluation_stagiaire'),
    path('stagiaires/<int:stagiaire_id>/delete/', views.delete_stagiaire_view, name='delete_stagiaire'),
    
    # Conventions et attestations
    path('conventions/', views.conventions_list_view, name='conventions_list'),
    path('convention/upload/<int:stagiaire_id>/', views.convention_upload, name='convention_upload'),
    path('convention/<int:stagiaire_id>/', views.convention_detail, name='convention_detail'),
    path('attestations/', views.attestations_list_view, name='attestations_list'),
    #contrat
    path('contrat/create/<int:stagiaire_id>/', views.contrat_create, name='contrat_create'),
    path('contrat/<int:stagiaire_id>/', views.contrat_detail, name='contrat_detail'),
    # Services
    path('services/', views.services_list_view, name='services_list'),
    path('services/add/', views.add_service_view, name='add_service'),
     path('service/edit/<int:service_id>/', views.edit_service_view, name='edit_service'),
    path('service/delete/<int:service_id>/', views.delete_service_view, name='delete_service'),
    path('service/content/', views.service_content_view, name='service_content'),
    
    
    
    # Thématiques
    path('thematiques/', views.thematiques_list_view, name='thematiques_list'),
    path('thematiques/add/', views.add_thematique_view, name='add_thematique'),
    path('thematiques/<int:thematique_id>/edit/', views.edit_thematique_view, name='edit_thematique'),
    path('thematiques/<int:thematique_id>/delete/', views.delete_thematique_view, name='delete_thematique'),
    path('thematiques/<int:thematique_id>/toggle/', views.toggle_thematique_view, name='toggle_thematique'),
    
    # Sujets
    path('sujets/', views.sujets_list_view, name='sujets_list'),
    path('sujets/add/', views.add_sujet_view, name='add_sujet'),
    path('sujets/<int:sujet_id>/edit/', views.edit_sujet_view, name='edit_sujet'),
    path('sujets/<int:sujet_id>/delete/', views.delete_sujet_view, name='delete_sujet'),
    path('sujets/<int:sujet_id>/toggle/', views.toggle_sujet_view, name='toggle_sujet'),
    path('api/thematiques/<int:thematique_id>/sujets/', views.get_sujets_by_thematique, name='get_sujets_by_thematique'),
    
    
    # Durées estimées
    path('durees-estimees/', views.durees_estimees_view, name='durees_estimees'),
    path('durees-estimees/<int:duree_id>/delete/', views.delete_duree_estimee_view, name='delete_duree_estimee'),
    
    # Rapports et statistiques
    path('reports/', views.reports_view, name='reports'),
    
    # Exports
    path('export/', views.export_view, name='export'),
    path('export/durees/csv/', export_utils.export_durees_csv, name='export_durees_csv'),
    path('export/durees/excel/', export_utils.export_durees_excel, name='export_durees_excel'),
    
    # Gestion des tâches
    path('stagiaires/<int:stagiaire_id>/taches/', views.taches_stagiaire_view, name='taches_stagiaire'),
    path('taches/<int:tache_id>/demarrer/', views.demarrer_tache_view, name='demarrer_tache'),
    path('taches/<int:tache_id>/terminer/', views.terminer_tache_view, name='terminer_tache'),
    path('taches/<int:tache_id>/annuler/', views.annuler_tache_view, name='annuler_tache'),
    path('stagiaires/<int:stagiaire_id>/taches/add/', views.add_tache_view, name='add_tache'),
    path('stagiaires/<int:stagiaire_id>/rencontre/', views.rencontre_stagiaire_view, name='rencontre_stagiaire'),
    path('calendrier/', views.calendrier_stagiaires_view, name='calendrier_stagiaires'),
    # Niveaux d'école
    path('niveaux-ecole/', views.niveaux_ecole_list_view, name='niveaux_ecole_list'),
    # Paramétrage
    path('niveaux-etudes/', views.niveaux_ecole_list_view, name='niveaux_ecole_list'),
    path('etablissements/', views.ecoles_list_view, name='ecoles_list'),
    path('niveaux/add/', views.add_niveau_view, name='add_niveau'),
    path('niveaux/delete/', views.delete_niveau_view, name='delete_niveau'),
    # Ajouter ces URLs pour la gestion des établissements
    path('etablissements/add/', views.add_ecole_view, name='add_ecole'),
    path('etablissements/delete/', views.delete_ecole_view, name='delete_ecole'),
    # URLs pour l'administration
    path('parametrage/', views.parametrage_view, name='parametrage'),
]



