{% extends 'stagiaires/base.html' %}

{% block title %}{{ title }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-tags me-2"></i>
                        Gestion des thématiques
                    </h3>
                    {% if user.role == 'ADMIN' or user.role == 'RH' or user.role == 'ENCADRANT' or thematique.cree_par == user %}
                    <a href="{% url 'add_thematique' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-plus me-1"></i>Ajouter une thématique
                    </a>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if thematiques %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Titre</th>
                                    <th>Description</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for thematique in thematiques %}
                                <tr>
                                    <td>{{ thematique.titre }}</td>
                                    <td>{{ thematique.description|truncatechars:100 }}</td>
                                    <td>
                                        <span class="badge {% if thematique.active %}bg-success{% else %}bg-danger{% endif %}">
                                            {% if thematique.active %}Active{% else %}Inactive{% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        {% if user.role == 'ADMIN' or user.role == 'RH' or user.role == 'ENCADRANT' or thematique.cree_par == user %}
                                        <div class="btn-group" role="group">
                                            <!-- Bouton Modifier -->
                                            <a href="{% url 'edit_thematique' thematique.id %}" class="btn btn-sm btn-outline-primary" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            
                                            <!-- Bouton Activer/Désactiver -->
                                            <a href="{% url 'toggle_thematique' thematique.id %}" class="btn btn-sm btn-outline-{% if thematique.active %}warning{% else %}success{% endif %}" title="{% if thematique.active %}Désactiver{% else %}Activer{% endif %}">
                                                {% if thematique.active %}
                                                    <i class="fas fa-toggle-off"></i>
                                                {% else %}
                                                    <i class="fas fa-toggle-on"></i>
                                                {% endif %}
                                            </a>
                                            
                                            <!-- Bouton Supprimer -->
                                            <a href="{% url 'delete_thematique' thematique.id %}" class="btn btn-sm btn-outline-danger" title="Supprimer" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette thématique ?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Aucune thématique n'a été créée pour le moment.
                        {% if user.role == 'ADMIN' or user.role == 'RH' %}
                        <a href="{% url 'add_thematique' %}" class="alert-link">Créer une thématique</a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}








