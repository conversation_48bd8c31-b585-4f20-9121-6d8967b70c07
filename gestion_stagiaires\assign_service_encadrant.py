#!/usr/bin/env python
"""
Script pour assigner un service à l'encadrant et créer des thématiques/sujets de test
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Service, Thematique, Sujet

User = get_user_model()

def assign_service_encadrant():
    """Assigner un service à l'encadrant et créer des données de test"""
    
    print("=== ASSIGNATION SERVICE À L'ENCADRANT ===")
    
    # 1. Récupérer l'encadrant
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not encadrant:
        print("❌ Aucun encadrant trouvé")
        return
    
    print(f"✅ Encadrant trouvé: {encadrant.get_full_name()}")
    
    # 2. <PERSON><PERSON><PERSON> ou récupérer un service informatique
    service_info, created = Service.objects.get_or_create(
        nom='Informatique',
        defaults={
            'description': 'Service informatique et développement',
            'actif': True
        }
    )
    
    if created:
        print(f"✅ Service créé: {service_info.nom}")
    else:
        print(f"✅ Service existant: {service_info.nom}")
    
    # 3. Assigner le service à l'encadrant
    encadrant.service = service_info
    encadrant.save()
    
    print(f"✅ Service assigné à l'encadrant: {service_info.nom}")
    
    # 4. Créer des thématiques pour ce service
    thematiques_data = [
        {
            'titre': 'Développement Web',
            'description': 'Développement d\'applications web modernes',
            'service': service_info
        },
        {
            'titre': 'Intelligence Artificielle',
            'description': 'Projets d\'IA et machine learning',
            'service': service_info
        },
        {
            'titre': 'Cybersécurité',
            'description': 'Sécurité informatique et protection des données',
            'service': service_info
        }
    ]
    
    print(f"\n📋 Création des thématiques:")
    for thematique_data in thematiques_data:
        thematique, created = Thematique.objects.get_or_create(
            titre=thematique_data['titre'],
            service=thematique_data['service'],
            defaults={
                'description': thematique_data['description'],
                'active': True,
                'cree_par': encadrant
            }
        )
        
        if created:
            print(f"   ✅ Thématique créée: {thematique.titre}")
        else:
            print(f"   ✅ Thématique existante: {thematique.titre}")
    
    # 5. Récupérer les thématiques créées
    thematiques = Thematique.objects.filter(service=service_info, active=True)

    # Associer les thématiques aux sujets
    sujets_data = [
        {
            'titre': 'Application de gestion des stagiaires',
            'description': 'Développement d\'une application web pour gérer les stagiaires',
            'service': service_info,
            'encadrant': encadrant,
            'thematique': thematiques.filter(titre='Développement Web').first()
        },
        {
            'titre': 'Système de recommandation',
            'description': 'Création d\'un système de recommandation basé sur l\'IA',
            'service': service_info,
            'encadrant': encadrant,
            'thematique': thematiques.filter(titre='Intelligence Artificielle').first()
        },
        {
            'titre': 'Audit de sécurité',
            'description': 'Réalisation d\'un audit de sécurité informatique',
            'service': service_info,
            'encadrant': encadrant,
            'thematique': thematiques.filter(titre='Cybersécurité').first()
        }
    ]

    print(f"\n📝 Création des sujets:")
    for sujet_data in sujets_data:
        # Vérifier que la thématique existe
        if not sujet_data['thematique']:
            print(f"   ⚠️ Thématique manquante pour le sujet: {sujet_data['titre']}")
            continue

        sujet, created = Sujet.objects.get_or_create(
            titre=sujet_data['titre'],
            service=sujet_data['service'],
            defaults={
                'description': sujet_data['description'],
                'actif': True,
                'encadrant': sujet_data['encadrant'],
                'cree_par': encadrant,
                'thematique': sujet_data['thematique']
            }
        )
        
        if created:
            print(f"   ✅ Sujet créé: {sujet.titre}")
        else:
            print(f"   ✅ Sujet existant: {sujet.titre}")
    
    # 6. Vérification finale
    print(f"\n📊 Vérification finale:")
    print(f"   Encadrant: {encadrant.get_full_name()}")
    print(f"   Service: {encadrant.service.nom}")
    print(f"   Thématiques du service: {Thematique.objects.filter(service=service_info, active=True).count()}")
    print(f"   Sujets du service: {Sujet.objects.filter(service=service_info, actif=True).count()}")
    
    print(f"\n✅ Configuration terminée ! L'encadrant peut maintenant ajouter des stagiaires de son service.")
    print(f"🎯 Testez maintenant avec: python test_encadrant_ajout_service.py")

if __name__ == '__main__':
    assign_service_encadrant()
