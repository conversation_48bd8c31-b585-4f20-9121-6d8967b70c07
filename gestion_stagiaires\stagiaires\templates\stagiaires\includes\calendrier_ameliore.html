<!-- Calendrier amélioré avec couleurs distinctes pour chaque stagiaire -->
<div class="calendrier-ameliore">
    {% if stagiaires %}
    
    <!-- Légende des stagiaires avec leurs couleurs -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h6 class="mb-0">
                <i class="fas fa-palette me-2"></i>Légende des stagiaires
                <small class="text-muted">({{ stagiaires|length }} stagiaire{{ stagiaires|length|pluralize }})</small>
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                {% for stagiaire in stagiaires %}
                <div class="col-md-6 col-lg-4 mb-2">
                    <div class="d-flex align-items-center">
                        <div class="stagiaire-color-box me-2"
                             style="background-color: {{ stagiaire.couleur }}; width: 20px; height: 20px; border-radius: 4px; border: 1px solid #ddd;"></div>
                        <div class="flex-grow-1">
                            <strong class="text-truncate d-block">{{ stagiaire.nom_complet }}</strong>
                            <small class="text-muted">
                                {% if stagiaire.service %}{{ stagiaire.service.nom }}{% else %}{{ stagiaire.get_departement_display }}{% endif %}
                                {% if stagiaire.encadrant %} - {{ stagiaire.encadrant.get_full_name }}{% endif %}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Calendrier mensuel -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <button type="button" class="btn btn-outline-light btn-sm" onclick="changerMois(-1)">
                    <i class="fas fa-chevron-left"></i> Précédent
                </button>
                <h5 class="mb-0">{{ current_month_year }}</h5>
                <button type="button" class="btn btn-outline-light btn-sm" onclick="changerMois(1)">
                    Suivant <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered mb-0 calendrier-table">
                    <thead class="bg-light">
                        <tr>
                            <th class="text-center py-2">Lundi</th>
                            <th class="text-center py-2">Mardi</th>
                            <th class="text-center py-2">Mercredi</th>
                            <th class="text-center py-2">Jeudi</th>
                            <th class="text-center py-2">Vendredi</th>
                            <th class="text-center py-2">Samedi</th>
                            <th class="text-center py-2">Dimanche</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for semaine in calendrier_jours %}
                        <tr>
                            {% for jour_data in semaine %}
                            <td class="calendrier-jour p-1 {% if jour_data.est_aujourd_hui %}aujourd-hui{% endif %}">
                                {% if jour_data %}
                                <div class="jour-container">
                                    <!-- Numéro du jour -->
                                    <div class="jour-numero {% if jour_data.est_aujourd_hui %}text-primary fw-bold{% endif %}">
                                        {{ jour_data.jour }}
                                    </div>
                                    
                                    <!-- Stagiaires du jour -->
                                    <div class="stagiaires-jour">
                                        {% for stagiaire in jour_data.stagiaires %}
                                        <div class="stagiaire-badge mb-1"
                                             style="background-color: {{ stagiaire.couleur }}; opacity: 0.8;"
                                             data-bs-toggle="tooltip"
                                             data-bs-placement="top"
                                             title="{{ stagiaire.nom_complet }} - {{ stagiaire.date_debut|date:'d/m' }} au {{ stagiaire.date_fin|date:'d/m' }}">
                                            <small class="text-white fw-bold">
                                                {{ stagiaire.nom.0 }}{{ stagiaire.prenom.0 }}
                                            </small>
                                        </div>
                                        {% endfor %}
                                        
                                        {% if jour_data.stagiaires|length > 3 %}
                                        <div class="stagiaire-badge-more">
                                            <small>+{{ jour_data.stagiaires|length|add:"-3" }}</small>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                {% else %}
                                <!-- Jour vide (autre mois) -->
                                <div class="jour-vide"></div>
                                {% endif %}
                            </td>
                            {% endfor %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Liste détaillée des stagiaires pour le mois -->
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h6 class="mb-0">
                <i class="fas fa-list me-2"></i>Détails des stages pour {{ current_month_year }}
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 5%;"></th>
                            <th style="width: 25%;">Stagiaire</th>
                            <th style="width: 15%;">Service</th>
                            <th style="width: 15%;">Encadrant</th>
                            <th style="width: 20%;">Période</th>
                            <th style="width: 20%;">Progression</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for stagiaire in stagiaires %}
                        <tr>
                            <td>
                                <div class="stagiaire-color-indicator"
                                     style="background-color: {{ stagiaire.couleur }}; width: 20px; height: 20px; border-radius: 50%; border: 2px solid #fff; box-shadow: 0 0 0 1px #ddd;"></div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <strong>{{ stagiaire.nom_complet }}</strong>
                                        <br>
                                        <small class="text-muted">{{ stagiaire.specialite }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if stagiaire.service %}
                                    <span class="badge bg-secondary">{{ stagiaire.service.nom }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ stagiaire.get_departement_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if stagiaire.encadrant %}
                                    {{ stagiaire.encadrant.get_full_name }}
                                {% else %}
                                    <span class="text-muted">Non assigné</span>
                                {% endif %}
                            </td>
                            <td>
                                <div>
                                    <strong>{{ stagiaire.date_debut|date:"d/m/Y" }}</strong> au <strong>{{ stagiaire.date_fin|date:"d/m/Y" }}</strong>
                                </div>
                                <small class="text-muted">
                                    {% with info=stagiaire.get_progress_info %}
                                    {{ info.total_days }} jours au total
                                    {% endwith %}
                                </small>
                            </td>
                            <td>
                                {% with info=stagiaire.get_progress_info %}
                                <div class="progress mb-1" style="height: 20px;">
                                    <div class="progress-bar"
                                         style="width: {{ info.progress_percentage }}%; background-color: {{ stagiaire.couleur }};"
                                         role="progressbar"
                                         aria-valuenow="{{ info.progress_percentage }}"
                                         aria-valuemin="0"
                                         aria-valuemax="100">
                                        {% if info.progress_percentage > 15 %}
                                            {{ info.progress_percentage }}%
                                        {% endif %}
                                    </div>
                                </div>
                                <small class="text-muted">
                                    {% if stagiaire.statut == 'EN_COURS' %}
                                        En cours ({{ info.days_elapsed }}/{{ info.total_days }} jours)
                                    {% elif stagiaire.statut == 'TERMINE' %}
                                        Terminé
                                    {% elif stagiaire.statut == 'ANNULE' %}
                                        Annulé
                                    {% elif stagiaire.statut == 'SUSPENDU' %}
                                        Suspendu
                                    {% elif info.days_until_start <= 0 %}
                                        Commence aujourd'hui
                                    {% elif info.days_until_start == 1 %}
                                        Commence demain
                                    {% elif info.days_until_start <= 30 %}
                                        Commence dans {{ info.days_until_start }} jours
                                    {% else %}
                                        Commence le {{ stagiaire.date_debut|date:"d/m/Y" }}
                                    {% endif %}
                                </small>
                                {% endwith %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">Aucun stagiaire trouvé pour les critères sélectionnés</h5>
    </div>
    {% endif %}
</div>

<style>
.calendrier-table {
    table-layout: fixed;
}

.calendrier-jour {
    height: 120px;
    vertical-align: top;
    position: relative;
    border: 1px solid #dee2e6;
}

.jour-container {
    height: 100%;
    position: relative;
}

.jour-numero {
    position: absolute;
    top: 5px;
    left: 8px;
    font-weight: bold;
    font-size: 14px;
}

.stagiaires-jour {
    margin-top: 25px;
    padding: 0 5px;
}

.stagiaire-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 12px;
    margin-right: 2px;
    margin-bottom: 2px;
    font-size: 10px;
    text-align: center;
    min-width: 24px;
    cursor: pointer;
    transition: all 0.2s;
}

.stagiaire-badge:hover {
    opacity: 1 !important;
    transform: scale(1.1);
}

.stagiaire-badge-more {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 12px;
    background-color: #6c757d;
    color: white;
    font-size: 10px;
    margin-top: 2px;
}

.aujourd-hui {
    background-color: #e3f2fd !important;
    border: 2px solid #2196f3 !important;
}

.jour-vide {
    height: 100%;
    background-color: #f8f9fa;
}

.stagiaire-color-box, .stagiaire-color-indicator {
    flex-shrink: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .calendrier-jour {
        height: 80px;
    }
    
    .stagiaire-badge {
        font-size: 8px;
        padding: 1px 4px;
        min-width: 20px;
    }
    
    .jour-numero {
        font-size: 12px;
    }
    
    .stagiaires-jour {
        margin-top: 20px;
    }
}
</style>

<script>
// Fonction pour changer de mois
function changerMois(direction) {
    const currentUrl = new URL(window.location);
    let mois = parseInt(currentUrl.searchParams.get('mois')) || new Date().getMonth() + 1;
    let annee = parseInt(currentUrl.searchParams.get('annee')) || new Date().getFullYear();
    
    mois += direction;
    
    if (mois > 12) {
        mois = 1;
        annee++;
    } else if (mois < 1) {
        mois = 12;
        annee--;
    }
    
    currentUrl.searchParams.set('mois', mois);
    currentUrl.searchParams.set('annee', annee);
    
    window.location.href = currentUrl.toString();
}

// Initialiser les tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
