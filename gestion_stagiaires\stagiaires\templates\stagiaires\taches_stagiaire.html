{% extends 'stagiaires/base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>
            <i class="fas fa-tasks me-2"></i>Tâches de {{ stagiaire.prenom }} {{ stagiaire.nom }}
        </h1>
        <a href="{% url 'stagiaire_detail' stagiaire.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour au profil
        </a>
    </div>
    
    {% if messages %}
    <div class="messages">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <div class="row">
        <!-- Liste des tâches -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>Liste des tâches</h5>
                </div>
                <div class="card-body">
                    {% if taches %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Tâche</th>
                                    <th>Priorité</th>
                                    <th>Statut</th>
                                    <th>Date limite</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for tache in taches %}
                                <tr>
                                    <td>
                                        <strong>{{ tache.titre }}</strong>
                                        {% if tache.description %}
                                        <button class="btn btn-sm btn-link p-0 ms-1" data-bs-toggle="modal" data-bs-target="#descriptionModal{{ tache.id }}">
                                            <i class="fas fa-info-circle"></i>
                                        </button>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if tache.priorite == 'BASSE' %}
                                        <span class="badge bg-success">Basse</span>
                                        {% elif tache.priorite == 'NORMALE' %}
                                        <span class="badge bg-info">Normale</span>
                                        {% elif tache.priorite == 'HAUTE' %}
                                        <span class="badge bg-warning">Haute</span>
                                        {% elif tache.priorite == 'URGENTE' %}
                                        <span class="badge bg-danger">Urgente</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if tache.statut == 'A_FAIRE' %}
                                        <span class="badge bg-secondary">À faire</span>
                                        {% elif tache.statut == 'EN_COURS' %}
                                        <span class="badge bg-primary">En cours</span>
                                        {% elif tache.statut == 'TERMINEE' %}
                                        <span class="badge bg-success">Terminée</span>
                                        {% elif tache.statut == 'ANNULEE' %}
                                        <span class="badge bg-danger">Annulée</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if tache.date_fin_prevue %}
                                        {{ tache.date_fin_prevue|date:"d/m/Y" }}
                                        {% else %}
                                        <span class="text-muted">Non définie</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if tache.statut == 'A_FAIRE' %}
                                        <a href="{% url 'demarrer_tache' tache.id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-play me-1"></i>Démarrer
                                        </a>
                                        {% elif tache.statut == 'EN_COURS' %}
                                        <a href="{% url 'terminer_tache' tache.id %}" class="btn btn-sm btn-success">
                                            <i class="fas fa-check me-1"></i>Terminer
                                        </a>
                                        {% endif %}
                                        
                                        {% if tache.statut != 'TERMINEE' and tache.statut != 'ANNULEE' %}
                                        <button class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#annulerModal{{ tache.id }}">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Aucune tâche n'a été assignée à ce stagiaire.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Formulaire d'ajout de tâche -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Ajouter une tâche</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.titre.id_for_label }}" class="form-label">Titre</label>
                            {{ form.titre }}
                            {% if form.titre.errors %}
                            <div class="text-danger small">
                                {% for error in form.titre.errors %}
                                <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                            <div class="text-danger small">
                                {% for error in form.description.errors %}
                                <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.priorite.id_for_label }}" class="form-label">Priorité</label>
                            {{ form.priorite }}
                            {% if form.priorite.errors %}
                            <div class="text-danger small">
                                {% for error in form.priorite.errors %}
                                <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.date_fin_prevue.id_for_label }}" class="form-label">Date limite</label>
                            {{ form.date_fin_prevue }}
                            {% if form.date_fin_prevue.errors %}
                            <div class="text-danger small">
                                {% for error in form.date_fin_prevue.errors %}
                                <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <button type="submit" class="btn btn-success w-100">
                            <i class="fas fa-plus-circle me-1"></i>Ajouter la tâche
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals pour les descriptions de tâches -->
{% for tache in taches %}
{% if tache.description %}
<div class="modal fade" id="descriptionModal{{ tache.id }}" tabindex="-1" aria-labelledby="descriptionModalLabel{{ tache.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="descriptionModalLabel{{ tache.id }}">{{ tache.titre }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{{ tache.description|linebreaks }}</p>
                <div class="mt-3">
                    <small class="text-muted">
                        Créée le {{ tache.date_creation|date:"d/m/Y" }} par {{ tache.creee_par.get_full_name|default:tache.creee_par.username }}
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}

<!-- Modals pour annuler les tâches -->
{% for tache in taches %}
{% if tache.statut != 'TERMINEE' and tache.statut != 'ANNULEE' %}
<div class="modal fade" id="annulerModal{{ tache.id }}" tabindex="-1" aria-labelledby="annulerModalLabel{{ tache.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="annulerModalLabel{{ tache.id }}">Confirmer l'annulation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir annuler la tâche <strong>{{ tache.titre }}</strong> ?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Non</button>
                <a href="{% url 'annuler_tache' tache.id %}" class="btn btn-danger">Oui, annuler</a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endfor %}
{% endblock %}

