"""
Tests pour vérifier que seuls les RH peuvent accéder aux champs de documents
dans le formulaire d'ajout de stagiaire.
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from .models import CustomUser
from .forms import StagiaireForm

class DocumentAccessTestCase(TestCase):
    """Tests pour l'accès aux documents selon le rôle utilisateur"""

    def setUp(self):
        """Configuration des tests"""
        self.client = Client()
        
        # Créer un utilisateur RH
        self.rh_user = CustomUser.objects.create_user(
            username='rh_test_docs',
            email='<EMAIL>',
            password='testpass123',
            role='RH',
            first_name='<PERSON>',
            last_name='RH'
        )
        
        # Créer un utilisateur ENCADRANT
        self.encadrant_user = CustomUser.objects.create_user(
            username='encadrant_test_docs',
            email='<EMAIL>',
            password='testpass123',
            role='ENCADRANT',
            first_name='<PERSON>',
            last_name='Encadrant'
        )
        
        # Créer un utilisateur ADMIN
        self.admin_user = CustomUser.objects.create_user(
            username='admin_test_docs',
            email='<EMAIL>',
            password='testpass123',
            role='ADMIN',
            first_name='Jean',
            last_name='Admin'
        )

    def test_rh_user_has_document_fields(self):
        """Test que les utilisateurs RH ont accès aux champs de documents"""
        form = StagiaireForm(user_role='RH')
        
        # Vérifier que les champs de documents sont présents
        self.assertIn('cv', form.fields)
        self.assertIn('assurance', form.fields)
        self.assertIn('convention_stage', form.fields)

    def test_non_rh_user_no_document_fields(self):
        """Test que les utilisateurs non-RH n'ont pas accès aux champs de documents"""
        # Test avec ENCADRANT
        form_encadrant = StagiaireForm(user_role='ENCADRANT')
        self.assertNotIn('cv', form_encadrant.fields)
        self.assertNotIn('assurance', form_encadrant.fields)
        self.assertNotIn('convention_stage', form_encadrant.fields)
        
        # Test avec ADMIN
        form_admin = StagiaireForm(user_role='ADMIN')
        self.assertNotIn('cv', form_admin.fields)
        self.assertNotIn('assurance', form_admin.fields)
        self.assertNotIn('convention_stage', form_admin.fields)

    def test_add_stagiaire_view_rh_access(self):
        """Test que la vue d'ajout de stagiaire affiche les documents pour RH"""
        self.client.login(username='rh_test_docs', password='testpass123')
        response = self.client.get(reverse('add_stagiaire'))
        
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.context['is_rh'])
        
        # Vérifier que le template contient la section documents
        self.assertContains(response, 'Documents du stagiaire')
        self.assertContains(response, 'RH uniquement')

    def test_add_stagiaire_view_non_rh_access(self):
        """Test que la vue d'ajout de stagiaire masque les documents pour non-RH"""
        self.client.login(username='encadrant_test_docs', password='testpass123')
        response = self.client.get(reverse('add_stagiaire'))
        
        self.assertEqual(response.status_code, 200)
        self.assertFalse(response.context['is_rh'])
        
        # Vérifier que le template contient le message informatif
        self.assertContains(response, 'est réservé aux gestionnaires RH')
        self.assertNotContains(response, 'RH uniquement')

    def test_form_submission_without_documents(self):
        """Test que les utilisateurs non-RH peuvent créer des stagiaires sans documents"""
        self.client.login(username='encadrant_test_docs', password='testpass123')
        
        form_data = {
            'nom': 'Durand',
            'prenom': 'Julie',
            'email': '<EMAIL>',
            'telephone': '0123456789',
            'date_naissance': '2000-05-15',
            'departement': 'IT',
            'encadrant': self.encadrant_user.id,
            'date_debut': '2025-07-01',
            'date_fin': '2025-09-30',
            'etablissement': 'Université Test',
            'niveau_etude': 'Master 2',
            'specialite': 'Informatique',
            'statut': 'EN_COURS'
        }
        
        response = self.client.post(reverse('add_stagiaire'), form_data)

        # Debug: afficher les erreurs si le formulaire échoue
        if response.status_code != 302:
            print(f"Status code: {response.status_code}")
            if 'form' in response.context:
                print(f"Form errors: {response.context['form'].errors}")

        # Vérifier que la création réussit
        self.assertEqual(response.status_code, 302)  # Redirection après succès
        
        # Vérifier que le stagiaire a été créé sans documents
        from .models import Stagiaire
        stagiaire = Stagiaire.objects.get(email='<EMAIL>')
        self.assertFalse(stagiaire.cv)
        self.assertFalse(stagiaire.assurance)
        self.assertFalse(stagiaire.convention_stage)

    def test_form_fields_consistency(self):
        """Test que les champs du formulaire sont cohérents avec le rôle"""
        # Formulaire RH doit avoir tous les champs
        rh_form = StagiaireForm(user_role='RH')
        rh_fields = set(rh_form.fields.keys())
        
        # Formulaire non-RH doit avoir tous les champs sauf les documents
        encadrant_form = StagiaireForm(user_role='ENCADRANT')
        encadrant_fields = set(encadrant_form.fields.keys())
        
        # Les champs de documents
        document_fields = {'cv', 'assurance', 'convention_stage'}
        
        # Vérifier que RH a les champs de documents
        self.assertTrue(document_fields.issubset(rh_fields))
        
        # Vérifier que non-RH n'a pas les champs de documents
        self.assertFalse(document_fields.intersection(encadrant_fields))
        
        # Vérifier que les autres champs sont identiques
        other_fields_rh = rh_fields - document_fields
        self.assertEqual(other_fields_rh, encadrant_fields)
