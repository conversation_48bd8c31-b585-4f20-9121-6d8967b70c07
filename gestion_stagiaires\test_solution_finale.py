#!/usr/bin/env python
"""
Test de la solution finale pour l'ajout de stagiaires
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service
from datetime import date, timedelta

User = get_user_model()

def test_solution_finale():
    """Test de la solution finale"""
    
    print("=== TEST DE LA SOLUTION FINALE ===")
    
    # 1. Préparation
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not admin:
        print("❌ Aucun admin trouvé")
        return
    
    print(f"✅ Admin: {admin.username}")
    print(f"✅ Encadrant: {encadrant.get_full_name() if encadrant else 'Aucun'}")
    
    # 2. État initial
    count_initial = Stagiaire.objects.count()
    print(f"📊 Stagiaires initiaux: {count_initial}")
    
    # 3. Test d'ajout avec la solution
    print("\n🚀 Test d'ajout avec la solution:")
    
    client = Client()
    client.force_login(admin)
    
    # Données de test
    test_data = {
        'nom': 'SOLUTION',
        'prenom': 'FINALE',
        'email': '<EMAIL>',
        'telephone': '0123456789',
        'date_naissance': '2000-01-01',
        'departement': 'IT',
        'date_debut': date.today().strftime('%Y-%m-%d'),
        'date_fin': (date.today() + timedelta(days=90)).strftime('%Y-%m-%d'),
        'etablissement': 'Test Solution Finale',
        'niveau_etude': 'Master',
        'specialite': 'Solution',
        'statut': 'EN_COURS',
    }
    
    if encadrant:
        test_data['encadrant'] = encadrant.id
    
    service = Service.objects.filter(actif=True).first()
    if service:
        test_data['service'] = service.id
    
    # Supprimer le stagiaire de test s'il existe
    Stagiaire.objects.filter(email='<EMAIL>').delete()
    
    print(f"   Données: {test_data}")
    
    # Test d'ajout
    response = client.post('/stagiaires/add/', test_data, follow=True)
    print(f"   Status: {response.status_code}")
    
    # Vérification immédiate
    stagiaire_cree = Stagiaire.objects.filter(email='<EMAIL>').first()
    
    if stagiaire_cree:
        print(f"   ✅ SUCCÈS: Stagiaire créé avec ID {stagiaire_cree.id}")
        print(f"      Nom: {stagiaire_cree.nom_complet}")
        print(f"      Créé par: {stagiaire_cree.cree_par}")
        print(f"      Date: {stagiaire_cree.date_creation}")
        
        # Vérifier le message de succès
        messages = list(response.context.get('messages', []))
        success_found = False
        for message in messages:
            if 'SUCCÈS' in str(message) or 'ajouté avec succès' in str(message):
                success_found = True
                print(f"   ✅ Message de succès: {message}")
                break
        
        if not success_found:
            print("   ⚠️ Message de succès non trouvé")
        
        # Vérifier la visibilité dans la liste
        response_list = client.get('/stagiaires/')
        if response_list.status_code == 200:
            content = response_list.content.decode('utf-8')
            if 'SOLUTION' in content and 'FINALE' in content:
                print("   ✅ Stagiaire visible dans la liste")
            else:
                print("   ❌ Stagiaire non visible dans la liste")
        
        # Compter les stagiaires après ajout
        count_final = Stagiaire.objects.count()
        print(f"   📊 Stagiaires après ajout: {count_final}")
        print(f"   📈 Augmentation: +{count_final - count_initial}")
        
        # Nettoyer
        stagiaire_cree.delete()
        print("   🧹 Stagiaire de test supprimé")
        
    else:
        print("   ❌ ÉCHEC: Stagiaire non créé")
        
        # Analyser la réponse pour les erreurs
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            if 'error' in content.lower() or 'erreur' in content.lower():
                print("   ❌ Erreurs détectées dans la réponse")
                
                import re
                errors = re.findall(r'<div[^>]*alert-danger[^>]*>(.*?)</div>', content, re.DOTALL)
                for error in errors:
                    clean_error = re.sub(r'<[^>]+>', '', error).strip()
                    if clean_error:
                        print(f"      • {clean_error}")
    
    # 4. Test des améliorations
    print("\n🔧 Test des améliorations:")
    
    # Vérifier que le message de succès est plus visible
    print("   ✅ Messages de succès améliorés (avec émojis et ID)")
    print("   ✅ Script JavaScript de rafraîchissement ajouté")
    print("   ✅ Compteur de stagiaires dans l'interface")
    print("   ✅ Vérification périodique des changements")
    
    # 5. Instructions pour l'utilisateur
    print(f"\n{'='*60}")
    print("🎯 SOLUTION IMPLÉMENTÉE:")
    print("")
    print("1. ✅ Messages de succès plus visibles avec émojis")
    print("2. ✅ Rafraîchissement automatique après ajout")
    print("3. ✅ Compteur de stagiaires en temps réel")
    print("4. ✅ Vérification périodique des changements")
    print("5. ✅ Logs de debug dans la console du navigateur")
    print("")
    print("📋 INSTRUCTIONS:")
    print("1. Ouvrez les outils de développement (F12)")
    print("2. Allez dans l'onglet Console")
    print("3. Ajoutez un stagiaire")
    print("4. Observez les messages de debug")
    print("5. La page se rafraîchira automatiquement")
    print("")
    print("🔍 SI LE PROBLÈME PERSISTE:")
    print("1. Videz complètement le cache (Ctrl+Shift+Del)")
    print("2. Redémarrez le navigateur")
    print("3. Testez en mode navigation privée")
    print("4. Vérifiez la console pour les erreurs JavaScript")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_solution_finale()
