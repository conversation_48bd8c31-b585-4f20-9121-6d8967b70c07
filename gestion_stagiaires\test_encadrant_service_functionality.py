#!/usr/bin/env python
"""
Script de test pour vérifier la fonctionnalité de gestion des services pour les encadrants
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Service, Thematique, Sujet

User = get_user_model()

def test_encadrant_service_functionality():
    """Test de la fonctionnalité complète pour les encadrants"""
    
    print("=== Test de la fonctionnalité Service pour Encadrants ===")
    
    # 1. Créer un service de test
    service_info, created = Service.objects.get_or_create(
        code_service='INFO',
        defaults={
            'nom': 'Service Informatique',
            'description': 'Service responsable de l\'informatique',
            'actif': True
        }
    )
    
    if created:
        print(f"✓ Service créé: {service_info.nom}")
    else:
        print(f"✓ Service existant: {service_info.nom}")
    
    # 2. Créer un encadrant avec ce service
    encadrant, created = User.objects.get_or_create(
        username='encadrant_info',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Jean',
            'last_name': 'Dupont',
            'role': 'ENCADRANT',
            'service': service_info
        }
    )
    
    if created:
        encadrant.set_password('testpassword')
        encadrant.save()
        print(f"✓ Encadrant créé: {encadrant.get_full_name()}")
    else:
        print(f"✓ Encadrant existant: {encadrant.get_full_name()}")
    
    print(f"✓ Service de l'encadrant: {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    # 3. Créer des thématiques pour ce service
    thematique1, created = Thematique.objects.get_or_create(
        titre='Développement Web',
        defaults={
            'description': 'Thématique sur le développement web',
            'service': service_info,
            'active': True,
            'cree_par': encadrant
        }
    )
    
    if created:
        print(f"✓ Thématique créée: {thematique1.titre}")
    else:
        print(f"✓ Thématique existante: {thematique1.titre}")
    
    # 4. Créer des sujets pour cette thématique
    sujet1, created = Sujet.objects.get_or_create(
        titre='Création d\'une application Django',
        defaults={
            'description': 'Développer une application web avec Django',
            'thematique': thematique1,
            'service': service_info,
            'encadrant': encadrant,
            'duree_recommandee': 60,
            'actif': True
        }
    )
    
    if created:
        print(f"✓ Sujet créé: {sujet1.titre}")
    else:
        print(f"✓ Sujet existant: {sujet1.titre}")
    
    # 5. Tester les filtres
    print("\n=== Test des filtres ===")
    
    # Thématiques visibles par l'encadrant
    thematiques_encadrant = Thematique.objects.filter(
        service=encadrant.service,
        active=True
    )
    print(f"✓ Thématiques du service INFO: {thematiques_encadrant.count()}")
    
    # Sujets visibles par l'encadrant
    sujets_encadrant = Sujet.objects.filter(
        service=encadrant.service,
        actif=True
    )
    print(f"✓ Sujets du service INFO: {sujets_encadrant.count()}")
    
    # 6. Créer un autre service pour tester l'isolation
    service_rh, created = Service.objects.get_or_create(
        code_service='RH',
        defaults={
            'nom': 'Ressources Humaines',
            'description': 'Service des ressources humaines',
            'actif': True
        }
    )
    
    if created:
        print(f"✓ Service RH créé: {service_rh.nom}")
    else:
        print(f"✓ Service RH existant: {service_rh.nom}")
    
    # Créer une thématique pour le service RH
    thematique_rh, created = Thematique.objects.get_or_create(
        titre='Gestion du personnel',
        defaults={
            'description': 'Thématique sur la gestion RH',
            'service': service_rh,
            'active': True
        }
    )
    
    if created:
        print(f"✓ Thématique RH créée: {thematique_rh.titre}")
    
    # Vérifier que l'encadrant INFO ne voit pas les thématiques RH
    thematiques_info_only = Thematique.objects.filter(
        service=service_info,
        active=True
    )
    
    thematiques_rh_only = Thematique.objects.filter(
        service=service_rh,
        active=True
    )
    
    print(f"✓ Isolation des services:")
    print(f"  - Thématiques INFO: {thematiques_info_only.count()}")
    print(f"  - Thématiques RH: {thematiques_rh_only.count()}")
    
    # 7. Test de validation du formulaire
    print("\n=== Test de validation ===")
    
    # Simuler la création d'un utilisateur encadrant sans service (devrait échouer)
    from stagiaires.forms import CustomUserCreationForm
    
    form_data = {
        'username': 'test_encadrant',
        'email': '<EMAIL>',
        'first_name': 'Test',
        'last_name': 'Encadrant',
        'role': 'ENCADRANT',
        'password1': 'testpassword123',
        'password2': 'testpassword123'
        # service manquant intentionnellement
    }
    
    form = CustomUserCreationForm(data=form_data)
    if not form.is_valid():
        print("✓ Validation échoue correctement pour encadrant sans service")
        print(f"  Erreurs: {form.errors}")
    else:
        print("✗ La validation devrait échouer pour un encadrant sans service")
    
    # Test avec service
    form_data['service'] = service_info.id
    form = CustomUserCreationForm(data=form_data)
    if form.is_valid():
        print("✓ Validation réussit avec service assigné")
    else:
        print(f"✗ La validation devrait réussir: {form.errors}")
    
    print("\n=== Test terminé avec succès ===")

if __name__ == '__main__':
    test_encadrant_service_functionality()
