#!/usr/bin/env python
"""
Diagnostic complet pour identifier les problèmes d'ajout de stagiaires
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service, Thematique, Sujet
from stagiaires.admin import StagiaireAdmin
from django.contrib import admin
from django.test import RequestFactory
from datetime import date, timedelta

User = get_user_model()

def diagnostic_complet():
    """Diagnostic complet du système"""
    
    print("=== DIAGNOSTIC COMPLET DU SYSTÈME ===")
    
    # 1. Vérification de la base de données
    print("\n1️⃣ ÉTAT DE LA BASE DE DONNÉES:")
    print("-" * 40)
    
    try:
        total_users = User.objects.count()
        total_stagiaires = Stagiaire.objects.count()
        total_services = Service.objects.count()
        
        print(f"✅ Utilisateurs: {total_users}")
        print(f"✅ Stagiaires: {total_stagiaires}")
        print(f"✅ Services: {total_services}")
        
        # Derniers stagiaires
        derniers = Stagiaire.objects.order_by('-date_creation')[:3]
        print(f"\nDerniers stagiaires créés:")
        for s in derniers:
            print(f"   • {s.nom_complet} - {s.date_creation.strftime('%Y-%m-%d %H:%M')}")
            
    except Exception as e:
        print(f"❌ Erreur d'accès à la base: {e}")
        return
    
    # 2. Vérification des utilisateurs admin
    print("\n2️⃣ UTILISATEURS ADMINISTRATEURS:")
    print("-" * 40)
    
    admins = User.objects.filter(is_superuser=True)
    print(f"Nombre d'admins: {admins.count()}")
    
    for admin in admins:
        print(f"   👤 {admin.username}:")
        print(f"      Email: {admin.email}")
        print(f"      Role: {admin.role}")
        print(f"      is_superuser: {admin.is_superuser}")
        print(f"      is_staff: {admin.is_staff}")
        print(f"      is_active: {admin.is_active}")
        print(f"      Dernière connexion: {admin.last_login}")
    
    # 3. Vérification des encadrants
    print("\n3️⃣ ENCADRANTS DISPONIBLES:")
    print("-" * 40)
    
    encadrants = User.objects.filter(role='ENCADRANT', is_active=True)
    print(f"Nombre d'encadrants actifs: {encadrants.count()}")
    
    for enc in encadrants:
        service_info = f"{enc.service.nom} ({enc.service.code_service})" if enc.service else "Aucun service"
        print(f"   👨‍🏫 {enc.get_full_name()} ({enc.username}):")
        print(f"      Email: {enc.email}")
        print(f"      Service: {service_info}")
        print(f"      Actif: {enc.is_active}")
    
    # 4. Vérification des services
    print("\n4️⃣ SERVICES DISPONIBLES:")
    print("-" * 40)
    
    services = Service.objects.all()
    print(f"Nombre total de services: {services.count()}")
    
    for service in services:
        encadrants_count = User.objects.filter(service=service, role='ENCADRANT').count()
        stagiaires_count = Stagiaire.objects.filter(service=service).count()
        print(f"   🏢 {service.nom} ({service.code_service}):")
        print(f"      Actif: {service.actif}")
        print(f"      Encadrants: {encadrants_count}")
        print(f"      Stagiaires: {stagiaires_count}")
    
    # 5. Test de création de stagiaire
    print("\n5️⃣ TEST DE CRÉATION DE STAGIAIRE:")
    print("-" * 40)
    
    admin_user = User.objects.filter(is_superuser=True).first()
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not admin_user:
        print("❌ Aucun administrateur trouvé")
        return
    
    if not encadrant:
        print("⚠️ Aucun encadrant trouvé")
        encadrant = None
    
    # Test avec données minimales
    test_email = f"diagnostic.test.{int(date.today().strftime('%Y%m%d'))}@example.com"
    
    try:
        # Supprimer le stagiaire de test s'il existe
        Stagiaire.objects.filter(email=test_email).delete()
        
        # Créer le stagiaire
        stagiaire_data = {
            'nom': 'DiagnosticTest',
            'prenom': 'Stagiaire',
            'email': test_email,
            'date_naissance': date(2000, 1, 1),
            'departement': 'IT',
            'date_debut': date.today(),
            'date_fin': date.today() + timedelta(days=90),
            'etablissement': 'Université Diagnostic',
            'niveau_etude': 'Master',
            'specialite': 'Informatique',
            'cree_par': admin_user
        }
        
        if encadrant:
            stagiaire_data['encadrant'] = encadrant
            stagiaire_data['service'] = encadrant.service
        
        stagiaire = Stagiaire.objects.create(**stagiaire_data)
        
        print("✅ Création de stagiaire réussie:")
        print(f"   ID: {stagiaire.id}")
        print(f"   Nom: {stagiaire.nom_complet}")
        print(f"   Email: {stagiaire.email}")
        print(f"   Créé par: {stagiaire.cree_par}")
        print(f"   Encadrant: {stagiaire.encadrant}")
        print(f"   Service: {stagiaire.service}")
        
        # Nettoyer
        stagiaire.delete()
        print("🧹 Stagiaire de test supprimé")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création: {e}")
        import traceback
        traceback.print_exc()
    
    # 6. Test de l'interface d'administration
    print("\n6️⃣ TEST DE L'INTERFACE D'ADMINISTRATION:")
    print("-" * 40)
    
    try:
        stagiaire_admin = StagiaireAdmin(Stagiaire, admin.site)
        factory = RequestFactory()
        request = factory.get('/admin/stagiaires/stagiaire/add/')
        request.user = admin_user
        
        # Permissions
        has_add = stagiaire_admin.has_add_permission(request)
        has_change = stagiaire_admin.has_change_permission(request)
        has_delete = stagiaire_admin.has_delete_permission(request)
        
        print(f"✅ Permissions:")
        print(f"   Ajouter: {'✅' if has_add else '❌'}")
        print(f"   Modifier: {'✅' if has_change else '❌'}")
        print(f"   Supprimer: {'✅' if has_delete else '❌'}")
        
        # Fieldsets
        fieldsets = stagiaire_admin.get_fieldsets(request)
        readonly_fields = stagiaire_admin.get_readonly_fields(request)
        
        print(f"✅ Configuration:")
        print(f"   Fieldsets: {len(fieldsets)}")
        print(f"   Champs en lecture seule: {len(readonly_fields)}")
        print(f"   'cree_par' en lecture seule: {'✅' if 'cree_par' in readonly_fields else '❌'}")
        
        # Inlines
        inlines = stagiaire_admin.get_inline_instances(request)
        print(f"   Inlines: {len(inlines)}")
        for inline in inlines:
            print(f"      • {inline.__class__.__name__}")
        
    except Exception as e:
        print(f"❌ Erreur dans l'interface d'admin: {e}")
        import traceback
        traceback.print_exc()
    
    # 7. Vérification des contraintes du modèle
    print("\n7️⃣ CONTRAINTES DU MODÈLE STAGIAIRE:")
    print("-" * 40)
    
    try:
        from django.core.exceptions import ValidationError
        
        # Test avec données incomplètes
        stagiaire_test = Stagiaire(
            nom='Test',
            prenom='Contrainte',
            email='<EMAIL>'
        )
        
        try:
            stagiaire_test.full_clean()
            print("⚠️ Validation réussie avec données incomplètes")
        except ValidationError as e:
            print("✅ Contraintes de validation actives:")
            for field, errors in e.message_dict.items():
                print(f"   • {field}: {errors[0] if errors else 'Erreur'}")
        
    except Exception as e:
        print(f"❌ Erreur lors du test de contraintes: {e}")
    
    # 8. Recommandations
    print("\n8️⃣ RECOMMANDATIONS:")
    print("-" * 40)
    
    if not encadrants.exists():
        print("⚠️ Aucun encadrant disponible - créez au moins un encadrant")
    
    if not services.filter(actif=True).exists():
        print("⚠️ Aucun service actif - créez au moins un service")
    
    print("✅ Pour tester l'ajout de stagiaires:")
    print("   1. Utilisez un email unique à chaque test")
    print("   2. Remplissez TOUS les champs obligatoires")
    print("   3. Vérifiez la console JavaScript (F12) pour les erreurs")
    print("   4. Testez avec différents navigateurs")
    print("   5. Désactivez les extensions de navigateur")
    
    print(f"\n{'='*50}")
    print("📊 DIAGNOSTIC TERMINÉ")
    print(f"{'='*50}")

if __name__ == '__main__':
    diagnostic_complet()
