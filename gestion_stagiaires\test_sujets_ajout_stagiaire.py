#!/usr/bin/env python
"""
Test de l'affichage des sujets dans l'ajout de stagiaire
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Sujet, Thematique, Service
import json

User = get_user_model()

def test_sujets_ajout_stagiaire():
    """Test de l'affichage des sujets dans l'ajout de stagiaire"""
    
    print("=== TEST SUJETS DANS AJOUT DE STAGIAIRE ===")
    
    # 1. Vérifier les données existantes
    print(f"📊 État actuel:")
    print(f"   Sujets actifs: {Sujet.objects.filter(actif=True).count()}")
    print(f"   Thématiques actives: {Thematique.objects.filter(active=True).count()}")
    
    # Afficher les sujets par thématique
    thematiques = Thematique.objects.filter(active=True)
    for thematique in thematiques:
        sujets_count = Sujet.objects.filter(thematique=thematique, actif=True).count()
        print(f"   Thématique '{thematique.titre}': {sujets_count} sujets")
    
    # 2. Test de l'API des sujets par thématique
    print(f"\n🔌 Test de l'API sujets par thématique:")
    
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    if not admin:
        print("❌ Aucun admin trouvé")
        return
    
    client = Client()
    client.force_login(admin)
    
    # Tester l'API pour chaque thématique
    for thematique in thematiques[:3]:  # Tester les 3 premières
        response = client.get(f'/api/thematiques/{thematique.id}/sujets/')
        print(f"   Thématique {thematique.id} ({thematique.titre}): Status {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"      Sujets retournés: {len(data)}")
                
                # Afficher les premiers sujets
                for sujet in data[:2]:
                    print(f"        • {sujet['titre']} (ID: {sujet['id']})")
            except json.JSONDecodeError:
                print(f"      ❌ Erreur de décodage JSON")
        else:
            print(f"      ❌ Erreur API")
    
    # 3. Test du formulaire d'ajout de stagiaire
    print(f"\n📝 Test du formulaire d'ajout de stagiaire:")
    
    # Test avec Admin
    response = client.get('/stagiaires/add/')
    print(f"   Status formulaire: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier la présence du champ sujet
        if 'name="sujet"' in content:
            print(f"   ✅ Champ sujet présent")
        else:
            print(f"   ❌ Champ sujet MANQUANT")
        
        # Vérifier la présence du JavaScript
        if 'updateSujets' in content:
            print(f"   ✅ JavaScript updateSujets présent")
        else:
            print(f"   ❌ JavaScript updateSujets MANQUANT")
        
        # Vérifier l'URL de l'API dans le JavaScript
        if '/api/thematiques/' in content:
            print(f"   ✅ URL API présente dans le JavaScript")
        else:
            print(f"   ❌ URL API MANQUANTE dans le JavaScript")
        
        # Compter les options de sujets dans le select
        sujet_options = content.count('<option value=') - content.count('thematique')
        print(f"   Options de sujets dans le select: {sujet_options}")
        
        # Vérifier si les sujets sont pré-chargés
        sujets_actifs = Sujet.objects.filter(actif=True)
        for sujet in sujets_actifs[:3]:
            if f'value="{sujet.id}"' in content:
                print(f"      ✅ Sujet '{sujet.titre}' présent")
            else:
                print(f"      ⚠️ Sujet '{sujet.titre}' non présent")
    
    # 4. Test avec Encadrant
    print(f"\n👤 Test avec encadrant:")
    
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    if encadrant and encadrant.service:
        client.force_login(encadrant)
        response = client.get('/stagiaires/add/')
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # Vérifier que les sujets sont filtrés par service
            sujets_service = Sujet.objects.filter(
                service=encadrant.service, actif=True
            )
            
            print(f"   Sujets du service {encadrant.service.nom}: {sujets_service.count()}")
            
            for sujet in sujets_service[:2]:
                if f'value="{sujet.id}"' in content:
                    print(f"      ✅ Sujet du service '{sujet.titre}' présent")
                else:
                    print(f"      ⚠️ Sujet du service '{sujet.titre}' non présent")
    
    # 5. Test de soumission avec sujet
    print(f"\n📤 Test de soumission avec sujet:")
    
    client.force_login(admin)
    
    # Récupérer un sujet pour le test
    sujet_test = Sujet.objects.filter(actif=True).first()
    thematique_test = sujet_test.thematique if sujet_test else None
    
    if sujet_test and thematique_test:
        from datetime import date, timedelta
        
        test_data = {
            'nom': 'TESTSUJET',
            'prenom': 'AVEC',
            'email': '<EMAIL>',
            'date_naissance': '2000-01-01',
            'date_debut': date.today().strftime('%Y-%m-%d'),
            'date_fin': (date.today() + timedelta(days=60)).strftime('%Y-%m-%d'),
            'etablissement': 'Test Sujet',
            'niveau_etude': 'Master',
            'specialite': 'Test',
            'thematique': thematique_test.id,
            'sujet': sujet_test.id,
            'technologies': 'Test technologies',
        }
        
        print(f"   Données de test:")
        print(f"     • Thématique: {thematique_test.titre}")
        print(f"     • Sujet: {sujet_test.titre}")
        
        # Supprimer le stagiaire de test s'il existe
        from stagiaires.models import Stagiaire
        Stagiaire.objects.filter(email='<EMAIL>').delete()
        
        response = client.post('/stagiaires/add/', test_data)
        print(f"   Status POST: {response.status_code}")
        
        if response.status_code == 302:
            print("   ✅ Soumission réussie")
            
            # Vérifier la création
            stagiaire_cree = Stagiaire.objects.filter(email='<EMAIL>').first()
            if stagiaire_cree:
                print(f"      • Stagiaire créé: {stagiaire_cree.nom_complet}")
                print(f"      • Thématique: {stagiaire_cree.thematique.titre if stagiaire_cree.thematique else 'Aucune'}")
                print(f"      • Sujet: {stagiaire_cree.sujet.titre if stagiaire_cree.sujet else 'Aucun'}")
                
                # Nettoyer
                stagiaire_cree.delete()
                print(f"      🧹 Stagiaire de test supprimé")
        else:
            print("   ❌ Soumission échouée")
    
    print(f"\n{'='*60}")
    print("📊 DIAGNOSTIC DU PROBLÈME:")
    print("")
    print("✅ POINTS À VÉRIFIER :")
    print("   1. Le champ 'sujet' est-il présent dans le formulaire ?")
    print("   2. L'API /api/thematiques/{id}/sujets/ fonctionne-t-elle ?")
    print("   3. Le JavaScript updateSujets() est-il présent ?")
    print("   4. Les sujets sont-ils pré-chargés dans le select ?")
    print("   5. Le filtrage par thématique fonctionne-t-il ?")
    print("")
    print("🔧 SOLUTIONS POSSIBLES :")
    print("   • Vérifier que les sujets ont des thématiques assignées")
    print("   • Vérifier que les sujets sont actifs (actif=True)")
    print("   • Vérifier la console JavaScript pour les erreurs")
    print("   • Vérifier que l'API retourne le bon format JSON")
    print("   • Vérifier les permissions d'accès aux sujets")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_sujets_ajout_stagiaire()
