/* ===== STYLES POUR PAGES D'AUTHENTIFICATION - BLEU ET BLANC ===== */

/* Variables CSS pour les couleurs */
:root {
    --primary-blue: #2563eb;
    --light-blue: #3b82f6;
    --dark-blue: #1d4ed8;
    --very-light-blue: #dbeafe;
    --blue-gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --light-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

    --card-shadow: 0 10px 25px rgba(37, 99, 235, 0.1);
    --card-shadow-hover: 0 15px 35px rgba(37, 99, 235, 0.15);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* Background sobre pour les pages d'auth */
body.auth-page {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #dbeafe 100%);
    min-height: 100vh;
    position: relative;
}

/* Motif subtil en arrière-plan */
body.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(37, 99, 235, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(37, 99, 235, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

/* Container principal pour l'authentification */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* Carte d'authentification */
.auth-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(37, 99, 235, 0.1);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.auth-card:hover {
    box-shadow: var(--card-shadow-hover);
}

/* Header de la carte avec gradient bleu */
.auth-header {
    background: var(--blue-gradient);
    color: white;
    padding: 30px;
    text-align: center;
    position: relative;
}

.auth-header h2 {
    font-weight: 700;
    font-size: 1.8rem;
    margin-bottom: 0;
    position: relative;
    z-index: 1;
}

.auth-header p {
    font-size: 0.95rem;
    margin-top: 8px;
    position: relative;
    z-index: 1;
}

/* Corps de la carte */
.auth-body {
    padding: 40px;
}

/* Styles pour les champs de formulaire */
.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.form-label i {
    color: var(--primary-blue);
    margin-right: 8px;
}

.form-control {
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 0.95rem;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.15);
    background: white;
    transform: translateY(-1px);
}

.form-control:hover {
    border-color: var(--light-blue);
    background: white;
}

/* Boutons avec style bleu et blanc */
.btn-primary {
    background: var(--blue-gradient);
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
    color: white;
}

.btn-primary:hover {
    background: var(--dark-blue);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    color: white;
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-outline-primary {
    background: transparent;
    border: 2px solid var(--primary-blue);
    border-radius: 10px;
    padding: 10px 22px;
    font-weight: 600;
    color: var(--primary-blue);
    transition: var(--transition);
}

.btn-outline-primary:hover {
    background: var(--primary-blue);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.2);
}

.btn-outline-success {
    background: transparent;
    border: 2px solid var(--light-blue);
    border-radius: 10px;
    padding: 8px 20px;
    font-weight: 500;
    color: var(--light-blue);
    transition: var(--transition);
}

.btn-outline-success:hover {
    background: var(--light-blue);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);
}

.btn-secondary {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 10px 22px;
    font-weight: 600;
    color: #64748b;
    transition: var(--transition);
}

.btn-secondary:hover {
    background: #e2e8f0;
    color: #475569;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(100, 116, 139, 0.15);
}

/* Liens avec couleurs bleues */
.auth-body a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.auth-body a:hover {
    color: var(--dark-blue);
    text-decoration: underline;
    transform: translateX(2px);
}

.text-muted {
    color: #6b7280 !important;
}

/* Alertes avec style bleu et blanc */
.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 10px;
    color: #991b1b;
}

.alert-success {
    background: rgba(37, 99, 235, 0.1);
    border: 1px solid rgba(37, 99, 235, 0.2);
    border-radius: 10px;
    color: var(--dark-blue);
}

.alert-warning {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 10px;
    color: var(--primary-blue);
}

/* Checkbox avec style personnalisé */
.form-check-input {
    border-radius: 6px;
    border: 2px solid #e5e7eb;
    transition: var(--transition);
}

.form-check-input:checked {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.15);
}

/* Select avec style personnalisé */
.form-select {
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.9);
    transition: var(--transition);
}

.form-select:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.15);
    background: white;
}

/* Animation subtile pour les icônes */
.auth-header i {
    animation: subtlePulse 3s ease-in-out infinite;
}

.form-label i {
    transition: var(--transition);
}

.form-label i:hover {
    transform: scale(1.1);
    color: var(--light-blue);
}

@keyframes subtlePulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.9; }
}

/* Responsive */
@media (max-width: 768px) {
    .auth-container {
        padding: 10px;
    }
    
    .auth-header {
        padding: 20px;
    }
    
    .auth-body {
        padding: 20px;
    }
    
    .auth-header h2 {
        font-size: 1.5rem;
    }
}

/* Effet de loading pour les boutons */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Effet de focus pour les champs requis */
.form-control:required:invalid {
    border-color: #ffc107;
}

.form-control:required:valid {
    border-color: #28a745;
}

/* Styles pour les messages d'aide */
.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
}

/* Animation d'entrée pour la carte */
.auth-card {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
