{% extends 'stagiaires/base.html' %}

{% block title %}Upload Convention - {{ stagiaire.nom_complet }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-file-upload me-2"></i>
                        Upload Convention de Stage
                    </h4>
                    <small>{{ stagiaire.nom_complet }} - {{ stagiaire.get_departement_display }}</small>
                </div>
                <div class="card-body">
                    <!-- Informations du stagiaire -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-user me-2"></i>Informations du stagiaire</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Nom :</strong> {{ stagiaire.nom_complet }}<br>
                                <strong>Email :</strong> {{ stagiaire.email }}<br>
                                <strong>Département :</strong> {{ stagiaire.get_departement_display }}
                            </div>
                            <div class="col-md-6">
                                <strong>Période :</strong> {{ stagiaire.date_debut }} - {{ stagiaire.date_fin }}<br>
                                <strong>Encadrant :</strong> {{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}<br>
                                <strong>Statut actuel :</strong> 
                                <span class="badge bg-{% if stagiaire.statut_convention == 'VALIDEE' %}success{% elif stagiaire.statut_convention == 'REJETEE' %}danger{% elif stagiaire.statut_convention == 'MODIFIEE' %}warning{% else %}secondary{% endif %}">
                                    {{ stagiaire.get_statut_convention_display }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Convention actuelle -->
                    {% if stagiaire.convention_stage %}
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-file-pdf me-2"></i>Convention actuelle</h6>
                        <p>Une convention est déjà uploadée : 
                            <a href="{{ stagiaire.convention_stage.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download me-1"></i>Télécharger
                            </a>
                        </p>
                        <small class="text-muted">Uploader un nouveau fichier remplacera la convention actuelle.</small>
                    </div>
                    {% endif %}

                    <!-- Formulaire d'upload -->
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.convention_stage.id_for_label }}" class="form-label">
                                <i class="fas fa-file me-1"></i>Fichier de convention
                            </label>
                            {{ form.convention_stage }}
                            {% if form.convention_stage.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.convention_stage.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Formats acceptés : PDF, DOC, DOCX. Taille maximale : 10 MB.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.commentaire_convention.id_for_label }}" class="form-label">
                                <i class="fas fa-comment me-1"></i>Commentaires
                            </label>
                            {{ form.commentaire_convention }}
                            {% if form.commentaire_convention.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.commentaire_convention.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'stagiaires_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-1"></i>Uploader la convention
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.alert h6 {
    margin-bottom: 10px;
    font-weight: bold;
}
</style>
{% endblock %}
