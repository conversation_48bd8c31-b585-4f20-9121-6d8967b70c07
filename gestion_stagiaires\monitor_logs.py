#!/usr/bin/env python
"""
Script pour surveiller les logs et diagnostiquer les problèmes en temps réel
"""

import os
import sys
import django
import time
from datetime import datetime

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire

User = get_user_model()

def monitor_logs():
    """Surveiller les logs en temps réel"""
    
    print("=== SURVEILLANCE DES LOGS EN TEMPS RÉEL ===")
    print(f"Démarré à: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # État initial
    initial_count = Stagiaire.objects.count()
    print(f"📊 État initial: {initial_count} stagiaires en base")
    
    # Derniers stagiaires
    derniers = Stagiaire.objects.order_by('-date_creation')[:3]
    print("📋 Derniers stagiaires:")
    for s in derniers:
        print(f"   • {s.nom_complet} - {s.date_creation.strftime('%H:%M:%S')}")
    
    print()
    print("🔍 SURVEILLANCE ACTIVE - Testez maintenant l'ajout de stagiaire")
    print("   (Appuyez sur Ctrl+C pour arrêter)")
    print("-" * 60)
    
    try:
        while True:
            time.sleep(2)  # Vérifier toutes les 2 secondes
            
            # Vérifier s'il y a de nouveaux stagiaires
            current_count = Stagiaire.objects.count()
            
            if current_count > initial_count:
                print(f"\n🎉 NOUVEAU STAGIAIRE DÉTECTÉ! ({current_count} total)")
                
                # Récupérer le nouveau stagiaire
                nouveaux = Stagiaire.objects.order_by('-date_creation')[:current_count - initial_count]
                
                for nouveau in nouveaux:
                    print(f"✅ Créé: {nouveau.nom_complet}")
                    print(f"   Email: {nouveau.email}")
                    print(f"   Créé par: {nouveau.cree_par}")
                    print(f"   Heure: {nouveau.date_creation.strftime('%H:%M:%S')}")
                    print(f"   Encadrant: {nouveau.encadrant}")
                    print(f"   Service: {nouveau.service}")
                
                initial_count = current_count
                print("-" * 60)
            
            # Afficher un point toutes les 10 secondes pour montrer que ça fonctionne
            if int(time.time()) % 10 == 0:
                print(".", end="", flush=True)
    
    except KeyboardInterrupt:
        print(f"\n\n📊 SURVEILLANCE ARRÊTÉE à {datetime.now().strftime('%H:%M:%S')}")
        
        # État final
        final_count = Stagiaire.objects.count()
        print(f"État final: {final_count} stagiaires en base")
        
        if final_count > initial_count:
            print(f"✅ {final_count - initial_count} nouveau(x) stagiaire(s) créé(s)")
        else:
            print("❌ Aucun nouveau stagiaire créé")
            print()
            print("💡 Si vous avez essayé d'ajouter un stagiaire:")
            print("   • Vérifiez les erreurs dans le formulaire")
            print("   • Vérifiez la console JavaScript (F12)")
            print("   • Assurez-vous que l'email est unique")
            print("   • Vérifiez que tous les champs obligatoires sont remplis")

def check_recent_activity():
    """Vérifier l'activité récente"""
    
    print("\n🔍 VÉRIFICATION DE L'ACTIVITÉ RÉCENTE:")
    print("-" * 40)
    
    # Stagiaires créés dans les dernières 24h
    from datetime import timedelta
    from django.utils import timezone
    
    yesterday = timezone.now() - timedelta(days=1)
    recent_stagiaires = Stagiaire.objects.filter(date_creation__gte=yesterday).order_by('-date_creation')
    
    print(f"Stagiaires créés dans les dernières 24h: {recent_stagiaires.count()}")
    
    for s in recent_stagiaires:
        print(f"   • {s.nom_complet} - {s.date_creation.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"     Email: {s.email}")
        print(f"     Créé par: {s.cree_par}")
    
    # Connexions admin récentes
    recent_logins = User.objects.filter(
        is_superuser=True,
        last_login__gte=yesterday
    ).order_by('-last_login')
    
    print(f"\nConnexions admin récentes: {recent_logins.count()}")
    for user in recent_logins:
        if user.last_login:
            print(f"   • {user.username} - {user.last_login.strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'check':
        check_recent_activity()
    else:
        monitor_logs()
