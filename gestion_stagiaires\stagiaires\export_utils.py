# export_utils.py - Fonctions d'exportation pour les stagiaires
from django.http import HttpResponse
from django.shortcuts import redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from .models import Stagiaire
import csv
from datetime import datetime

@login_required
def export_durees_csv(request):
    """Exporter les durées estimées au format CSV"""
    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    # Récupérer les filtres
    departement_filter = request.GET.get('departement', '')
    statut_filter = request.GET.get('statut', '')
    encadrant_filter = request.GET.get('encadrant', '')
    
    # Récupérer les stagiaires filtrés
    stagiaires = Stagiaire.objects.all().select_related('encadrant', 'service', 'sujet')
    
    if departement_filter:
        stagiaires = stagiaires.filter(departement=departement_filter)
    
    if statut_filter:
        stagiaires = stagiaires.filter(statut=statut_filter)
    
    if encadrant_filter:
        stagiaires = stagiaires.filter(encadrant_id=encadrant_filter)
    
    # Créer la réponse HTTP avec le type de contenu CSV
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="durees_estimees_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'
    
    # Créer le writer CSV
    writer = csv.writer(response)
    writer.writerow(['Nom', 'Prénom', 'Email', 'Département', 'Encadrant', 'Sujet', 
                    'Date début', 'Date fin', 'Durée estimée (jours)', 'Durée réelle (jours)', 'Écart (jours)'])
    
    # Ajouter les données
    for stagiaire in stagiaires:
        encadrant_nom = stagiaire.encadrant.get_full_name() if stagiaire.encadrant else 'Non assigné'
        sujet_titre = stagiaire.sujet.titre if hasattr(stagiaire, 'sujet') and stagiaire.sujet else 'Non défini'
        duree_reelle = stagiaire.duree_stage
        ecart = duree_reelle - getattr(stagiaire, 'duree_estimee', 0)
        
        writer.writerow([
            stagiaire.nom,
            stagiaire.prenom,
            stagiaire.email,
            stagiaire.get_departement_display(),
            encadrant_nom,
            sujet_titre,
            stagiaire.date_debut.strftime('%d/%m/%Y') if stagiaire.date_debut else '',
            stagiaire.date_fin.strftime('%d/%m/%Y') if stagiaire.date_fin else '',
            getattr(stagiaire, 'duree_estimee', 0),
            duree_reelle,
            ecart
        ])
    
    return response

@login_required
def export_durees_excel(request):
    """Exporter les durées estimées au format CSV (alternative à Excel)"""
    # Pour l'instant, on utilise simplement le format CSV
    # Si vous souhaitez un vrai export Excel, installez xlwt ou openpyxl
    return export_durees_csv(request)



