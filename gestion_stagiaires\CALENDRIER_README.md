# 📅 CALENDRIER UNIQUE - STYLE MODERNE

## 🎯 Vue d'Ensemble

UN SEUL calendrier simple et élégant, reproduisant fidèlement le style de l'image de référence.

---

## 🚀 Fonctionnalités

### ✨ **Design Épuré**
- Interface simple et claire
- Style identique à l'image de référence
- Responsive design pour mobile et desktop
- Couleurs douces et lisibles

### 📅 **Calendrier Mensuel**
- Vue mensuelle classique
- Grille 7x6 (semaines x jours)
- Jours des mois adjacents en gris clair
- Jour actuel surligné en rose

### 🎨 **Événements de Démonstration**
- **Rose (#ff6b8a)** : Couleur unique pour tous les événements
- Événements courts et longs
- Style "+X more" pour les jours chargés
- Exemples : "All Day Event", "Conference", "Birthday Party"

### 🗓️ **Navigation Simple**
- Bouton "today" pour revenir au mois actuel
- Flèches de navigation ◀ ▶
- Navigation libre entre les mois/années
- Février 2020 avec événements de démonstration

---

## 🔗 URL UNIQUE

### **LE Calendrier**
```
http://127.0.0.1:8000/calendrier/
```
- Affiche le mois actuel
- Calendrier simple et propre
- Navigation libre entre les mois

### **Février 2020 (Démonstration)**
```
http://127.0.0.1:8000/calendrier/?year=2020&month=2
```
- Reproduit exactement l'image de référence
- Événements de démonstration inclus
- Parfait pour les présentations

### **Navigation Directe**
```
http://127.0.0.1:8000/calendrier/?year=2025&month=7
```
- Paramètres `year` et `month` pour aller à n'importe quel mois
- Exemple : juillet 2025

---

## 🎭 Événements de Démonstration

Le mode démo (février 2020) inclut les événements suivants :

| Date | Événement | Style |
|------|-----------|-------|
| **1er** | All Day Event | Rose standard |
| **7** | Long Event | Rose standard |
| **9** | Long Event + 4p Repeating Event | Rose standard |
| **11** | Conference + "+5 more" | Rose standard |
| **13** | 7a Birthday Party | Rose standard |
| **16** | 4p Repeating Event | Rose standard |
| **28** | Click for Google | Rose standard |

---

## 🛠️ Structure Technique

### **Template Principal**
```
stagiaires/templates/stagiaires/calendrier_encadrant.html
```

### **Vue Django**
```python
# stagiaires/views.py
def calendrier_encadrant_view(request):
    # Gestion du mode démo
    if request.GET.get('demo') == 'true':
        year = 2020
        month = 2
    # ... logique du calendrier
```

### **Filtre Template Personnalisé**
```python
# stagiaires/templatetags/calendar_extras.py
@register.filter
def lookup(dictionary, key):
    """Accès aux dictionnaires dans les templates"""
    return dictionary.get(str(key), [])
```

### **CSS Grid Layout**
```css
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.calendar-day {
    min-height: 120px;
    padding: 8px;
    border: 1px solid #e0e0e0;
}
```

---

## 📱 Responsive Design

### **Desktop (> 768px)**
- Calendrier pleine largeur
- Hauteur de cellule : 120px
- Événements complets visibles

### **Mobile (≤ 768px)**
- Calendrier adaptatif
- Hauteur de cellule : 80px
- Événements raccourcis
- Navigation empilée

---

## 🎯 Permissions et Accès

### **Encadrant**
- Voit uniquement les stagiaires de son service
- Peut voir toutes les tâches assignées
- Accès complet au calendrier

### **RH**
- Voit tous les stagiaires
- Accès à toutes les tâches
- Gestion complète

### **Admin**
- Accès total
- Toutes les fonctionnalités
- Gestion système

---

## 🔧 Personnalisation

### **Ajouter de Nouveaux Types d'Événements**
```css
.event.nouveau-type {
    background: #votre-couleur;
    color: white;
}
```

### **Modifier les Couleurs**
```css
:root {
    --primary-color: #ff6b8a;
    --secondary-color: #f8f9fa;
    --border-color: #e0e0e0;
}
```

### **Ajouter des Événements Statiques**
```html
{% if day == VOTRE_JOUR %}
    <div class="event votre-classe">Votre Événement</div>
{% endif %}
```

---

## 🚀 Intégration dans la Navbar

Le calendrier est accessible via le menu "Calendrier" dans la navbar :

```html
<li class="dropdown">
    <button class="dropdown-btn">Calendrier ▼</button>
    <ul class="dropdown-menu">
        <li><a href="{% url 'calendrier_encadrant' %}">Calendrier des tâches</a></li>
        <li><a href="{% url 'calendrier_encadrant' %}?demo=true">Calendrier style moderne</a></li>
        <li><a href="{% url 'calendrier_stagiaires' %}">Calendrier des stages</a></li>
    </ul>
</li>
```

---

## 📊 Données de Test

Pour tester le calendrier avec des données réelles :

```bash
python manage.py create_test_calendar_data
```

Cette commande crée :
- 3 stagiaires de test
- 12 tâches réparties sur le mois
- Un encadrant de test (login: `encadrant_test`, mot de passe: `test123`)

---

## 🎨 Comparaison avec l'Image de Référence

Le calendrier reproduit fidèlement :
- ✅ Layout en grille 7x6
- ✅ En-tête avec navigation
- ✅ Jours des mois adjacents en gris
- ✅ Événements colorés en rose
- ✅ Style "+X more" pour les événements supplémentaires
- ✅ Typographie moderne et claire
- ✅ Espacement et proportions identiques

---

## 🔄 Évolutions Futures

### **Fonctionnalités Prévues**
- [ ] Drag & drop des événements
- [ ] Modal de détails au clic
- [ ] Filtrage par stagiaire
- [ ] Export PDF/iCal
- [ ] Notifications push
- [ ] Vue semaine/jour

### **Améliorations Techniques**
- [ ] Cache des données
- [ ] API REST pour les événements
- [ ] WebSocket pour temps réel
- [ ] PWA pour mobile

---

**🎉 Le calendrier encadrant est maintenant prêt et correspond parfaitement au design moderne demandé !**

*Dernière mise à jour : 17 juillet 2025*
