# 📊 Diagrammes PlantUML - Système de Gestion des Stagiaires

Ce document contient tous les diagrammes PlantUML pour documenter l'architecture et le fonctionnement du système de gestion des stagiaires.

## 📋 Diagrammes Disponibles

### 1. 🏗️ Diagramme de Classes
**Fichier:** `Diagramme_Classes_Gestion_Stagiaires`
- **Description:** Structure complète du système avec tous les modèles
- **Contenu:** 
  - Mod<PERSON><PERSON> principaux (CustomUser, Stagiaire, Service, etc.)
  - Relations entre les entités
  - Attributs et méthodes importantes
  - Énumérations (choix prédéfinis)

### 2. 👥 Diagramme de Cas d'Usage
**Fichier:** `Diagramme_Cas_Usage_Gestion_Stagiaires`
- **Description:** Fonctionnalités disponibles par rôle d'utilisateur
- **Acteurs:**
  - 🔴 Administrateur (gestion complète)
  - 🟠 RH Manager (gestion stagiaires et documents)
  - 🔵 Encadrant (suivi et évaluation)
  - 🟢 Stagiaire (consultation et soumission)

### 3. 🔄 Diagrammes de Séquence

#### 3.1 Ajout de Stagiaire
**Fichier:** `Sequence_Ajout_Stagiaire`
- Processus complet d'ajout d'un nouveau stagiaire par RH
- Validation des données et notifications

#### 3.2 Validation de Convention
**Fichier:** `Sequence_Validation_Convention`
- Upload de convention par le stagiaire
- Validation par RH avec notifications

#### 3.3 Filtrage par Service
**Fichier:** `Sequence_Filtrage_Stagiaires`
- Mécanisme de filtrage des stagiaires selon le service de l'encadrant
- Mapping service → département

### 4. 📈 Diagramme d'Activité
**Fichier:** `Activite_Gestion_Stage`
- **Description:** Workflow complet de gestion d'un stage
- **Processus:** De l'ajout du stagiaire à la génération de l'attestation
- **Acteurs:** RH, Encadrant, Stagiaire

### 5. 🔄 Diagramme d'État
**Fichier:** `Etats_Stagiaire`
- **Description:** Cycle de vie complet d'un stagiaire
- **États:** Créé → Convention → Tâches → Terminé → Attestation
- **Transitions:** Conditions de passage entre états

## 🛠️ Utilisation

### Option 1: Génération Automatique
```bash
python generer_diagrammes.py
```
Génère tous les diagrammes en images PNG dans le dossier `diagrammes_output/`

### Option 2: VSCode avec Extension PlantUML
1. Installez l'extension "PlantUML" dans VSCode
2. Ouvrez le fichier `diagrammes_plantuml.md`
3. Utilisez `Ctrl+Shift+P` → "PlantUML: Preview Current Diagram"

### Option 3: PlantUML en Ligne
1. Copiez le code d'un diagramme
2. Allez sur http://www.plantuml.com/plantuml/
3. Collez le code et générez l'image

## 📦 Installation des Outils

### PlantUML (pour génération automatique)
```bash
# Via npm
npm install -g plantuml

# Ou téléchargez plantuml.jar
# http://plantuml.com/download
```

### Extension VSCode
1. Ouvrez VSCode
2. Allez dans Extensions (Ctrl+Shift+X)
3. Recherchez "PlantUML"
4. Installez l'extension de jebbs

## 🎨 Personnalisation

### Couleurs
Les diagrammes utilisent un code couleur cohérent :
- 🔴 **Rouge** : Administrateur
- 🟠 **Orange** : RH
- 🔵 **Bleu** : Encadrant  
- 🟢 **Vert** : Stagiaire
- 🟡 **Jaune** : Documents
- 🟣 **Rose** : Tâches/Rapports

### Modification
1. Éditez le fichier `diagrammes_plantuml.md`
2. Modifiez le code PlantUML selon vos besoins
3. Régénérez les images avec le script

## 📁 Structure des Fichiers

```
gestion_stagiaires/
├── diagrammes_plantuml.md          # Code source des diagrammes
├── generer_diagrammes.py           # Script de génération
├── README_DIAGRAMMES.md            # Ce fichier
└── diagrammes_output/              # Dossier généré
    ├── diagramme_classes.puml      # Code source
    ├── diagramme_classes.png       # Image générée
    ├── diagramme_cas_usage.puml
    ├── diagramme_cas_usage.png
    └── ...
```

## 🎯 Cas d'Usage des Diagrammes

### Pour la Documentation
- **Classes** : Comprendre la structure de la base de données
- **Cas d'usage** : Identifier les fonctionnalités par rôle
- **Séquence** : Comprendre les processus métier

### Pour le Développement
- **Classes** : Guide pour les relations entre modèles
- **Séquence** : Implémentation des workflows
- **États** : Gestion des transitions de statut

### Pour la Formation
- **Cas d'usage** : Formation des utilisateurs par rôle
- **Activité** : Comprendre le processus global
- **États** : Suivi du cycle de vie des stages

## 🔧 Dépannage

### PlantUML non trouvé
```bash
# Vérifiez l'installation
plantuml -version

# Ou utilisez Java directement
java -jar plantuml.jar -version
```

### Erreurs de génération
- Vérifiez la syntaxe PlantUML
- Assurez-vous que les balises @startuml/@enduml sont correctes
- Utilisez l'aperçu VSCode pour déboguer

### Images non générées
- Vérifiez les permissions du dossier
- Assurez-vous que PlantUML est dans le PATH
- Utilisez le mode verbose : `plantuml -v`

## 📞 Support

Pour toute question sur les diagrammes :
1. Consultez la documentation PlantUML : http://plantuml.com/
2. Utilisez l'aide du script : `python generer_diagrammes.py --help`
3. Vérifiez les exemples dans le fichier source

---

**Note :** Ces diagrammes sont générés automatiquement à partir du code source et reflètent l'état actuel du système. Ils sont mis à jour régulièrement pour rester synchronisés avec l'implémentation.
