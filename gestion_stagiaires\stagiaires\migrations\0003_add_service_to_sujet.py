# Generated manually to add service_id to sujet table

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('stagiaires', '0002_create_thematique_table'),
    ]

    operations = [
        migrations.AddField(
            model_name='sujet',
            name='service',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='sujets',
                to='stagiaires.service',
                verbose_name='Service'
            ),
        ),
    ]