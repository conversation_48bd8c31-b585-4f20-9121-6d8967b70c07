{% extends 'stagiaires/base.html' %}

{% block title %}Contrat {{ contrat.reference }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0">
                            <i class="fas fa-file-contract me-2"></i>
                            Contrat de Stage {{ contrat.reference }}
                        </h4>
                        <small>{{ contrat.stagiaire.nom_complet }} - {{ contrat.stagiaire.get_departement_display }}</small>
                    </div>
                    <div>
                        <span class="badge bg-{% if contrat.statut == 'ENTIEREMENT_SIGNE' %}success{% elif contrat.statut == 'PARTIELLEMENT_SIGNE' %}warning{% elif contrat.statut == 'EN_ATTENTE_SIGNATURE' %}info{% else %}secondary{% endif %} fs-6">
                            {{ contrat.get_statut_display }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Informations générales -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations générales</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Référence :</strong> {{ contrat.reference }}</p>
                                    <p><strong>Type :</strong> {{ contrat.get_type_contrat_display }}</p>
                                    <p><strong>Titre :</strong> {{ contrat.titre_stage }}</p>
                                    <p><strong>Créé le :</strong> {{ contrat.date_creation|date:"d/m/Y à H:i" }}</p>
                                    <p><strong>Créé par :</strong> {{ contrat.cree_par.get_full_name }}</p>
                                    {% if contrat.date_expiration %}
                                        <p><strong>Expire le :</strong> 
                                            <span class="{% if contrat.est_expire %}text-danger{% else %}text-success{% endif %}">
                                                {{ contrat.date_expiration|date:"d/m/Y" }}
                                                {% if contrat.est_expire %}<i class="fas fa-exclamation-triangle ms-1"></i>{% endif %}
                                            </span>
                                        </p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Stagiaire</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Nom :</strong> {{ contrat.stagiaire.nom_complet }}</p>
                                    <p><strong>Email :</strong> {{ contrat.stagiaire.email }}</p>
                                    <p><strong>Téléphone :</strong> {{ contrat.stagiaire.telephone|default:"Non renseigné" }}</p>
                                    <p><strong>Département :</strong> {{ contrat.stagiaire.get_departement_display }}</p>
                                    <p><strong>Période :</strong> {{ contrat.stagiaire.date_debut|date:"d/m/Y" }} - {{ contrat.stagiaire.date_fin|date:"d/m/Y" }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Détails du stage -->
                    <div class="card border-warning mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-briefcase me-2"></i>Détails du stage</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-tasks me-1"></i>Missions</h6>
                                    <p class="text-muted">{{ contrat.description_missions|linebreaks }}</p>
                                    
                                    <h6><i class="fas fa-graduation-cap me-1"></i>Objectifs pédagogiques</h6>
                                    <p class="text-muted">{{ contrat.objectifs_pedagogiques|linebreaks }}</p>
                                </div>
                                <div class="col-md-6">
                                    {% if contrat.competences_acquises %}
                                        <h6><i class="fas fa-lightbulb me-1"></i>Compétences à acquérir</h6>
                                        <p class="text-muted">{{ contrat.competences_acquises|linebreaks }}</p>
                                    {% endif %}
                                    
                                    <h6><i class="fas fa-info me-1"></i>Conditions</h6>
                                    <ul class="list-unstyled text-muted">
                                        <li><i class="fas fa-clock me-1"></i>{{ contrat.duree_hebdomadaire }}h/semaine</li>
                                        <li><i class="fas fa-euro-sign me-1"></i>{{ contrat.gratification_mensuelle|floatformat:2 }}€/mois</li>
                                        {% if contrat.avantages %}
                                            <li><i class="fas fa-gift me-1"></i>{{ contrat.avantages }}</li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Encadrement -->
                    <div class="card border-secondary mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0"><i class="fas fa-users me-2"></i>Encadrement</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-user-tie me-1"></i>Encadrant entreprise</h6>
                                    {% if contrat.encadrant_entreprise %}
                                        <p class="text-muted">
                                            {{ contrat.encadrant_entreprise.get_full_name }}<br>
                                            <small>{{ contrat.encadrant_entreprise.email }}</small>
                                        </p>
                                    {% else %}
                                        <p class="text-muted">Non assigné</p>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-chalkboard-teacher me-1"></i>Tuteur pédagogique</h6>
                                    <p class="text-muted">{{ contrat.tuteur_pedagogique|default:"Non renseigné" }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Signature RH -->
                    <div class="card border-dark mb-4">
                        <div class="card-header bg-dark text-white">
                            <h6 class="mb-0"><i class="fas fa-signature me-2"></i>Signature électronique RH</h6>
                        </div>
                        <div class="card-body">
                            <div class="row justify-content-center">
                                <div class="col-md-6">
                                    <div class="text-center">
                                        <i class="fas fa-building fa-3x {% if contrat.signature_rh %}text-success{% else %}text-muted{% endif %} mb-3"></i>
                                        <h5>Responsable RH</h5>
                                        {% if contrat.signature_rh %}
                                            <span class="badge bg-success fs-6 mb-2">✓ Contrat signé</span>
                                            <br><small class="text-muted">{{ contrat.date_signature_rh|date:"d/m/Y à H:i" }}</small>
                                            {% if contrat.signature_rh_par %}
                                                <br><small class="text-muted"><strong>Signé par:</strong> {{ contrat.signature_rh_par.get_full_name }}</small>
                                                <br><small class="text-muted">{{ contrat.signature_rh_par.email }}</small>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-warning fs-6 mb-2">⏳ En attente de signature</span>
                                            <br><small class="text-muted">Le contrat doit être signé par un responsable RH</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Commentaires -->
                    {% if contrat.commentaires_admin or contrat.notes_internes %}
                        <div class="card border-info mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-comment me-2"></i>Commentaires</h6>
                            </div>
                            <div class="card-body">
                                {% if contrat.commentaires_admin %}
                                    <h6>Commentaires administratifs</h6>
                                    <p class="text-muted">{{ contrat.commentaires_admin|linebreaks }}</p>
                                {% endif %}
                                {% if contrat.notes_internes %}
                                    <h6>Notes internes</h6>
                                    <p class="text-muted">{{ contrat.notes_internes|linebreaks }}</p>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}

                    <!-- Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'contracts' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour aux contrats
                        </a>
                        <div>
                            {% if user.role == 'RH' or user.role == 'ADMIN' %}
                                {% if contrat.statut != 'ENTIEREMENT_SIGNE' %}
                                    <a href="#" class="btn btn-warning me-2" onclick="editContract()">
                                        <i class="fas fa-edit me-1"></i>Modifier
                                    </a>
                                {% endif %}
                                {% if not contrat.signature_rh %}
                                    <button class="btn btn-success me-2" onclick="signContract()">
                                        <i class="fas fa-signature me-1"></i>Signer (RH)
                                    </button>
                                {% endif %}
                            {% endif %}
                            <button class="btn btn-primary" onclick="downloadContract()">
                                <i class="fas fa-download me-1"></i>Télécharger PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function editContract() {
    if (confirm('Modifier ce contrat ?')) {
        // Redirection vers la page de modification
        window.location.href = '#'; // À implémenter
    }
}

function signContract() {
    if (confirm('Êtes-vous sûr de vouloir signer ce contrat en tant que RH ?\n\nCette action est irréversible.')) {
        // Désactiver le bouton pendant la signature
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Signature en cours...';

        // Envoyer la requête de signature
        fetch(`/contrats/{{ contrat.id }}/sign/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                location.reload();
            } else {
                alert('❌ Erreur: ' + data.error);
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('❌ Erreur de connexion lors de la signature');
            btn.disabled = false;
            btn.innerHTML = originalText;
        });
    }
}

function downloadContract() {
    // Logique de téléchargement à implémenter
    alert('Téléchargement du contrat en cours...');
}
</script>
{% endblock %}
