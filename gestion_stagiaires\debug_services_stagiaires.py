#!/usr/bin/env python
"""
Debug des services et stagiaires
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service

User = get_user_model()

def debug_services_stagiaires():
    """Debug des services et stagiaires"""
    
    print("=== DEBUG SERVICES ET STAGIAIRES ===")
    
    # 1. Encadrant et son service
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    print(f"✅ Encadrant: {encadrant.get_full_name()}")
    print(f"📋 Service de l'encadrant: {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    # 2. Tous les stagiaires de cet encadrant
    print(f"\n📋 TOUS LES STAGIAIRES DE {encadrant.get_full_name()}:")
    
    stagiaires_encadrant = Stagiaire.objects.filter(encadrant=encadrant)
    
    for stagiaire in stagiaires_encadrant:
        print(f"\n   👤 {stagiaire.nom_complet}")
        print(f"      Service stagiaire: {stagiaire.service.nom if stagiaire.service else '❌ AUCUN SERVICE'}")
        print(f"      Service encadrant: {encadrant.service.nom if encadrant.service else 'Aucun'}")
        print(f"      Dates: {stagiaire.date_debut} → {stagiaire.date_fin}")
        
        # Vérifier si le service correspond
        if stagiaire.service == encadrant.service:
            print(f"      ✅ MÊME SERVICE - Sera affiché")
        elif stagiaire.service is None:
            print(f"      ⚠️ STAGIAIRE SANS SERVICE - Ne sera pas affiché")
        else:
            print(f"      ❌ SERVICE DIFFÉRENT - Ne sera pas affiché")
    
    # 3. Proposer des corrections
    print(f"\n🔧 CORRECTIONS SUGGÉRÉES:")
    
    stagiaires_sans_service = stagiaires_encadrant.filter(service__isnull=True)
    stagiaires_autre_service = stagiaires_encadrant.exclude(service=encadrant.service).exclude(service__isnull=True)
    
    if stagiaires_sans_service.exists():
        print(f"\n   📝 Stagiaires sans service à corriger:")
        for stagiaire in stagiaires_sans_service:
            print(f"      • {stagiaire.nom_complet} → Assigner au service {encadrant.service.nom}")
    
    if stagiaires_autre_service.exists():
        print(f"\n   📝 Stagiaires avec service différent:")
        for stagiaire in stagiaires_autre_service:
            print(f"      • {stagiaire.nom_complet} (Service: {stagiaire.service.nom}) → Changer vers {encadrant.service.nom} ?")
    
    # 4. Correction automatique (optionnelle)
    print(f"\n🔄 VOULEZ-VOUS CORRIGER AUTOMATIQUEMENT ?")
    print(f"   Cela assignera le service '{encadrant.service.nom}' à tous les stagiaires de {encadrant.get_full_name()}")
    
    return stagiaires_encadrant, encadrant

def corriger_services_stagiaires():
    """Corriger les services des stagiaires"""
    
    stagiaires_encadrant, encadrant = debug_services_stagiaires()
    
    print(f"\n🔧 CORRECTION DES SERVICES:")
    
    corrections = 0
    for stagiaire in stagiaires_encadrant:
        if stagiaire.service != encadrant.service:
            ancien_service = stagiaire.service.nom if stagiaire.service else "Aucun"
            stagiaire.service = encadrant.service
            stagiaire.save()
            corrections += 1
            print(f"   ✅ {stagiaire.nom_complet}: {ancien_service} → {encadrant.service.nom}")
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Corrections effectuées: {corrections}")
    print(f"   Tous les stagiaires de {encadrant.get_full_name()} sont maintenant dans le service {encadrant.service.nom}")
    
    # Vérification
    print(f"\n🔍 VÉRIFICATION:")
    stagiaires_corriges = Stagiaire.objects.filter(
        encadrant=encadrant,
        service=encadrant.service
    )
    print(f"   Stagiaires dans le bon service: {stagiaires_corriges.count()}")
    
    return corrections

if __name__ == '__main__':
    # D'abord debug
    stagiaires_encadrant, encadrant = debug_services_stagiaires()
    
    # Demander si on veut corriger
    print(f"\n" + "="*60)
    reponse = input("Voulez-vous corriger automatiquement les services ? (oui/non): ").lower().strip()
    
    if reponse in ['oui', 'o', 'yes', 'y']:
        corrections = corriger_services_stagiaires()
        print(f"\n🎉 {corrections} corrections effectuées !")
        print(f"Maintenant, testez le calendrier - les stagiaires devraient s'afficher.")
    else:
        print(f"\n❌ Aucune correction effectuée.")
        print(f"Les stagiaires ne s'afficheront pas tant qu'ils ne sont pas dans le bon service.")
