#!/usr/bin/env python
"""
Script pour diagnostiquer le problème réel d'ajout de stagiaires dans l'admin
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service
from django.test import Client
from datetime import date, timedelta
import json

User = get_user_model()

def debug_real_admin():
    """Diagnostic en temps réel de l'interface d'administration"""
    
    print("=== Diagnostic en temps réel de l'interface d'administration ===")
    
    # Configuration du client de test avec les bons paramètres
    client = Client()
    
    # Récupérer un admin
    admin_user = User.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ Aucun administrateur trouvé")
        return
    
    print(f"✅ Admin trouvé: {admin_user.username}")
    
    # Se connecter
    client.force_login(admin_user)
    print("✅ Connexion réussie")
    
    # Test 1: Accès à la page d'ajout
    print(f"\n📋 Test 1: Accès à la page d'ajout")
    try:
        response = client.get('/admin/stagiaires/stagiaire/add/')
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Page d'ajout accessible")
            
            # Analyser le contenu de la page
            content = response.content.decode('utf-8')
            
            # Vérifier la présence des champs obligatoires
            required_fields = ['nom', 'prenom', 'email', 'date_naissance', 'departement', 'date_debut', 'date_fin', 'etablissement', 'niveau_etude', 'specialite']
            
            missing_fields = []
            for field in required_fields:
                if f'name="{field}"' not in content and f'id="id_{field}"' not in content:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"   ⚠️ Champs manquants: {missing_fields}")
            else:
                print("   ✅ Tous les champs obligatoires sont présents")
            
            # Vérifier la présence du token CSRF
            if 'csrfmiddlewaretoken' in content:
                print("   ✅ Token CSRF présent")
            else:
                print("   ❌ Token CSRF manquant")
                
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
            return
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return
    
    # Test 2: Soumission d'un formulaire minimal
    print(f"\n💾 Test 2: Soumission d'un formulaire minimal")
    
    # Récupérer un encadrant
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    if not encadrant:
        print("   ⚠️ Aucun encadrant trouvé")
        encadrant_id = ''
        service_id = ''
    else:
        encadrant_id = encadrant.id
        service_id = encadrant.service.id if encadrant.service else ''
        print(f"   ✅ Encadrant: {encadrant.username}")
    
    # Email unique pour ce test
    test_email = f"test.real.admin.{date.today().strftime('%Y%m%d')}.{admin_user.id}@example.com"
    
    # Supprimer le stagiaire de test s'il existe
    Stagiaire.objects.filter(email=test_email).delete()
    
    # Données minimales du formulaire
    form_data = {
        'nom': 'TestRealAdmin',
        'prenom': 'Stagiaire',
        'email': test_email,
        'telephone': '',
        'date_naissance': '2000-01-01',
        'departement': 'IT',
        'service': service_id,
        'encadrant': encadrant_id,
        'date_debut': date.today().strftime('%Y-%m-%d'),
        'date_fin': (date.today() + timedelta(days=90)).strftime('%Y-%m-%d'),
        'statut': 'EN_COURS',
        'etablissement': 'Université Test Real',
        'niveau_etude': 'Master',
        'specialite': 'Informatique',
        'technologies': '',
        'thematique': '',
        'sujet': '',
        'duree_estimee': '0',
        'description_taches': '',
        'statut_taches': 'NON_COMMENCEES',
        'statut_convention': 'EN_ATTENTE',
        'commentaire_convention': '',
        'evaluation_encadrant': '',
        'note_finale': '',
        'cv': '',
        'assurance': '',
        'convention_stage': '',
        'date_validation_convention': '',
        'validee_par': '',
        'attestation_fin_stage': '',
        'date_generation_attestation': '',
        'attestation_generee_par': '',
        # Inlines vides
        'tache_set-TOTAL_FORMS': '0',
        'tache_set-INITIAL_FORMS': '0',
        'tache_set-MIN_NUM_FORMS': '0',
        'tache_set-MAX_NUM_FORMS': '1000',
        'mission_set-TOTAL_FORMS': '0',
        'mission_set-INITIAL_FORMS': '0',
        'mission_set-MIN_NUM_FORMS': '0',
        'mission_set-MAX_NUM_FORMS': '1000',
        'rapportstage_set-TOTAL_FORMS': '0',
        'rapportstage_set-INITIAL_FORMS': '0',
        'rapportstage_set-MIN_NUM_FORMS': '0',
        'rapportstage_set-MAX_NUM_FORMS': '1000',
        '_save': 'Enregistrer',
    }
    
    try:
        # Récupérer d'abord la page pour obtenir le token CSRF
        get_response = client.get('/admin/stagiaires/stagiaire/add/')
        
        # Soumettre le formulaire
        response = client.post('/admin/stagiaires/stagiaire/add/', data=form_data, follow=True)
        
        print(f"   Status final: {response.status_code}")
        print(f"   URL finale: {response.request['PATH_INFO']}")
        
        if response.status_code == 200:
            # Analyser la réponse
            content = response.content.decode('utf-8')
            
            # Vérifier si on est sur la page de liste (succès)
            if '/admin/stagiaires/stagiaire/' in response.request['PATH_INFO'] and 'add' not in response.request['PATH_INFO']:
                print("   ✅ Redirection vers la liste des stagiaires (succès probable)")
                
                # Vérifier que le stagiaire a été créé
                stagiaire_cree = Stagiaire.objects.filter(email=test_email).first()
                if stagiaire_cree:
                    print(f"   ✅ Stagiaire créé avec succès: {stagiaire_cree.nom_complet}")
                    print(f"      ID: {stagiaire_cree.id}")
                    print(f"      Email: {stagiaire_cree.email}")
                    print(f"      Créé par: {stagiaire_cree.cree_par}")
                    
                    # Nettoyer
                    stagiaire_cree.delete()
                    print("   🧹 Stagiaire de test supprimé")
                else:
                    print("   ❌ Stagiaire non trouvé en base malgré la redirection")
            
            # Vérifier s'il y a des erreurs dans le formulaire
            elif 'errorlist' in content:
                print("   ❌ Erreurs dans le formulaire:")
                
                # Extraire les erreurs
                import re
                errors = re.findall(r'<ul class="errorlist[^>]*">(.*?)</ul>', content, re.DOTALL)
                for i, error in enumerate(errors[:5]):
                    clean_error = re.sub(r'<[^>]+>', '', error).strip()
                    if clean_error:
                        print(f"      {i+1}. {clean_error}")
                
                # Extraire les champs avec erreurs
                field_errors = re.findall(r'<div class="form-row[^>]*errors[^>]*">.*?<label[^>]*>([^<]+)</label>', content, re.DOTALL)
                if field_errors:
                    print("   📋 Champs avec erreurs:")
                    for field in field_errors[:5]:
                        print(f"      • {field.strip()}")
            
            else:
                print("   ❓ Réponse inattendue - pas d'erreurs visibles mais pas de redirection")
                
        else:
            print(f"   ❌ Erreur HTTP: {response.status_code}")
    
    except Exception as e:
        print(f"   ❌ Erreur lors de la soumission: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Vérifier les logs Django
    print(f"\n📊 Test 3: État de la base de données")
    
    try:
        total_stagiaires = Stagiaire.objects.count()
        derniers_stagiaires = Stagiaire.objects.order_by('-date_creation')[:3]
        
        print(f"   Total stagiaires: {total_stagiaires}")
        print("   Derniers stagiaires créés:")
        for stagiaire in derniers_stagiaires:
            print(f"      • {stagiaire.nom_complet} - {stagiaire.date_creation.strftime('%Y-%m-%d %H:%M')}")
    
    except Exception as e:
        print(f"   ❌ Erreur d'accès à la base: {e}")
    
    # Test 4: Vérifier les permissions
    print(f"\n🔐 Test 4: Vérification des permissions")
    
    try:
        from stagiaires.admin import StagiaireAdmin
        from django.contrib import admin
        from django.test import RequestFactory
        
        stagiaire_admin = StagiaireAdmin(Stagiaire, admin.site)
        factory = RequestFactory()
        request = factory.get('/admin/stagiaires/stagiaire/add/')
        request.user = admin_user
        
        has_add = stagiaire_admin.has_add_permission(request)
        has_change = stagiaire_admin.has_change_permission(request)
        has_delete = stagiaire_admin.has_delete_permission(request)
        
        print(f"   Permission d'ajout: {'✅' if has_add else '❌'}")
        print(f"   Permission de modification: {'✅' if has_change else '❌'}")
        print(f"   Permission de suppression: {'✅' if has_delete else '❌'}")
        
        if not has_add:
            print("   ❌ PROBLÈME: L'utilisateur n'a pas la permission d'ajouter des stagiaires!")
    
    except Exception as e:
        print(f"   ❌ Erreur de vérification des permissions: {e}")
    
    print(f"\n=== Diagnostic terminé ===")
    
    print(f"\n💡 Actions recommandées:")
    print(f"   1. Vérifiez les erreurs spécifiques affichées ci-dessus")
    print(f"   2. Testez manuellement avec un email unique")
    print(f"   3. Vérifiez que tous les champs obligatoires sont remplis")
    print(f"   4. Consultez les logs du serveur Django")

if __name__ == '__main__':
    debug_real_admin()
