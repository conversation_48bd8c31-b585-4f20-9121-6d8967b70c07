{% extends 'stagiaires/base.html' %}

{% block title %}Connexion - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="col-md-6 col-lg-4">
        <div class="auth-card">
            <div class="auth-header">
                <h2 class="mb-0">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Connexion
                </h2>
                <p class="mb-0 mt-2 opacity-75">Accédez à votre espace de gestion des stagiaires</p>
            </div>
            <div class="auth-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-user me-1"></i>Nom d'utilisateur
                        </label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.username.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-4">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <i class="fas fa-lock me-1"></i>Mot de passe
                        </label>
                        {{ form.password }}
                        {% if form.password.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.password.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid mb-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                        </button>
                    </div>

                    <div class="text-center mb-3">
                        <p class="mb-2 text-muted">Pas encore de compte ?</p>
                        <a href="{% url 'register' %}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-user-plus me-1"></i>Créer un compte
                        </a>
                    </div>

                    <div class="text-center">
                        <a href="{% url 'home' %}" class="text-muted text-decoration-none">
                            <i class="fas fa-home me-1"></i>Retour à l'accueil
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

