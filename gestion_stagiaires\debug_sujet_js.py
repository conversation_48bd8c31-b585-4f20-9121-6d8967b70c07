#!/usr/bin/env python
"""
Debug du sujet "js" qui s'affiche incorrectement
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Sujet

User = get_user_model()

def debug_sujet_js():
    """Debug du sujet js"""
    
    print("=== DEBUG SUJET JS ===")
    
    # Trouver le sujet "js"
    sujet_js = Sujet.objects.filter(titre__icontains='js').first()
    
    if not sujet_js:
        print("❌ Sujet 'js' non trouvé")
        return
    
    print(f"✅ Sujet trouvé: {sujet_js.titre}")
    print(f"   ID: {sujet_js.id}")
    print(f"   Service: {sujet_js.service.nom if sujet_js.service else 'Aucun'}")
    print(f"   Thématique: {sujet_js.thematique.titre if sujet_js.thematique else 'Aucune'}")
    print(f"   Encadrant: {sujet_js.encadrant.get_full_name() if sujet_js.encadrant else 'Aucun'}")
    
    if hasattr(sujet_js, 'cree_par'):
        print(f"   Créé par: {sujet_js.cree_par.get_full_name() if sujet_js.cree_par else 'Aucun'}")
    
    # Vérifier l'encadrant Marketing
    encadrant_marketing = User.objects.filter(
        role='ENCADRANT',
        service__nom__icontains='marketing'
    ).first()
    
    if encadrant_marketing:
        print(f"\n👨‍💼 Encadrant Marketing: {encadrant_marketing.get_full_name()}")
        print(f"   Service: {encadrant_marketing.service.nom}")
        
        # Vérifier pourquoi ce sujet s'affiche pour cet encadrant
        print(f"\n🔍 ANALYSE FILTRAGE:")
        
        # Test 1: Service
        if sujet_js.service == encadrant_marketing.service:
            print(f"   ❌ Même service: {sujet_js.service.nom} = {encadrant_marketing.service.nom}")
        else:
            print(f"   ✅ Services différents: {sujet_js.service.nom if sujet_js.service else 'Aucun'} ≠ {encadrant_marketing.service.nom}")
        
        # Test 2: Encadrant
        if sujet_js.encadrant == encadrant_marketing:
            print(f"   ❌ Même encadrant: {sujet_js.encadrant.get_full_name()}")
        else:
            print(f"   ✅ Encadrants différents: {sujet_js.encadrant.get_full_name() if sujet_js.encadrant else 'Aucun'} ≠ {encadrant_marketing.get_full_name()}")
        
        # Test 3: Créé par
        if hasattr(sujet_js, 'cree_par') and sujet_js.cree_par == encadrant_marketing:
            print(f"   ❌ Créé par l'encadrant: {sujet_js.cree_par.get_full_name()}")
        else:
            print(f"   ✅ Pas créé par l'encadrant")
        
        # Test de la requête exacte
        print(f"\n🧪 TEST REQUÊTE EXACTE:")
        
        sujets_encadrant = Sujet.objects.filter(service=encadrant_marketing.service)
        print(f"   Sujets du service Marketing: {sujets_encadrant.count()}")
        
        if sujet_js in sujets_encadrant:
            print(f"   ❌ Sujet 'js' inclus dans les sujets du service Marketing")
            
            # Corriger le service du sujet
            print(f"\n🔧 CORRECTION:")
            print(f"   Ancien service: {sujet_js.service.nom}")
            
            # Le sujet "js" devrait être dans le service Informatique
            service_informatique = sujet_js.service  # Il est déjà dans Informatique selon les tests
            print(f"   Service actuel: {service_informatique.nom}")
            
            # Vérifier s'il y a une incohérence
            if service_informatique.nom.lower() != 'informatique':
                print(f"   ⚠️ Problème détecté: Le service n'est pas Informatique")
        else:
            print(f"   ✅ Sujet 'js' pas inclus dans les sujets du service Marketing")

if __name__ == '__main__':
    debug_sujet_js()
