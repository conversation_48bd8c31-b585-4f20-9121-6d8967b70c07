{% extends 'stagiaires/base.html' %}

{% block title %}Modifier un utilisateur - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        {{ title }}
                    </h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user me-1"></i>Prénom
                                </label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.first_name.errors }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user me-1"></i>Nom
                                </label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.last_name.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="fas fa-at me-1"></i>Nom d'utilisateur
                            </label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.username.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                <i class="fas fa-envelope me-1"></i>Adresse email
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.email.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.role.id_for_label }}" class="form-label">
                                <i class="fas fa-briefcase me-1"></i>Rôle
                            </label>
                            {{ form.role }}
                            {% if form.role.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.role.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3" id="service-field">
                            <label for="{{ form.service.id_for_label }}" class="form-label">
                                <i class="fas fa-building me-1"></i>Service
                            </label>
                            {{ form.service }}
                            <div class="form-text">Obligatoire pour les encadrants</div>
                            {% if form.service.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.service.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_admin }}
                                <label class="form-check-label" for="{{ form.is_admin.id_for_label }}">
                                    <i class="fas fa-shield-alt me-1 text-danger"></i>
                                    <strong>{{ form.is_admin.label }}</strong>
                                </label>
                            </div>
                            <div class="form-text">{{ form.is_admin.help_text }}</div>
                            {% if form.is_admin.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.is_admin.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    <i class="fas fa-user-check me-1 text-success"></i>
                                    <strong>Compte actif</strong>
                                </label>
                            </div>
                            <div class="form-text">Décocher pour désactiver le compte utilisateur</div>
                            {% if form.is_active.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.is_active.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Informations sur l'utilisateur -->
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>
                                Informations actuelles
                            </h6>
                            <p class="mb-1"><strong>Utilisateur :</strong> {{ user_to_edit.get_full_name }}</p>
                            <p class="mb-1"><strong>Rôle actuel :</strong> {{ user_to_edit.get_role_display }}</p>
                            <p class="mb-1"><strong>Statut admin :</strong> 
                                {% if user_to_edit.is_superuser %}
                                    <span class="badge bg-danger">Administrateur</span>
                                {% else %}
                                    <span class="badge bg-secondary">Utilisateur standard</span>
                                {% endif %}
                            </p>
                            <p class="mb-0"><strong>Compte :</strong> 
                                {% if user_to_edit.is_active %}
                                    <span class="badge bg-success">Actif</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactif</span>
                                {% endif %}
                            </p>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'user_management' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left me-1"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function toggleServiceField(role) {
        const serviceField = document.getElementById('service-field');
        const serviceSelect = document.getElementById('id_service');
        
        if (role === 'ENCADRANT') {
            serviceField.style.display = 'block';
            serviceSelect.required = true;
        } else {
            serviceField.style.display = 'block'; // Toujours visible en édition
            serviceSelect.required = false;
        }
    }
    
    // Exécuter au chargement de la page
    document.addEventListener('DOMContentLoaded', function() {
        const roleSelect = document.getElementById('id_role');
        if (roleSelect) {
            toggleServiceField(roleSelect.value);
            roleSelect.addEventListener('change', function() {
                toggleServiceField(this.value);
            });
        }
    });
</script>
{% endblock %}
