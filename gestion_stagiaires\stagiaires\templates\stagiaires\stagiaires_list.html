{% extends 'stagiaires/base.html' %}

{% block title %}Liste des Stagiaires - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title mb-0">
                            <i class="fas fa-users me-2"></i>
                            Liste des Stagiaires
                        </h3>
                        {% if is_encadrant %}
                        <small class="text-light">
                            Service : {{ stats.service_nom }} |
                            {% if filtre_actuel == 'mon_service' %}
                                Mes stagiaires ({{ stats.mes_stagiaires }})
                            {% else %}
                                Tous les stagiaires ({{ stats.total_stagiaires }})
                            {% endif %}
                        </small>
                        {% endif %}
                    </div>
                    <a href="{% url 'dashboard' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Retour au tableau de bord
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filtres pour les encadrants -->
                    {% if is_encadrant %}
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0 text-primary">
                                                <i class="fas fa-filter me-2"></i>Filtres d'affichage
                                            </h6>
                                        </div>
                                        <div class="btn-group" role="group">
                                            <a href="?filtre=tous" class="btn btn-sm {% if filtre_actuel == 'tous' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                                                <i class="fas fa-globe me-1"></i>
                                                Tous les stagiaires ({{ stats.total_stagiaires }})
                                            </a>
                                            <a href="?filtre=mon_service" class="btn btn-sm {% if filtre_actuel == 'mon_service' %}btn-success{% else %}btn-outline-success{% endif %}">
                                                <i class="fas fa-users me-1"></i>
                                                Mon service ({{ stats.mes_stagiaires }})
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="Rechercher un stagiaire..." id="searchInput">
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            {% if user.role == 'ADMIN' or user.role == 'RH' %}
                            <a href="{% url 'add_stagiaire' %}" class="btn btn-success">
                                <i class="fas fa-plus me-1"></i>Ajouter un stagiaire
                            </a>
                            {% endif %}
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><i class="fas fa-user me-1"></i>Nom</th>
                                    <th><i class="fas fa-envelope me-1"></i>Email</th>
                                    <th><i class="fas fa-building me-1"></i>Département</th>
                                    <th><i class="fas fa-user-tie me-1"></i>Encadrant</th>
                                    <th><i class="fas fa-calendar me-1"></i>Période</th>
                                    <th><i class="fas fa-info-circle me-1"></i>Statut</th>
                                    <th><i class="fas fa-cogs me-1"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if stagiaires %}
                                    {% for stagiaire in stagiaires %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle bg-primary text-white me-2">
                                                    {{ stagiaire.prenom.0 }}{{ stagiaire.nom.0 }}
                                                </div>
                                                <div>
                                                    <strong>{{ stagiaire.nom_complet }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ stagiaire.specialite }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ stagiaire.email }}</td>
                                        <td>
                                            <span class="badge bg-secondary">{{ stagiaire.get_departement_display }}</span>
                                        </td>
                                        <td>
                                            {% if stagiaire.encadrant %}
                                                {{ stagiaire.encadrant.get_full_name|default:stagiaire.encadrant.username }}
                                            {% else %}
                                                <span class="text-muted">Non assigné</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small>{{ stagiaire.date_debut|date:"d/m/Y" }} - {{ stagiaire.date_fin|date:"d/m/Y" }}</small>
                                            <br>
                                            <span class="badge bg-info">{{ stagiaire.duree_stage }} jours</span>
                                        </td>
                                        <td>
                                            {% if stagiaire.statut == 'EN_COURS' %}
                                                <span class="badge bg-success">{{ stagiaire.get_statut_display }}</span>
                                            {% elif stagiaire.statut == 'TERMINE' %}
                                                <span class="badge bg-secondary">{{ stagiaire.get_statut_display }}</span>
                                            {% elif stagiaire.statut == 'SUSPENDU' %}
                                                <span class="badge bg-warning">{{ stagiaire.get_statut_display }}</span>
                                            {% else %}
                                                <span class="badge bg-danger">{{ stagiaire.get_statut_display }}</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-end">
                                            <div class="btn-group">
                                                <a href="{% url 'stagiaire_detail' stagiaire.id %}" class="btn btn-sm btn-info" title="Voir le détail">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'edit_stagiaire' stagiaire.id %}" class="btn btn-sm btn-warning" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                {% if user.role == 'ENCADRANT' %}
                                                    {% comment %} Vérifier si l'encadrant peut accéder à ce stagiaire {% endcomment %}
                                                    {% if stagiaire.encadrant == user or stagiaire.service == user.service %}
                                                    <a href="{% url 'rencontre_stagiaire' stagiaire.id %}" class="btn btn-sm btn-success" title="Rencontre - Ajouter des tâches">
                                                        <i class="fas fa-handshake"></i>
                                                    </a>
                                                    {% endif %}
                                                {% endif %}
                                                {% if user.role == 'ADMIN' %}
                                                <button type="button" class="btn btn-sm btn-danger" onclick="confirmDelete({{ stagiaire.id }}, '{{ stagiaire.nom }} {{ stagiaire.prenom }}')" title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <h5>Aucun stagiaire trouvé</h5>
                                                <p>Commencez par ajouter votre premier stagiaire.</p>
                                                <a href="{% url 'add_stagiaire' %}" class="btn btn-success">
                                                    <i class="fas fa-plus me-1"></i>Ajouter un stagiaire
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    {% if stagiaires %}
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <small class="text-muted">
                                Total : {{ stagiaires|length }} stagiaire{{ stagiaires|length|pluralize }}
                            </small>
                        </div>
                        <div>
                            <a href="{% url 'add_stagiaire' %}" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>Ajouter un stagiaire
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}
</style>

<script>
// Fonction pour obtenir le token CSRF
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Fonction pour afficher les alertes
function showAlert(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insérer l'alerte en haut de la page
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    // Supprimer l'alerte après 5 secondes
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Fonction pour supprimer un stagiaire
function deleteStagiaire(stagiaireId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce stagiaire ? Cette action supprimera également toutes ses tâches, missions et rapports. Cette action est irréversible.')) {

        // Préparer les données
        const csrftoken = getCookie('csrftoken');

        // Envoyer la requête AJAX
        fetch(`/stagiaires/${stagiaireId}/delete/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrftoken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                // Recharger la page après un court délai
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showAlert(data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            showAlert('Une erreur est survenue lors de la suppression.', 'danger');
        });
    }
}

// SOLUTION POUR LE PROBLÈME D'AJOUT DE STAGIAIRES
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 Vérification des stagiaires ajoutés...');

    // Vérifier s'il y a un message de succès d'ajout
    const successMessages = document.querySelectorAll('.alert-success');
    let hasAddSuccess = false;

    successMessages.forEach(function(message) {
        const text = message.textContent || message.innerText;
        if (text.includes('ajouté avec succès') || text.includes('SUCCÈS')) {
            hasAddSuccess = true;
            console.log('✅ Message de succès d\'ajout détecté');
        }
    });

    // Forcer le rafraîchissement si nécessaire
    if (hasAddSuccess) {
        console.log('🔄 Rafraîchissement forcé dans 2 secondes...');

        setTimeout(function() {
            console.log('🔄 Rafraîchissement en cours...');

            // Vider le cache du navigateur
            if ('caches' in window) {
                caches.keys().then(function(names) {
                    names.forEach(function(name) {
                        caches.delete(name);
                    });
                });
            }

            // Rafraîchissement forcé avec cache bypass
            window.location.reload(true);
        }, 2000);
    }

    // Ajouter un compteur visible dans le titre
    const tableBody = document.querySelector('tbody');
    if (tableBody) {
        const rows = tableBody.querySelectorAll('tr');
        const count = rows.length;
        console.log(`📊 ${count} stagiaires trouvés dans le tableau`);

        // Mettre à jour le titre avec le compteur
        const title = document.querySelector('h2');
        if (title && !title.textContent.includes('(')) {
            title.innerHTML += ` <span class="badge bg-info ms-2">${count} en cours</span>`;
        }
    }

    // Vérifier périodiquement s'il y a de nouveaux stagiaires
    let lastCount = tableBody ? tableBody.querySelectorAll('tr').length : 0;

    setInterval(function() {
        const currentTableBody = document.querySelector('tbody');
        if (currentTableBody) {
            const currentCount = currentTableBody.querySelectorAll('tr').length;
            if (currentCount !== lastCount) {
                console.log(`📊 Changement détecté: ${lastCount} → ${currentCount} stagiaires`);
                lastCount = currentCount;

                // Mettre à jour le badge
                const badge = document.querySelector('.badge.bg-info');
                if (badge) {
                    badge.textContent = `${currentCount} en cours`;
                }
            }
        }
    }, 3000); // Vérifier toutes les 3 secondes

    // FONCTIONNALITÉ DE RECHERCHE EN TEMPS RÉEL
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('tbody tr');
            let visibleCount = 0;

            tableRows.forEach(function(row) {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            // Mettre à jour le compteur de résultats
            const badge = document.querySelector('.badge.bg-info');
            if (badge) {
                if (searchTerm) {
                    badge.textContent = `${visibleCount} trouvé${visibleCount > 1 ? 's' : ''}`;
                    badge.className = 'badge bg-warning ms-2';
                } else {
                    badge.textContent = `${tableRows.length} en cours`;
                    badge.className = 'badge bg-info ms-2';
                }
            }

            // Afficher un message si aucun résultat
            let noResultsRow = document.getElementById('no-results-row');
            if (visibleCount === 0 && searchTerm) {
                if (!noResultsRow) {
                    noResultsRow = document.createElement('tr');
                    noResultsRow.id = 'no-results-row';
                    noResultsRow.innerHTML = `
                        <td colspan="7" class="text-center text-muted py-4">
                            <i class="fas fa-search fa-2x mb-2"></i><br>
                            Aucun stagiaire trouvé pour "${searchTerm}"
                        </td>
                    `;
                    document.querySelector('tbody').appendChild(noResultsRow);
                }
            } else if (noResultsRow) {
                noResultsRow.remove();
            }
        });

        // Effacer la recherche avec Escape
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                this.value = '';
                this.dispatchEvent(new Event('input'));
            }
        });
    }
});
</script>

{% block extra_js %}
<script>
function confirmDelete(id, name) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le stagiaire "${name}" ?`)) {
        // Créer un formulaire pour envoyer une requête POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = "{% url 'delete_stagiaire' 0 %}".replace('0', id);
        
        // Ajouter le token CSRF
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = '{{ csrf_token }}';
        form.appendChild(csrfToken);
        
        // Ajouter le formulaire au document et le soumettre
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}

{% endblock %}


