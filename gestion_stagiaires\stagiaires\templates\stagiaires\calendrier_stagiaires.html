{% extends 'stagiaires/base.html' %}
{% load dict_extras %}

{% block title %}Calendrier des Stagiaires{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-0">
                <i class="fas fa-calendar-alt me-2"></i>Calendrier des Stagiaires
            </h2>
            <p class="text-muted">Visualisez les périodes de stage de tous les stagiaires</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'stagiaires_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-list me-1"></i>Liste des stagiaires
            </a>
        </div>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filtres</h5>
        </div>
        <div class="card-body">
            <form method="get" action="{% url 'calendrier_stagiaires' %}" class="row g-3">
                <div class="col-md-3">
                    <label for="vue" class="form-label">Vue</label>
                    <select name="vue" id="vue" class="form-select">
                        <option value="tous" {% if vue == 'tous' %}selected{% endif %}>Tous les stagiaires</option>
                        <option value="service" {% if vue == 'service' %}selected{% endif %}>Par service</option>
                        <option value="encadrant" {% if vue == 'encadrant' %}selected{% endif %}>Par encadrant</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="service" class="form-label">Service</label>
                    <select name="service" id="service" class="form-select">
                        <option value="">Tous les services</option>
                        {% for service in services %}
                        <option value="{{ service.id }}" {% if selected_service == service.id %}selected{% endif %}>{{ service.nom }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="encadrant" class="form-label">Encadrant</label>
                    <select name="encadrant" id="encadrant" class="form-select">
                        <option value="">Tous les encadrants</option>
                        {% for encadrant in encadrants %}
                        <option value="{{ encadrant.id }}" {% if selected_encadrant == encadrant.id %}selected{% endif %}>{{ encadrant.get_full_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="annee" class="form-label">Année</label>
                    <select name="annee" id="annee" class="form-select">
                        {% for an in annees %}
                        <option value="{{ an }}" {% if selected_annee == an %}selected{% endif %}>{{ an }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="mois" class="form-label">Mois</label>
                    <select name="mois" id="mois" class="form-select">
                        {% for mois in mois_liste %}
                        <option value="{{ mois.numero }}" {% if selected_mois == mois.numero %}selected{% endif %}>{{ mois.nom }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-12 text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>Filtrer
                    </button>
                    <a href="{% url 'calendrier_stagiaires' %}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-undo me-1"></i>Réinitialiser
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Légende -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Légende</h5>
        </div>
        <div class="card-body">
            <div class="d-flex flex-wrap">
                <div class="me-4 mb-2">
                    <span class="badge bg-success p-2">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                    <span class="ms-1">En cours</span>
                </div>
                <div class="me-4 mb-2">
                    <span class="badge bg-warning p-2">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                    <span class="ms-1">Débute dans moins de 30 jours</span>
                </div>
                <div class="me-4 mb-2">
                    <span class="badge bg-info p-2">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                    <span class="ms-1">Planifié (> 30 jours)</span>
                </div>
                <div class="me-4 mb-2">
                    <span class="badge bg-secondary p-2">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                    <span class="ms-1">Terminé</span>
                </div>
                <div class="me-4 mb-2">
                    <span class="badge bg-danger p-2">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                    <span class="ms-1">Annulé/Suspendu</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendrier -->
    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="fas fa-calendar-alt me-2"></i>
                {% if vue == 'service' %}Calendrier par service
                {% elif vue == 'encadrant' %}Calendrier par encadrant
                {% else %}Calendrier de tous les stagiaires
                {% endif %}
            </h5>
        </div>
        <div class="card-body">
            {% if vue == 'service' %}
                {% for service, stagiaires_service in stagiaires_par_service.items %}
                <div class="mb-4">
                    <h5 class="border-bottom pb-2">
                        <i class="fas fa-building me-2"></i>{{ service }}
                        <span class="badge bg-primary ms-2">{{ stagiaires_service|length }} stagiaire{{ stagiaires_service|length|pluralize }}</span>
                    </h5>
                    {% include 'stagiaires/includes/calendrier_ameliore.html' with stagiaires=stagiaires_service current_month_year=current_month_year calendrier_jours=calendrier_jours %}
                </div>
                {% empty %}
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun stagiaire trouvé pour les critères sélectionnés</h5>
                </div>
                {% endfor %}
            {% elif vue == 'encadrant' %}
                {% for encadrant, stagiaires_encadrant in stagiaires_par_encadrant.items %}
                <div class="mb-4">
                    <h5 class="border-bottom pb-2">
                        <i class="fas fa-user-tie me-2"></i>{{ encadrant }}
                        <span class="badge bg-success ms-2">{{ stagiaires_encadrant|length }} stagiaire{{ stagiaires_encadrant|length|pluralize }}</span>
                    </h5>
                    {% include 'stagiaires/includes/calendrier_ameliore.html' with stagiaires=stagiaires_encadrant current_month_year=current_month_year calendrier_jours=calendrier_jours %}
                </div>
                {% empty %}
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun stagiaire trouvé pour les critères sélectionnés</h5>
                </div>
                {% endfor %}
            {% else %}
                {% include 'stagiaires/includes/calendrier_ameliore.html' with stagiaires=stagiaires current_month_year=current_month_year calendrier_jours=calendrier_jours %}
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mise à jour dynamique des filtres
    const vueSelect = document.getElementById('vue');
    const serviceSelect = document.getElementById('service');
    const encadrantSelect = document.getElementById('encadrant');
    
    vueSelect.addEventListener('change', function() {
        if (this.value === 'service') {
            serviceSelect.disabled = false;
            encadrantSelect.disabled = true;
        } else if (this.value === 'encadrant') {
            serviceSelect.disabled = true;
            encadrantSelect.disabled = false;
        } else {
            serviceSelect.disabled = false;
            encadrantSelect.disabled = false;
        }
    });
    
    // Déclencher l'événement au chargement
    vueSelect.dispatchEvent(new Event('change'));
});
</script>
{% endblock %}
