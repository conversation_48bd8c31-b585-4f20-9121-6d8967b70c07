{% extends 'stagiaires/base.html' %}

{% block title %}Gestion des Attestations{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-certificate me-2"></i>
                        Gestion des Attestations de Fin de Stage
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Statistiques -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <h5 class="text-success">{{ stagiaires_avec_attestations|length }}</h5>
                                    <small>Attestations générées</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <h5 class="text-warning">{{ stagiaires_eligibles|length }}</h5>
                                    <small>Éligibles pour attestation</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <h5 class="text-info">{{ stagiaires_avec_attestations|length|add:stagiaires_eligibles|length }}</h5>
                                    <small>Total stages terminés</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Onglets -->
                    <ul class="nav nav-tabs" id="attestationTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="eligibles-tab" data-bs-toggle="tab" data-bs-target="#eligibles" type="button" role="tab">
                                <i class="fas fa-clock me-1"></i>Éligibles ({{ stagiaires_eligibles|length }})
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="generees-tab" data-bs-toggle="tab" data-bs-target="#generees" type="button" role="tab">
                                <i class="fas fa-check-circle me-1"></i>Générées ({{ stagiaires_avec_attestations|length }})
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="attestationTabsContent">
                        <!-- Stagiaires éligibles -->
                        <div class="tab-pane fade show active" id="eligibles" role="tabpanel">
                            <div class="mt-3">
                                {% if stagiaires_eligibles %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Ces stagiaires ont terminé leur stage et remplissent toutes les conditions pour recevoir une attestation.
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-warning">
                                            <tr>
                                                <th>Stagiaire</th>
                                                <th>Département</th>
                                                <th>Encadrant</th>
                                                <th>Fin de stage</th>
                                                <th>Note</th>
                                                <th>Statut</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for stagiaire in stagiaires_eligibles %}
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle bg-warning text-dark me-2">
                                                            {{ stagiaire.prenom.0 }}{{ stagiaire.nom.0 }}
                                                        </div>
                                                        <div>
                                                            <strong>{{ stagiaire.nom_complet }}</strong><br>
                                                            <small class="text-muted">{{ stagiaire.email }}</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">{{ stagiaire.get_departement_display }}</span>
                                                </td>
                                                <td>
                                                    {% if stagiaire.encadrant %}
                                                        {{ stagiaire.encadrant.get_full_name }}
                                                    {% else %}
                                                        <span class="text-muted">Non assigné</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {{ stagiaire.date_fin|date:"d/m/Y" }}<br>
                                                    <small class="text-muted">{{ stagiaire.duree_stage }} jours</small>
                                                </td>
                                                <td>
                                                    {% if stagiaire.note_finale %}
                                                        <span class="badge bg-{% if stagiaire.note_finale >= 16 %}success{% elif stagiaire.note_finale >= 12 %}warning{% else %}danger{% endif %}">
                                                            {{ stagiaire.note_finale }}/20
                                                        </span>
                                                    {% else %}
                                                        <span class="text-muted">Non notée</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <span class="badge bg-success">{{ stagiaire.get_statut_taches_display }}</span><br>
                                                    <small class="badge bg-success">{{ stagiaire.get_statut_convention_display }}</small>
                                                </td>
                                                <td>
                                                    <a href="{% url 'generer_attestation' stagiaire.id %}" class="btn btn-success btn-sm">
                                                        <i class="fas fa-certificate me-1"></i>Générer
                                                    </a>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Aucun stagiaire éligible</h5>
                                    <p class="text-muted">Les stagiaires éligibles pour une attestation apparaîtront ici.</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Attestations générées -->
                        <div class="tab-pane fade" id="generees" role="tabpanel">
                            <div class="mt-3">
                                {% if stagiaires_avec_attestations %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-success">
                                            <tr>
                                                <th>Stagiaire</th>
                                                <th>Département</th>
                                                <th>Encadrant</th>
                                                <th>Fin de stage</th>
                                                <th>Note</th>
                                                <th>Générée le</th>
                                                <th>Générée par</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for stagiaire in stagiaires_avec_attestations %}
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle bg-success text-white me-2">
                                                            {{ stagiaire.prenom.0 }}{{ stagiaire.nom.0 }}
                                                        </div>
                                                        <div>
                                                            <strong>{{ stagiaire.nom_complet }}</strong><br>
                                                            <small class="text-muted">{{ stagiaire.email }}</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">{{ stagiaire.get_departement_display }}</span>
                                                </td>
                                                <td>
                                                    {% if stagiaire.encadrant %}
                                                        {{ stagiaire.encadrant.get_full_name }}
                                                    {% else %}
                                                        <span class="text-muted">Non assigné</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {{ stagiaire.date_fin|date:"d/m/Y" }}<br>
                                                    <small class="text-muted">{{ stagiaire.duree_stage }} jours</small>
                                                </td>
                                                <td>
                                                    {% if stagiaire.note_finale %}
                                                        <span class="badge bg-{% if stagiaire.note_finale >= 16 %}success{% elif stagiaire.note_finale >= 12 %}warning{% else %}danger{% endif %}">
                                                            {{ stagiaire.note_finale }}/20
                                                        </span>
                                                    {% else %}
                                                        <span class="text-muted">Non notée</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {{ stagiaire.date_generation_attestation|date:"d/m/Y H:i" }}
                                                </td>
                                                <td>
                                                    {{ stagiaire.attestation_generee_par.get_full_name }}
                                                </td>
                                                <td>
                                                    <a href="{% url 'generer_attestation' stagiaire.id %}" class="btn btn-outline-success btn-sm" title="Régénérer">
                                                        <i class="fas fa-redo"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-certificate fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Aucune attestation générée</h5>
                                    <p class="text-muted">Les attestations générées apparaîtront ici.</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}
</style>
{% endblock %}
