#!/usr/bin/env python
"""
Script de test pour vérifier que les gestionnaires RH peuvent ajouter des services
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Service

User = get_user_model()

def test_rh_service_permissions():
    """Test que les utilisateurs RH peuvent créer des services"""
    
    print("=== Test des permissions RH pour les services ===")
    
    # Créer un utilisateur RH de test
    rh_user, created = User.objects.get_or_create(
        username='test_rh',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'RH',
            'role': 'RH'
        }
    )
    
    if created:
        rh_user.set_password('testpassword')
        rh_user.save()
        print(f"✓ Utilisateur RH créé: {rh_user.username}")
    else:
        print(f"✓ Utilisateur RH existant: {rh_user.username}")
    
    # Vérifier le rôle
    print(f"✓ Rôle de l'utilisateur: {rh_user.role}")
    print(f"✓ Affichage du rôle: {rh_user.get_role_display()}")
    
    # Tester la création d'un service
    try:
        service = Service.objects.create(
            nom='Service Test RH',
            code_service='TEST_RH',
            description='Service créé par un gestionnaire RH pour test',
            cree_par=rh_user,
            actif=True
        )
        print(f"✓ Service créé avec succès: {service.nom} (Code: {service.code_service})")
        print(f"✓ Créé par: {service.cree_par.get_full_name()}")
        
        # Nettoyer après le test
        service.delete()
        print("✓ Service de test supprimé")
        
    except Exception as e:
        print(f"✗ Erreur lors de la création du service: {e}")
    
    # Vérifier les permissions dans les vues
    from django.test import RequestFactory
    from django.contrib.auth.models import AnonymousUser
    from stagiaires.views import add_service_view, services_list_view
    
    factory = RequestFactory()
    
    # Test de la vue services_list_view
    request = factory.get('/services/')
    request.user = rh_user
    
    try:
        response = services_list_view(request)
        print("✓ Vue services_list_view accessible pour RH")
    except Exception as e:
        print(f"✗ Erreur dans services_list_view: {e}")
    
    # Test de la vue add_service_view
    request = factory.get('/services/add/')
    request.user = rh_user
    
    try:
        response = add_service_view(request)
        print("✓ Vue add_service_view accessible pour RH")
    except Exception as e:
        print(f"✗ Erreur dans add_service_view: {e}")
    
    print("\n=== Test terminé ===")

if __name__ == '__main__':
    test_rh_service_permissions()
