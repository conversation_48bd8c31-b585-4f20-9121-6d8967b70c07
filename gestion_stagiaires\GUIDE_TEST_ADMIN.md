# Guide de test pour l'ajout de stagiaires dans l'admin Django

## Étapes de diagnostic

### 1. <PERSON><PERSON><PERSON><PERSON> le serveur
```bash
python manage.py runserver
```

### 2. Se connecter à l'administration
- Aller sur http://127.0.0.1:8000/admin/
- Se connecter avec les identifiants admin

### 3. Tester l'ajout d'un stagiaire

#### Données de test à utiliser :
```
Nom: TestManuel
Prénom: Stagiaire
Email: <EMAIL> (remplacer YYYYMMDD par la date du jour)
Téléphone: 0123456789
Date de naissance: 01/01/2000
Département: IT
Service: (sélectionner un service si disponible)
Encadrant: (sélectionner un encadrant)
Date de début: (date d'aujourd'hui)
Date de fin: (date dans 3 mois)
Statut: En cours
Établissement: Université Test Manuel
Niveau d'étude: Master
Spécialité: Informatique
```

#### Champs optionnels à laisser vides :
- Technologies
- Thématique
- Sujet
- Durée estimée: 0
- Description des tâches
- Tous les autres champs

### 4. Points à vérifier

#### Avant de cliquer "Enregistrer" :
- [ ] Tous les champs obligatoires sont remplis
- [ ] L'email est unique (pas déjà utilisé)
- [ ] Les dates sont cohérentes (fin > début)
- [ ] Un encadrant est sélectionné

#### Après avoir cliqué "Enregistrer" :
- [ ] Y a-t-il des erreurs affichées en rouge ?
- [ ] La page se recharge-t-elle sur le formulaire ou redirige-t-elle ?
- [ ] Y a-t-il un message de succès ?

#### En cas d'erreur :
1. Noter exactement le message d'erreur
2. Vérifier la console du navigateur (F12)
3. Vérifier les logs du serveur Django

### 5. Vérification en base de données

Après un test, vérifier si le stagiaire a été créé :
```bash
python manage.py shell
```

```python
from stagiaires.models import Stagiaire
stagiaires = Stagiaire.objects.filter(email__contains='test.manuel')
for s in stagiaires:
    print(f"{s.nom_complet} - {s.email} - Créé par: {s.cree_par}")
```

## Problèmes courants et solutions

### Problème 1: "This field is required"
**Cause**: Un champ obligatoire n'est pas rempli
**Solution**: Remplir tous les champs marqués avec *

### Problème 2: "Stagiaire with this Email already exists"
**Cause**: L'email est déjà utilisé
**Solution**: Utiliser un email unique

### Problème 3: La page se recharge sans message
**Cause**: Erreur silencieuse, souvent liée au champ cree_par
**Solution**: Vérifier que la correction du champ cree_par est appliquée

### Problème 4: Erreur 500
**Cause**: Erreur serveur
**Solution**: Vérifier les logs Django dans la console

## Informations de diagnostic

### Utilisateurs disponibles :
- Admin principal: admin
- Encadrants: salmarhm@, encadrant_debug, encadrant_test

### Services disponibles :
- Vérifier avec : `python manage.py shell -c "from stagiaires.models import Service; print([s.nom for s in Service.objects.all()])"`

### Dernière modification de l'admin :
- Le champ cree_par est maintenant en lecture seule
- Il est automatiquement rempli lors de la création
- Tous les champs du modèle sont inclus dans les fieldsets

## Test de validation

Si l'ajout fonctionne, le stagiaire devrait apparaître dans :
1. La liste des stagiaires dans l'admin
2. La base de données (vérifiable via shell)
3. L'interface utilisateur principale du site
