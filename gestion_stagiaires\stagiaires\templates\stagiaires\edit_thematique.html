{% extends 'stagiaires/base.html' %}

{% block title %}{{ title }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        Modifier la thématique
                    </h3>
                    <a href="{% url 'thematiques_list' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                    </a>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.titre.id_for_label }}" class="form-label">{{ form.titre.label }}</label>
                            {{ form.titre }}
                            {% if form.titre.errors %}
                                <div class="text-danger small mt-1">{{ form.titre.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">{{ form.description.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3 form-check">
                            {{ form.active }}
                            <label class="form-check-label" for="{{ form.active.id_for_label }}">{{ form.active.label }}</label>
                            {% if form.active.errors %}
                                <div class="text-danger small mt-1">{{ form.active.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Enregistrer les modifications
                            </button>
                            <a href="{% url 'thematiques_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

