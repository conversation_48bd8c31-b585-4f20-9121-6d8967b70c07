#!/usr/bin/env python
"""
Script de test des permissions pour l'application de gestion des stagiaires
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser

def test_permissions():
    """Test des permissions pour chaque rôle d'utilisateur"""
    
    print("=== TEST DES PERMISSIONS ===\n")
    
    # Récupération des utilisateurs
    try:
        admin_user = CustomUser.objects.get(role='ADMIN')
        rh_user = CustomUser.objects.get(role='RH')
        encadrant_user = CustomUser.objects.get(role='ENCADRANT')
        
        print("✅ Utilisateurs trouvés:")
        print(f"   - Administrateur: {admin_user.username} ({admin_user.get_role_display()})")
        print(f"   - RH: {rh_user.username} ({rh_user.get_role_display()})")
        print(f"   - Encadrant: {encadrant_user.username} ({encadrant_user.get_role_display()})")
        print()
        
    except CustomUser.DoesNotExist as e:
        print(f"❌ Erreur: Utilisateur manquant - {e}")
        return
    
    # Test des permissions
    permissions = {
        'Gestion des utilisateurs': {
            'url': '/users/',
            'admin': True,
            'rh': False,
            'encadrant': False
        },
        'Contrats de stage': {
            'url': '/contracts/',
            'admin': True,
            'rh': True,
            'encadrant': False
        },
        'Liste des stagiaires': {
            'url': '/stagiaires/',
            'admin': True,
            'rh': True,
            'encadrant': True
        },
        'Ajouter un stagiaire': {
            'url': '/stagiaires/add/',
            'admin': True,
            'rh': True,
            'encadrant': True
        },
        'Rapports': {
            'url': '/reports/',
            'admin': True,
            'rh': True,
            'encadrant': True
        },
        'Export': {
            'url': '/export/',
            'admin': True,
            'rh': True,
            'encadrant': True
        }
    }
    
    print("=== MATRICE DES PERMISSIONS ===")
    print(f"{'Fonctionnalité':<25} {'Admin':<8} {'RH':<8} {'Encadrant':<10}")
    print("-" * 55)
    
    for func_name, perms in permissions.items():
        admin_access = "✅" if perms['admin'] else "❌"
        rh_access = "✅" if perms['rh'] else "❌"
        encadrant_access = "✅" if perms['encadrant'] else "❌"
        
        print(f"{func_name:<25} {admin_access:<8} {rh_access:<8} {encadrant_access:<10}")
    
    print("\n=== RÉSUMÉ DES RÔLES ===")
    print("🔴 ADMINISTRATEUR:")
    print("   - Accès complet à toutes les fonctionnalités")
    print("   - Seul autorisé à gérer les utilisateurs")
    print("   - Peut gérer les contrats de stage")
    print()
    
    print("🟡 GESTIONNAIRE RH:")
    print("   - Accès aux fonctions de gestion des stagiaires")
    print("   - Peut gérer les contrats de stage")
    print("   - PAS d'accès à la gestion des utilisateurs")
    print()
    
    print("🔵 ENCADRANT:")
    print("   - Accès aux fonctions de base (stagiaires, rapports)")
    print("   - PAS d'accès aux contrats de stage")
    print("   - PAS d'accès à la gestion des utilisateurs")
    print()
    
    print("=== URLS DE TEST ===")
    print("Pour tester les permissions, connectez-vous avec chaque utilisateur et essayez d'accéder à:")
    for func_name, perms in permissions.items():
        print(f"   - {func_name}: http://127.0.0.1:8000{perms['url']}")
    
    print("\n✅ Test des permissions terminé!")

if __name__ == "__main__":
    test_permissions()
