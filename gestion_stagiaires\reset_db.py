#!/usr/bin/env python
import os
import sqlite3
import shutil
import time
import sys

# Chemin vers la base de données
db_path = 'db.sqlite3'

# Sauvegarde de la base de données
if os.path.exists(db_path):
    backup_path = 'db.sqlite3.backup'
    try:
        shutil.copy2(db_path, backup_path)
        print(f"Base de données sauvegardée dans {backup_path}")
    except Exception as e:
        print(f"Erreur lors de la sauvegarde : {e}")
        sys.exit(1)

    # Supprimer la base de données avec plusieurs tentatives
    max_attempts = 5
    for attempt in range(max_attempts):
        try:
            os.remove(db_path)
            print("Base de données supprimée")
            break
        except PermissionError:
            if attempt < max_attempts - 1:
                print(f"La base de données est utilisée par un autre processus. Tentative {attempt+1}/{max_attempts}...")
                print("Veuillez fermer toutes les applications qui pourraient utiliser la base de données.")
                time.sleep(3)  # Attendre 3 secondes avant de réessayer
            else:
                print("\nERREUR : Impossible de supprimer la base de données après plusieurs tentatives.")
                print("Veuillez suivre ces étapes manuellement :")
                print("1. Fermez tous les terminaux Django et Python")
                print("2. Arrêtez le serveur Django (Ctrl+C)")
                print("3. Fermez tous les outils de base de données")
                print("4. Supprimez manuellement le fichier db.sqlite3")
                print("5. Exécutez : python manage.py migrate")
                sys.exit(1)

print("Vous pouvez maintenant exécuter 'python manage.py migrate' pour recréer la base de données")
print("Puis 'python manage.py createsuperuser' pour créer un nouvel administrateur")
