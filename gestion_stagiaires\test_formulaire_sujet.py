#!/usr/bin/env python
"""
Test du formulaire d'ajout de sujet - Vérification des champs
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Sujet, Thematique, Service

User = get_user_model()

def test_formulaire_sujet():
    """Test du formulaire d'ajout de sujet"""
    
    print("=== TEST FORMULAIRE D'AJOUT DE SUJET ===")
    
    # 1. Récupérer les utilisateurs de test
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not admin:
        print("❌ Aucun admin trouvé")
        return
    
    print(f"✅ Admin: {admin.get_full_name()}")
    if encadrant:
        print(f"✅ Encadrant: {encadrant.get_full_name()}")
        print(f"   Service: {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    # 2. Test avec Admin
    print(f"\n👑 Test du formulaire avec Admin:")
    
    client = Client()
    client.force_login(admin)
    
    response = client.get('/sujets/add/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier la présence de tous les champs
        champs_attendus = [
            ('name="titre"', 'Champ titre'),
            ('name="description"', 'Champ description'),
            ('name="thematique"', 'Champ thématique'),
            ('name="service"', 'Champ service'),
            ('name="encadrant"', 'Champ encadrant'),
            ('name="niveau_difficulte"', 'Champ niveau difficulté'),
            ('name="duree_recommandee"', 'Champ durée recommandée'),
            ('name="competences_requises"', 'Champ compétences requises'),
            ('name="actif"', 'Champ actif'),
        ]
        
        print(f"   Vérification des champs:")
        for champ, description in champs_attendus:
            if champ in content:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description} MANQUANT")
        
        # Vérifier les labels
        labels_attendus = [
            ('Titre *', 'Label titre'),
            ('Description *', 'Label description'),
            ('Thématique *', 'Label thématique'),
            ('Service', 'Label service'),
            ('Encadrant *', 'Label encadrant'),
            ('Niveau de difficulté *', 'Label niveau difficulté'),
            ('Durée recommandée', 'Label durée'),
            ('Compétences requises', 'Label compétences'),
            ('Sujet actif', 'Label actif'),
        ]
        
        print(f"   Vérification des labels:")
        for label, description in labels_attendus:
            if label in content:
                print(f"      ✅ {description}")
            else:
                print(f"      ⚠️ {description} non trouvé")
        
        # Vérifier les options des select
        print(f"   Vérification des options:")
        
        # Thématiques
        thematiques_count = content.count('<option value=')
        print(f"      Options thématiques: {thematiques_count}")
        
        # Services
        services_count = Service.objects.filter(actif=True).count()
        print(f"      Services actifs en base: {services_count}")
        
        # Encadrants
        encadrants_count = User.objects.filter(role='ENCADRANT', is_active=True).count()
        print(f"      Encadrants actifs en base: {encadrants_count}")
    
    # 3. Test avec Encadrant
    if encadrant:
        print(f"\n👤 Test du formulaire avec Encadrant:")
        
        client.force_login(encadrant)
        response = client.get('/sujets/add/')
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # Vérifications spécifiques pour encadrant
            checks_encadrant = [
                ('service_info', 'Champ service_info'),
                ('readonly', 'Champ en lecture seule'),
                ('automatiquement assignés', 'Message informatif'),
                (encadrant.get_full_name(), 'Nom de l\'encadrant affiché'),
            ]
            
            print(f"   Vérifications spécifiques encadrant:")
            for check, description in checks_encadrant:
                if check in content:
                    print(f"      ✅ {description}")
                else:
                    print(f"      ⚠️ {description} non trouvé")
            
            # Vérifier que les thématiques sont filtrées
            if encadrant.service:
                thematiques_service = Thematique.objects.filter(
                    service=encadrant.service, active=True
                ).count()
                print(f"      Thématiques du service: {thematiques_service}")
    
    # 4. Test de soumission du formulaire
    print(f"\n📝 Test de soumission du formulaire:")
    
    # Récupérer une thématique pour le test
    thematique_test = Thematique.objects.filter(active=True).first()
    service_test = Service.objects.filter(actif=True).first()
    
    if thematique_test and service_test:
        test_data = {
            'titre': 'Test Formulaire Complet',
            'description': 'Description complète du sujet de test avec tous les champs',
            'thematique': thematique_test.id,
            'service': service_test.id,
            'encadrant': admin.id,
            'niveau_difficulte': 'MOYEN',
            'duree_recommandee': 45,
            'competences_requises': 'Python, Django, HTML, CSS',
            'actif': True,
        }
        
        print(f"   Données de test:")
        for key, value in test_data.items():
            print(f"     • {key}: {value}")
        
        # Supprimer le sujet de test s'il existe
        Sujet.objects.filter(titre='Test Formulaire Complet').delete()
        
        client.force_login(admin)
        response = client.post('/sujets/add/', test_data)
        print(f"   Status POST: {response.status_code}")
        
        if response.status_code == 302:
            print("   ✅ Soumission réussie")
            
            # Vérifier la création
            sujet_cree = Sujet.objects.filter(titre='Test Formulaire Complet').first()
            if sujet_cree:
                print(f"   ✅ Sujet créé avec tous les champs:")
                print(f"      • Titre: {sujet_cree.titre}")
                print(f"      • Description: {sujet_cree.description[:50]}...")
                print(f"      • Thématique: {sujet_cree.thematique.titre}")
                print(f"      • Service: {sujet_cree.service.nom if sujet_cree.service else 'Aucun'}")
                print(f"      • Encadrant: {sujet_cree.encadrant.get_full_name() if sujet_cree.encadrant else 'Aucun'}")
                print(f"      • Niveau: {sujet_cree.get_niveau_difficulte_display()}")
                print(f"      • Durée: {sujet_cree.duree_recommandee} jours")
                print(f"      • Compétences: {sujet_cree.competences_requises[:50] if sujet_cree.competences_requises else 'Aucune'}...")
                print(f"      • Actif: {sujet_cree.actif}")
                
                # Nettoyer
                sujet_cree.delete()
                print(f"      🧹 Sujet de test supprimé")
        else:
            print("   ❌ Soumission échouée")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DE LA VÉRIFICATION:")
    print("")
    print("✅ CHAMPS VÉRIFIÉS :")
    print("   • Titre (obligatoire)")
    print("   • Description (obligatoire)")
    print("   • Thématique (obligatoire)")
    print("   • Service")
    print("   • Encadrant")
    print("   • Niveau de difficulté")
    print("   • Durée recommandée")
    print("   • Compétences requises")
    print("   • Statut actif")
    print("")
    print("✅ FONCTIONNALITÉS VÉRIFIÉES :")
    print("   • Affichage de tous les champs")
    print("   • Labels appropriés")
    print("   • Validation des données")
    print("   • Adaptation par rôle (encadrant)")
    print("   • Soumission et création")
    print("")
    print("🎯 SI DES CHAMPS NE S'AFFICHENT PAS :")
    print("   1. Vérifiez que le formulaire SujetForm contient tous les champs")
    print("   2. Vérifiez que le template affiche tous les champs")
    print("   3. Vérifiez les permissions et filtres")
    print("   4. Consultez les erreurs JavaScript dans le navigateur")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_formulaire_sujet()
