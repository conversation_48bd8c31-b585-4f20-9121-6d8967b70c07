#!/usr/bin/env python
"""
Script de test pour la suppression d'utilisateurs
"""

import os
import sys
import django
from datetime import date

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser, Stagiaire, TacheStage
from django.db import transaction
from django.contrib.auth.models import Permission

def test_user_deletion_permissions():
    """Test des permissions de suppression d'utilisateurs"""
    print("🔍 Test des permissions de suppression d'utilisateurs")
    print("=" * 60)
    
    # Vérifier les permissions Django
    print("\n1. 📋 Permissions Django pour CustomUser :")
    permissions = Permission.objects.filter(content_type__model='customuser')
    for perm in permissions:
        print(f"   - {perm.codename}: {perm.name}")
    
    # Compter les utilisateurs par rôle
    print("\n2. 👥 Utilisateurs existants :")
    for role_code, role_label in CustomUser.ROLE_CHOICES:
        count = CustomUser.objects.filter(role=role_code).count()
        print(f"   - {role_label}: {count} utilisateur(s)")
    
    total_users = CustomUser.objects.count()
    print(f"   - Total: {total_users} utilisateur(s)")
    
    return total_users > 0

def test_create_test_user():
    """Créer un utilisateur de test pour la suppression"""
    print("\n3. 🧪 Création d'un utilisateur de test :")
    
    try:
        # Créer un utilisateur de test
        test_user, created = CustomUser.objects.get_or_create(
            username='test_deletion_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'Deletion',
                'role': 'ENCADRANT',
                'is_active': True
            }
        )
        
        if created:
            test_user.set_password('test123')
            test_user.save()
            print(f"   ✓ Utilisateur de test créé : {test_user.get_full_name()}")
        else:
            print(f"   ✓ Utilisateur de test existant : {test_user.get_full_name()}")
        
        return test_user
        
    except Exception as e:
        print(f"   ❌ Erreur lors de la création : {e}")
        return None

def test_user_dependencies(user):
    """Vérifier les dépendances d'un utilisateur"""
    print(f"\n4. 🔗 Vérification des dépendances pour {user.username} :")
    
    dependencies = {}
    
    # Stagiaires créés par cet utilisateur
    stagiaires_crees = user.stagiaires_crees.count()
    dependencies['stagiaires_crees'] = stagiaires_crees
    print(f"   - Stagiaires créés : {stagiaires_crees}")
    
    # Stagiaires encadrés (si encadrant)
    if hasattr(user, 'stagiaire'):
        stagiaires_encadres = Stagiaire.objects.filter(encadrant=user).count()
        dependencies['stagiaires_encadres'] = stagiaires_encadres
        print(f"   - Stagiaires encadrés : {stagiaires_encadres}")
    
    # Tâches créées
    taches_creees = TacheStage.objects.filter(creee_par=user).count()
    dependencies['taches_creees'] = taches_creees
    print(f"   - Tâches créées : {taches_creees}")
    
    # Vérifier si l'utilisateur peut être supprimé
    can_delete = all(count == 0 for count in dependencies.values())
    print(f"   - Peut être supprimé : {'✅ Oui' if can_delete else '❌ Non'}")
    
    return dependencies, can_delete

def test_safe_deletion(user):
    """Test de suppression sécurisée"""
    print(f"\n5. 🗑️ Test de suppression pour {user.username} :")
    
    try:
        # Vérifier les dépendances
        dependencies, can_delete = test_user_dependencies(user)
        
        if not can_delete:
            print("   ⚠️ Utilisateur a des dépendances, suppression non recommandée")
            return False
        
        # Sauvegarder les informations avant suppression
        user_info = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'full_name': user.get_full_name(),
            'role': user.get_role_display()
        }
        
        # Tenter la suppression
        with transaction.atomic():
            user.delete()
            print(f"   ✅ Utilisateur supprimé avec succès")
            print(f"      - ID: {user_info['id']}")
            print(f"      - Nom: {user_info['full_name']}")
            print(f"      - Email: {user_info['email']}")
            print(f"      - Rôle: {user_info['role']}")
        
        # Vérifier que l'utilisateur n'existe plus
        try:
            CustomUser.objects.get(id=user_info['id'])
            print("   ❌ Erreur: L'utilisateur existe encore après suppression")
            return False
        except CustomUser.DoesNotExist:
            print("   ✅ Confirmation: L'utilisateur a bien été supprimé")
            return True
            
    except Exception as e:
        print(f"   ❌ Erreur lors de la suppression : {e}")
        return False

def test_deletion_with_dependencies():
    """Test de suppression avec dépendances"""
    print("\n6. 🔗 Test de suppression avec dépendances :")
    
    try:
        # Créer un utilisateur avec des dépendances
        user_with_deps = CustomUser.objects.create_user(
            username='user_with_deps',
            email='<EMAIL>',
            password='test123',
            first_name='User',
            last_name='WithDeps',
            role='ENCADRANT'
        )
        
        # Créer un stagiaire encadré par cet utilisateur
        stagiaire = Stagiaire.objects.create(
            nom='TestStagiaire',
            prenom='Suppression',
            email='<EMAIL>',
            date_naissance=date(2000, 1, 1),
            telephone='0123456789',
            departement='Test',
            encadrant=user_with_deps,
            date_debut=date.today(),
            date_fin=date.today(),
            etablissement='Test University',
            niveau_etude='Master',
            specialite='Test',
            cree_par=user_with_deps
        )
        
        print(f"   ✓ Utilisateur avec dépendances créé : {user_with_deps.username}")
        print(f"   ✓ Stagiaire associé créé : {stagiaire.nom_complet}")
        
        # Tenter la suppression
        dependencies, can_delete = test_user_dependencies(user_with_deps)
        
        if can_delete:
            print("   ⚠️ Attention: L'utilisateur peut être supprimé malgré les dépendances")
        else:
            print("   ✅ Correct: L'utilisateur ne peut pas être supprimé à cause des dépendances")
        
        # Nettoyer les données de test
        stagiaire.delete()
        user_with_deps.delete()
        print("   🧹 Données de test nettoyées")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur lors du test avec dépendances : {e}")
        return False

def test_admin_interface_deletion():
    """Test de l'interface admin Django"""
    print("\n7. 🔧 Test de l'interface admin Django :")
    
    try:
        # Vérifier si l'admin est configuré
        from django.contrib import admin
        from stagiaires.admin import CustomUserAdmin
        
        print("   ✓ Interface admin configurée")
        print(f"   - Modèle enregistré : {CustomUser in admin.site._registry}")
        print(f"   - Admin class : {CustomUserAdmin}")
        
        # Vérifier les actions disponibles
        admin_instance = CustomUserAdmin(CustomUser, admin.site)
        actions = admin_instance.get_actions(None)
        
        print("   - Actions disponibles :")
        for action_name, action_func in actions.items():
            print(f"     • {action_name}")
        
        # Vérifier si delete_selected est disponible
        has_delete = 'delete_selected' in actions
        print(f"   - Action de suppression : {'✅ Disponible' if has_delete else '❌ Non disponible'}")
        
        return has_delete
        
    except Exception as e:
        print(f"   ❌ Erreur lors du test admin : {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 TEST DE SUPPRESSION D'UTILISATEURS")
    print("🔍 Diagnostic des problèmes de suppression")
    print("=" * 70)
    
    results = {}
    
    try:
        # Tests séquentiels
        results['permissions'] = test_user_deletion_permissions()
        
        test_user = test_create_test_user()
        if test_user:
            results['safe_deletion'] = test_safe_deletion(test_user)
        else:
            results['safe_deletion'] = False
        
        results['dependencies'] = test_deletion_with_dependencies()
        results['admin_interface'] = test_admin_interface_deletion()
        
        # Résumé des résultats
        print("\n" + "=" * 70)
        print("📊 RÉSUMÉ DES TESTS")
        print("=" * 70)
        
        for test_name, result in results.items():
            status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
            print(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        # Diagnostic
        print("\n🔍 DIAGNOSTIC :")
        
        if not results.get('admin_interface', False):
            print("   ⚠️ L'action de suppression n'est pas disponible dans l'admin")
            print("   💡 Solution : Vérifier la configuration de l'admin Django")
        
        if results.get('safe_deletion', False):
            print("   ✅ La suppression fonctionne pour les utilisateurs sans dépendances")
        else:
            print("   ❌ Problème avec la suppression d'utilisateurs")
        
        if results.get('dependencies', False):
            print("   ✅ La gestion des dépendances fonctionne correctement")
        
        print("\n📋 RECOMMANDATIONS :")
        print("   1. Implémenter une vue de suppression AJAX dans l'interface personnalisée")
        print("   2. Ajouter une vérification des dépendances avant suppression")
        print("   3. Proposer une suppression en cascade ou un transfert de données")
        print("   4. Ajouter des confirmations de sécurité")
        
        success = all(results.values())
        
    except Exception as e:
        print(f"\n❌ Erreur générale lors des tests : {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
