# 🚀 GUIDE D'INSTALLATION - SYSTÈME DE GESTION DES STAGIAIRES

## 📋 PRÉREQUIS

### Système d'Exploitation
- **Windows** 10/11, **macOS** 10.15+, ou **Linux** Ubuntu 18.04+
- **RAM** : Minimum 4GB, Recommandé 8GB+
- **Espace disque** : Minimum 2GB libre

### Logiciels Requis
- **Python 3.8+** ([Télécharger](https://python.org/downloads/))
- **pip** (inclus avec Python)
- **Git** ([Télécharger](https://git-scm.com/downloads/))

---

## ⚡ INSTALLATION RAPIDE (5 MINUTES)

### 1. C<PERSON><PERSON> le Projet
```bash
git clone <url-du-repository>
cd gestion_stagiaires
```

### 2. Créer l'Environnement Virtuel
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

### 3. Installer les Dépendances
```bash
pip install -r requirements.txt
```

### 4. Configuration de la Base de Données
```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. Créer un Superutilisateur
```bash
python manage.py createsuperuser
# Suivre les instructions à l'écran
```

### 6. Lancer l'Application
```bash
python manage.py runserver
```

### 7. Accéder à l'Application
- **URL** : http://127.0.0.1:8000/
- **Admin** : http://127.0.0.1:8000/admin/

---

## 🧪 DONNÉES DE TEST

### Créer un Utilisateur Encadrant de Test
```bash
python create_test_user.py
```
**Identifiants créés :**
- Username : `encadrant_test`
- Password : `test123`
- Rôle : Encadrant
- Service : Informatique

### Créer des Stagiaires de Test
```bash
python test_cv_functionality.py
python test_statut_periode.py
```

### Démonstration Complète
```bash
python demo_complete_rencontre.py
```

---

## 🔧 CONFIGURATION AVANCÉE

### Variables d'Environnement (.env)
Créer un fichier `.env` dans le dossier racine :
```env
# Sécurité
SECRET_KEY=votre-clé-secrète-très-longue-et-complexe
DEBUG=True

# Base de données (optionnel)
DATABASE_URL=sqlite:///db.sqlite3

# Email (optionnel)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=votre-mot-de-passe-app

# Fichiers média
MEDIA_ROOT=media/
STATIC_ROOT=staticfiles/
```

### Configuration Email
Pour activer l'envoi d'emails réels, modifier dans `settings.py` :
```python
# Remplacer
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Par
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
```

---

## 🗄️ CONFIGURATION BASE DE DONNÉES

### SQLite (Par défaut - Développement)
Aucune configuration supplémentaire requise.

### PostgreSQL (Production Recommandée)
```bash
# Installation PostgreSQL
pip install psycopg2-binary

# Configuration dans settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'gestion_stagiaires',
        'USER': 'votre_utilisateur',
        'PASSWORD': 'votre_mot_de_passe',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

### MySQL (Alternative)
```bash
# Installation MySQL
pip install mysqlclient

# Configuration dans settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'gestion_stagiaires',
        'USER': 'votre_utilisateur',
        'PASSWORD': 'votre_mot_de_passe',
        'HOST': 'localhost',
        'PORT': '3306',
    }
}
```

---

## 🌐 DÉPLOIEMENT PRODUCTION

### 1. Préparation
```bash
# Désactiver le mode debug
DEBUG = False

# Configurer les hôtes autorisés
ALLOWED_HOSTS = ['votre-domaine.com', 'www.votre-domaine.com']

# Collecter les fichiers statiques
python manage.py collectstatic
```

### 2. Serveur Web (Nginx + Gunicorn)
```bash
# Installation Gunicorn
pip install gunicorn

# Lancement avec Gunicorn
gunicorn gestion_stagiaires.wsgi:application --bind 0.0.0.0:8000
```

### 3. Configuration Nginx
```nginx
server {
    listen 80;
    server_name votre-domaine.com;
    
    location /static/ {
        alias /path/to/staticfiles/;
    }
    
    location /media/ {
        alias /path/to/media/;
    }
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

## 🔍 VÉRIFICATION DE L'INSTALLATION

### Tests Automatiques
```bash
# Test des fonctionnalités principales
python test_rencontre_functionality.py
python test_cv_functionality.py
python test_statut_periode.py

# Démonstration complète
python demo_complete_rencontre.py
```

### Vérifications Manuelles
1. **Accès à l'application** : http://127.0.0.1:8000/
2. **Page de connexion** fonctionnelle
3. **Interface d'administration** : http://127.0.0.1:8000/admin/
4. **Upload de fichiers** opérationnel
5. **Envoi d'emails** (console ou SMTP)

---

## 🛠️ DÉPANNAGE

### Problèmes Courants

#### Erreur "Module not found"
```bash
# Vérifier l'environnement virtuel
pip list
pip install -r requirements.txt
```

#### Erreur de base de données
```bash
# Réinitialiser la base de données
rm db.sqlite3
python manage.py makemigrations
python manage.py migrate
```

#### Problème de permissions fichiers
```bash
# Linux/macOS
chmod -R 755 media/
chmod -R 755 static/

# Windows
# Vérifier les permissions du dossier dans l'explorateur
```

#### Erreur CSRF
```bash
# Vider le cache du navigateur
# Ou ajouter dans settings.py :
CSRF_TRUSTED_ORIGINS = ['http://127.0.0.1:8000', 'http://localhost:8000']
```

### Logs de Débogage
```bash
# Activer les logs détaillés dans settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'debug.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

---

## 📞 SUPPORT

### Documentation
- **Rapport complet** : `RAPPORT_APPLICATION_GESTION_STAGIAIRES.md`
- **Synthèse exécutive** : `SYNTHESE_EXECUTIVE.md`
- **Code source** : Commenté et documenté

### Scripts Utiles
- `create_test_user.py` : Création d'utilisateurs de test
- `test_*.py` : Scripts de test et validation
- `demo_*.py` : Démonstrations fonctionnelles

### Commandes de Maintenance
```bash
# Sauvegarde de la base de données
python manage.py dumpdata > backup.json

# Restauration
python manage.py loaddata backup.json

# Nettoyage des sessions expirées
python manage.py clearsessions

# Vérification de l'intégrité
python manage.py check
```

---

## ✅ CHECKLIST POST-INSTALLATION

- [ ] Application accessible sur http://127.0.0.1:8000/
- [ ] Connexion administrateur fonctionnelle
- [ ] Création d'utilisateurs possible
- [ ] Upload de fichiers opérationnel
- [ ] Envoi d'emails configuré
- [ ] Tests automatiques passent
- [ ] Interface responsive sur mobile
- [ ] Sauvegarde configurée
- [ ] Monitoring en place (production)
- [ ] SSL configuré (production)

---

**🎉 Félicitations ! Votre système de gestion des stagiaires est opérationnel !**

*Pour toute question technique, consulter la documentation complète ou contacter l'équipe de développement.*
