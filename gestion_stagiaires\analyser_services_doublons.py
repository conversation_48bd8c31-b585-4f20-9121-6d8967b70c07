#!/usr/bin/env python
"""
Script pour analyser les services en double (sans supprimer)
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import Service
from django.db.models import Count

def analyser_services_doublons():
    """Analyser les services en double sans les supprimer"""
    
    print("=== ANALYSE DES SERVICES EN DOUBLE ===")
    
    # 1. Statistiques générales
    total_services = Service.objects.count()
    services_actifs = Service.objects.filter(actif=True).count()
    services_inactifs = Service.objects.filter(actif=False).count()
    
    print(f"\n📊 STATISTIQUES GÉNÉRALES:")
    print(f"   Total services: {total_services}")
    print(f"   Services actifs: {services_actifs}")
    print(f"   Services inactifs: {services_inactifs}")
    
    # 2. Analyser les doublons par nom
    print(f"\n🔍 ANALYSE DES DOUBLONS PAR NOM:")
    
    doublons_nom = Service.objects.values('nom').annotate(
        count=Count('nom')
    ).filter(count__gt=1).order_by('nom')
    
    print(f"   Noms en double: {doublons_nom.count()}")
    
    for doublon in doublons_nom:
        nom = doublon['nom']
        count = doublon['count']
        print(f"\n   📋 '{nom}' ({count} occurrences):")
        
        services_meme_nom = Service.objects.filter(nom=nom).order_by('date_creation')
        
        for i, service in enumerate(services_meme_nom):
            statut = "✅ GARDER" if i == 0 else "❌ SUPPRIMER"
            actif_str = "Actif" if service.actif else "Inactif"
            
            # Compter les dépendances
            utilisateurs = service.utilisateurs.count()
            thematiques = service.thematiques.count()
            sujets = service.sujets.count()
            stagiaires = service.stagiaires_service.count()
            
            print(f"      {statut}: ID {service.id} | {actif_str} | Créé: {service.date_creation.strftime('%d/%m/%Y')}")
            print(f"         Code: {service.code_service}")
            print(f"         Dépendances: {utilisateurs} users, {thematiques} thématiques, {sujets} sujets, {stagiaires} stagiaires")
    
    # 3. Analyser les doublons par code
    print(f"\n🔍 ANALYSE DES DOUBLONS PAR CODE:")
    
    doublons_code = Service.objects.values('code_service').annotate(
        count=Count('code_service')
    ).filter(count__gt=1).order_by('code_service')
    
    print(f"   Codes en double: {doublons_code.count()}")
    
    for doublon in doublons_code:
        code = doublon['code_service']
        count = doublon['count']
        print(f"\n   📋 Code '{code}' ({count} occurrences):")
        
        services_meme_code = Service.objects.filter(code_service=code).order_by('date_creation')
        
        for i, service in enumerate(services_meme_code):
            statut = "✅ GARDER" if i == 0 else "❌ SUPPRIMER"
            actif_str = "Actif" if service.actif else "Inactif"
            
            # Compter les dépendances
            utilisateurs = service.utilisateurs.count()
            thematiques = service.thematiques.count()
            sujets = service.sujets.count()
            stagiaires = service.stagiaires_service.count()
            
            print(f"      {statut}: ID {service.id} | {service.nom} | {actif_str}")
            print(f"         Créé: {service.date_creation.strftime('%d/%m/%Y')}")
            print(f"         Dépendances: {utilisateurs} users, {thematiques} thématiques, {sujets} sujets, {stagiaires} stagiaires")
    
    # 4. Analyser les doublons par nom similaire (casse)
    print(f"\n🔍 ANALYSE DES DOUBLONS PAR CASSE:")
    
    tous_services = Service.objects.all().order_by('nom')
    services_traites = set()
    doublons_casse_count = 0
    
    for service in tous_services:
        if service.id in services_traites:
            continue
        
        # Chercher des services avec le même nom en ignorant la casse
        services_similaires = Service.objects.filter(
            nom__iexact=service.nom
        ).exclude(id=service.id)
        
        if services_similaires.exists():
            doublons_casse_count += 1
            print(f"\n   📋 Groupe de noms similaires:")
            
            # Afficher le service principal
            utilisateurs = service.utilisateurs.count()
            thematiques = service.thematiques.count()
            sujets = service.sujets.count()
            stagiaires = service.stagiaires_service.count()
            actif_str = "Actif" if service.actif else "Inactif"

            print(f"      ✅ GARDER: ID {service.id} | '{service.nom}' | {actif_str}")
            print(f"         Code: {service.code_service} | Créé: {service.date_creation.strftime('%d/%m/%Y')}")
            print(f"         Dépendances: {utilisateurs} users, {thematiques} thématiques, {sujets} sujets, {stagiaires} stagiaires")
            
            # Afficher les services similaires
            for service_similaire in services_similaires:
                if service_similaire.id not in services_traites:
                    utilisateurs_sim = service_similaire.utilisateurs.count()
                    thematiques_sim = service_similaire.thematiques.count()
                    sujets_sim = service_similaire.sujets.count()
                    stagiaires_sim = service_similaire.stagiaires_service.count()
                    actif_str_sim = "Actif" if service_similaire.actif else "Inactif"

                    print(f"      ❌ SUPPRIMER: ID {service_similaire.id} | '{service_similaire.nom}' | {actif_str_sim}")
                    print(f"         Code: {service_similaire.code_service} | Créé: {service_similaire.date_creation.strftime('%d/%m/%Y')}")
                    print(f"         Dépendances: {utilisateurs_sim} users, {thematiques_sim} thématiques, {sujets_sim} sujets, {stagiaires_sim} stagiaires")
                    
                    services_traites.add(service_similaire.id)
        
        services_traites.add(service.id)
    
    print(f"   Groupes de noms similaires: {doublons_casse_count}")
    
    # 5. Liste de tous les services
    print(f"\n📋 LISTE COMPLÈTE DES SERVICES:")
    
    tous_services = Service.objects.all().order_by('nom')
    for service in tous_services:
        actif_str = "✅" if service.actif else "❌"
        utilisateurs = service.utilisateurs.count()
        thematiques = service.thematiques.count()
        sujets = service.sujets.count()
        stagiaires = service.stagiaires_service.count()

        print(f"   {actif_str} ID {service.id:2d} | {service.nom:30s} | Code: {service.code_service:10s} | Deps: {utilisateurs}u/{thematiques}t/{sujets}s/{stagiaires}st")
    
    # 6. Résumé
    total_doublons = doublons_nom.count() + doublons_code.count() + doublons_casse_count
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DE L'ANALYSE:")
    print("")
    print(f"🔍 DOUBLONS DÉTECTÉS :")
    print(f"   • Doublons par nom exact: {doublons_nom.count()}")
    print(f"   • Doublons par code: {doublons_code.count()}")
    print(f"   • Doublons par casse: {doublons_casse_count}")
    print(f"   • Total groupes de doublons: {total_doublons}")
    print("")
    
    if total_doublons > 0:
        print("⚠️  ACTIONS RECOMMANDÉES :")
        print("   1. Exécuter le script de suppression des doublons")
        print("   2. Vérifier les dépendances avant suppression")
        print("   3. Sauvegarder la base de données avant nettoyage")
        print("")
        print("📝 POUR SUPPRIMER LES DOUBLONS :")
        print("   python supprimer_services_doublons.py")
    else:
        print("✅ AUCUN DOUBLON DÉTECTÉ !")
        print("   La base de données est propre.")
    
    print(f"{'='*60}")

if __name__ == '__main__':
    analyser_services_doublons()
