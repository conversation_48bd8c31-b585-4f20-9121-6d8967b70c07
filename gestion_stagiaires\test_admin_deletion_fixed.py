#!/usr/bin/env python
"""
Script de test pour vérifier que la suppression fonctionne maintenant dans l'admin Django
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser, Stagiaire, Mission, RapportStage
from datetime import date

def test_user_deletion_with_dependencies():
    """Tester la suppression d'un utilisateur avec des dépendances"""
    print("🧪 TEST DE SUPPRESSION AVEC DÉPENDANCES")
    print("=" * 60)
    
    # Créer un utilisateur de test
    test_user = CustomUser.objects.create(
        username='test_deletion_fixed',
        email='<EMAIL>',
        first_name='Test',
        last_name='DeletionFixed',
        role='ENCADRANT'
    )
    test_user.set_password('test123')
    test_user.save()
    print(f"✅ Utilisateur créé : {test_user.get_full_name()}")
    
    # Créer un stagiaire
    stagiaire = Stagiaire.objects.create(
        nom='TestStagiaire',
        prenom='Suppression',
        email='<EMAIL>',
        date_naissance=date(2000, 1, 1),
        telephone='0123456789',
        departement='Informatique',
        encadrant=test_user,
        date_debut=date.today(),
        date_fin=date.today(),
        etablissement='Test University',
        niveau_etude='Master',
        specialite='Test',
        cree_par=test_user
    )
    print(f"✅ Stagiaire créé : {stagiaire.nom_complet}")
    
    # Créer une mission (qui était problématique avant)
    mission = Mission.objects.create(
        stagiaire=stagiaire,
        titre='Mission de test',
        description='Description de test',
        objectifs='Objectifs de test',
        livrables_attendus='Livrables de test',
        date_debut_prevue=date.today(),
        date_fin_prevue=date.today(),
        creee_par=test_user  # Cette relation était CASCADE avant
    )
    print(f"✅ Mission créée : {mission.titre}")
    
    # Vérifier les dépendances avant suppression
    print(f"\n🔍 Dépendances de {test_user.username} :")
    print(f"   • Stagiaires créés : {Stagiaire.objects.filter(cree_par=test_user).count()}")
    print(f"   • Stagiaires encadrés : {Stagiaire.objects.filter(encadrant=test_user).count()}")
    print(f"   • Missions créées : {Mission.objects.filter(creee_par=test_user).count()}")
    
    # Tenter la suppression de l'utilisateur
    print(f"\n🗑️ Tentative de suppression de {test_user.username}...")
    try:
        user_id = test_user.id
        test_user.delete()
        print("✅ SUPPRESSION RÉUSSIE !")
        
        # Vérifier que l'utilisateur n'existe plus
        try:
            CustomUser.objects.get(id=user_id)
            print("❌ ERREUR : L'utilisateur existe encore")
            return False
        except CustomUser.DoesNotExist:
            print("✅ Confirmation : L'utilisateur a bien été supprimé")
        
        # Vérifier l'état des objets liés
        print("\n🔍 État des objets liés après suppression :")
        
        # Le stagiaire devrait encore exister (SET_NULL)
        try:
            stagiaire_updated = Stagiaire.objects.get(id=stagiaire.id)
            print(f"   ✅ Stagiaire existe encore : {stagiaire_updated.nom_complet}")
            print(f"   • Encadrant : {stagiaire_updated.encadrant}")  # Devrait être None
            print(f"   • Créé par : {stagiaire_updated.cree_par}")    # Devrait être None
        except Stagiaire.DoesNotExist:
            print("   ❌ ERREUR : Le stagiaire a été supprimé (ne devrait pas)")
        
        # La mission devrait encore exister (SET_NULL)
        try:
            mission_updated = Mission.objects.get(id=mission.id)
            print(f"   ✅ Mission existe encore : {mission_updated.titre}")
            print(f"   • Créée par : {mission_updated.creee_par}")    # Devrait être None
        except Mission.DoesNotExist:
            print("   ❌ ERREUR : La mission a été supprimée (ne devrait pas)")
        
        # Nettoyer les objets restants
        print("\n🧹 Nettoyage...")
        Mission.objects.filter(id=mission.id).delete()
        Stagiaire.objects.filter(id=stagiaire.id).delete()
        print("✅ Objets de test nettoyés")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR lors de la suppression : {e}")
        # Nettoyer en cas d'erreur
        try:
            mission.delete()
            stagiaire.delete()
            test_user.delete()
        except:
            pass
        return False

def test_stagiaire_deletion():
    """Tester la suppression d'un stagiaire"""
    print("\n" + "=" * 60)
    print("🧪 TEST DE SUPPRESSION DE STAGIAIRE")
    print("=" * 60)
    
    # Créer un encadrant
    encadrant = CustomUser.objects.create(
        username='encadrant_test_deletion',
        email='<EMAIL>',
        first_name='Encadrant',
        last_name='Test',
        role='ENCADRANT'
    )
    encadrant.set_password('test123')
    encadrant.save()
    
    # Créer un stagiaire
    stagiaire = Stagiaire.objects.create(
        nom='StagiaireTest',
        prenom='Suppression',
        email='<EMAIL>',
        date_naissance=date(2000, 1, 1),
        telephone='0123456789',
        departement='Informatique',
        encadrant=encadrant,
        date_debut=date.today(),
        date_fin=date.today(),
        etablissement='Test University',
        niveau_etude='Master',
        specialite='Test',
        cree_par=encadrant
    )
    print(f"✅ Stagiaire créé : {stagiaire.nom_complet}")
    
    # Tenter la suppression
    try:
        stagiaire_id = stagiaire.id
        stagiaire.delete()
        print("✅ SUPPRESSION DE STAGIAIRE RÉUSSIE !")
        
        # Vérifier que le stagiaire n'existe plus
        try:
            Stagiaire.objects.get(id=stagiaire_id)
            print("❌ ERREUR : Le stagiaire existe encore")
            success = False
        except Stagiaire.DoesNotExist:
            print("✅ Confirmation : Le stagiaire a bien été supprimé")
            success = True
            
    except Exception as e:
        print(f"❌ ERREUR lors de la suppression du stagiaire : {e}")
        success = False
    
    # Nettoyer l'encadrant
    try:
        encadrant.delete()
        print("✅ Encadrant nettoyé")
    except Exception as e:
        print(f"⚠️ Erreur lors du nettoyage de l'encadrant : {e}")
    
    return success

def test_admin_interface_access():
    """Tester l'accès à l'interface admin"""
    print("\n" + "=" * 60)
    print("🌐 TEST D'ACCÈS À L'INTERFACE ADMIN")
    print("=" * 60)
    
    from django.urls import reverse
    
    try:
        # URLs importantes
        admin_urls = [
            ('admin:index', 'Page d\'accueil admin'),
            ('admin:stagiaires_customuser_changelist', 'Liste des utilisateurs'),
            ('admin:stagiaires_stagiaire_changelist', 'Liste des stagiaires'),
        ]
        
        for url_name, description in admin_urls:
            try:
                url = reverse(url_name)
                print(f"   ✅ {description}: {url}")
            except Exception as e:
                print(f"   ❌ {description}: Erreur - {e}")
                return False
        
        print(f"\n📋 Instructions pour tester :")
        print(f"   1. Allez sur : http://127.0.0.1:8000/admin/")
        print(f"   2. Connectez-vous avec : admin / [votre mot de passe]")
        print(f"   3. Essayez de supprimer un utilisateur ou stagiaire")
        print(f"   4. La suppression devrait maintenant fonctionner !")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification des URLs : {e}")
        return False

def main():
    """Fonction principale"""
    print("🔧 VÉRIFICATION DE LA CORRECTION DU PROBLÈME DE SUPPRESSION")
    print("🎯 Les relations CASCADE ont été changées en SET_NULL")
    print("=" * 70)
    
    try:
        # Tests
        test1 = test_user_deletion_with_dependencies()
        test2 = test_stagiaire_deletion()
        test3 = test_admin_interface_access()
        
        print("\n" + "=" * 60)
        print("📊 RÉSUMÉ DES TESTS")
        print("=" * 60)
        
        tests = [
            ("Suppression utilisateur avec dépendances", test1),
            ("Suppression stagiaire", test2),
            ("Accès interface admin", test3)
        ]
        
        all_passed = True
        for test_name, result in tests:
            status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
            print(f"   {status}: {test_name}")
            if not result:
                all_passed = False
        
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 PROBLÈME RÉSOLU !")
            print("✅ La suppression fonctionne maintenant dans l'admin Django")
            print("\n🔧 Changements apportés :")
            print("   • Mission.creee_par : CASCADE → SET_NULL")
            print("   • RapportStage.valide_par : CASCADE → SET_NULL")
            print("   • Migration appliquée avec succès")
            print("\n📋 VOUS POUVEZ MAINTENANT :")
            print("   • Supprimer des utilisateurs dans l'admin Django")
            print("   • Supprimer des stagiaires dans l'admin Django")
            print("   • Les objets liés ne seront plus bloquants")
        else:
            print("❌ CERTAINS PROBLÈMES PERSISTENT")
            print("⚠️ Vérifiez les erreurs ci-dessus")
        
        return all_passed
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests : {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
