{% extends 'stagiaires/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- En-tête -->
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-handshake me-2"></i>
                            {{ title }}
                        </h4>
                        <div>
                            <a href="{% url 'stagiaire_detail' stagiaire.id %}" class="btn btn-outline-light">
                                <i class="fas fa-arrow-left me-1"></i>Retour au profil
                            </a>
                            <a href="{% url 'stagiaires_list' %}" class="btn btn-outline-light ms-2">
                                <i class="fas fa-list me-1"></i>Liste des stagiaires
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-user me-1"></i>Informations du stagiaire</h6>
                            <p><strong>Nom :</strong> {{ stagiaire.nom_complet }}</p>
                            <p><strong>Email :</strong> {{ stagiaire.email }}</p>
                            <p><strong>Service :</strong> {{ stagiaire.service.nom|default:"Non défini" }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-calendar me-1"></i>Informations du stage</h6>
                            <p><strong>Date de début :</strong> {{ stagiaire.date_debut|default:"Non définie" }}</p>
                            <p><strong>Date de fin :</strong> {{ stagiaire.date_fin|default:"Non définie" }}</p>
                            <p><strong>Durée :</strong> {{ stagiaire.duree_stage|default:"Non définie" }}</p>
                            <p><strong>Établissement :</strong> {{ stagiaire.etablissement|default:"Non défini" }}</p>
                            <p><strong>Spécialité :</strong> {{ stagiaire.specialite|default:"Non définie" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section CV du stagiaire (compacte) -->
            <div class="card shadow mb-4">
                <div class="card-header bg-info text-white py-2">
                    <h6 class="mb-0">
                        <i class="fas fa-file-pdf me-2"></i>
                        CV du stagiaire
                    </h6>
                </div>
                <div class="card-body py-3">
                    {% if stagiaire.cv %}
                        <div class="row align-items-center">
                            <div class="col-md-7">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file-pdf fa-lg text-danger me-2"></i>
                                    <div>
                                        <small class="fw-bold">{{ stagiaire.cv.name|default:"CV.pdf" }}</small><br>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            Téléversé le {{ stagiaire.date_creation|date:"d/m/Y" }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-5 text-end">
                                <button type="button" class="btn btn-sm btn-primary me-1" onclick="toggleCvViewer()">
                                    <i class="fas fa-eye me-1"></i>Consulter
                                </button>
                                <a href="{{ stagiaire.cv.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-download me-1"></i>Télécharger
                                </a>
                            </div>
                        </div>

                        <!-- Zone d'affichage du PDF (masquée par défaut) -->
                        <div id="cvViewer" class="mt-3" style="display: none;">
                            <div class="border rounded p-2" style="background-color: #f8f9fa;">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-file-pdf me-1"></i>Aperçu du CV
                                    </small>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleCvViewer()">
                                        <i class="fas fa-times me-1"></i>Fermer
                                    </button>
                                </div>
                                <div class="text-center">
                                    {% if stagiaire.cv.name|slice:"-4:" == ".pdf" %}
                                        <embed src="{{ stagiaire.cv.url }}" type="application/pdf" width="100%" height="400px" />
                                    {% else %}
                                        <div class="py-4">
                                            <i class="fas fa-file fa-3x text-muted mb-3"></i>
                                            <h6>Aperçu non disponible</h6>
                                            <p class="text-muted">Ce type de fichier ne peut pas être affiché directement.</p>
                                            <a href="{{ stagiaire.cv.url }}" target="_blank" class="btn btn-primary">
                                                <i class="fas fa-download me-1"></i>Télécharger le fichier
                                            </a>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="text-center py-2">
                            <i class="fas fa-file-excel fa-2x text-muted mb-2"></i>
                            <small class="text-muted d-block">Aucun CV disponible</small>
                            <small class="text-muted">Ce stagiaire n'a pas encore téléversé son CV.</small>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Formulaire d'ajout de tâche -->
            <div class="card shadow mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        Ajouter une nouvelle tâche
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.titre.id_for_label }}" class="form-label">Titre de la tâche *</label>
                                    {{ form.titre }}
                                </div>
                                <div class="mb-3">
                                    <label for="{{ form.priorite.id_for_label }}" class="form-label">Priorité</label>
                                    {{ form.priorite }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.date_debut.id_for_label }}" class="form-label">Date de début</label>
                                    {{ form.date_debut }}
                                </div>
                                <div class="mb-3">
                                    <label for="{{ form.date_fin_prevue.id_for_label }}" class="form-label">Date limite</label>
                                    {{ form.date_fin_prevue }}
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            {{ form.description }}
                        </div>
                        <button type="submit" name="add_tache" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>Ajouter la tâche
                        </button>
                    </form>
                </div>
            </div>

            <!-- Liste des tâches -->
            <div class="card shadow mb-4">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>
                        Tâches assignées ({{ taches.count }})
                    </h5>
                    {% if taches.exists %}
                    <form method="post" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" name="send_email" class="btn btn-warning" 
                                onclick="return confirm('Envoyer les tâches par email à {{ stagiaire.email }} ?')">
                            <i class="fas fa-envelope me-1"></i>Envoyer par email
                        </button>
                    </form>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if taches.exists %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Titre</th>
                                        <th>Description</th>
                                        <th>Priorité</th>
                                        <th>Statut</th>
                                        <th>Date limite</th>
                                        <th>Créée le</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for tache in taches %}
                                    <tr>
                                        <td><strong>{{ tache.titre }}</strong></td>
                                        <td>{{ tache.description|default:"Non spécifiée"|truncatechars:50 }}</td>
                                        <td>
                                            {% if tache.priorite == 'HAUTE' %}
                                                <span class="badge bg-danger">{{ tache.get_priorite_display }}</span>
                                            {% elif tache.priorite == 'MOYENNE' %}
                                                <span class="badge bg-warning">{{ tache.get_priorite_display }}</span>
                                            {% else %}
                                                <span class="badge bg-success">{{ tache.get_priorite_display }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if tache.statut == 'A_FAIRE' %}
                                                <span class="badge bg-secondary">{{ tache.get_statut_display }}</span>
                                            {% elif tache.statut == 'EN_COURS' %}
                                                <span class="badge bg-primary">{{ tache.get_statut_display }}</span>
                                            {% elif tache.statut == 'TERMINEE' %}
                                                <span class="badge bg-success">{{ tache.get_statut_display }}</span>
                                            {% else %}
                                                <span class="badge bg-dark">{{ tache.get_statut_display }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if tache.date_fin_prevue %}
                                                {{ tache.date_fin_prevue|date:"d/m/Y" }}
                                            {% else %}
                                                <span class="text-muted">Non définie</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ tache.date_creation|date:"d/m/Y H:i" }}</td>
                                        <td>
                                            {% if tache.statut == 'A_FAIRE' %}
                                            <a href="{% url 'demarrer_tache' tache.id %}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-play me-1"></i>Démarrer
                                            </a>
                                            {% elif tache.statut == 'EN_COURS' %}
                                            <a href="{% url 'terminer_tache' tache.id %}" class="btn btn-sm btn-success">
                                                <i class="fas fa-check me-1"></i>Terminer
                                            </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                            <h5>Aucune tâche assignée</h5>
                            <p class="text-muted">Commencez par ajouter une tâche pour ce stagiaire.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    border-top: none;
    font-weight: 600;
}

.badge {
    font-size: 0.85em;
}

.card {
    border: none;
    border-radius: 10px;
}

.btn {
    border-radius: 6px;
}

#cvViewer {
    transition: all 0.3s ease;
}
</style>

<script>
function toggleCvViewer() {
    const cvViewer = document.getElementById('cvViewer');
    const btnConsulter = document.querySelector('#btnConsulterCv, button[onclick="toggleCvViewer()"]');

    if (cvViewer.style.display === 'none' || cvViewer.style.display === '') {
        // Afficher le CV
        cvViewer.style.display = 'block';
        if (btnConsulter) {
            btnConsulter.innerHTML = '<i class="fas fa-eye-slash me-1"></i>Masquer';
            btnConsulter.classList.remove('btn-primary');
            btnConsulter.classList.add('btn-secondary');
        }

        // Scroll vers le CV pour le rendre visible
        setTimeout(() => {
            cvViewer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }, 100);
    } else {
        // Masquer le CV
        cvViewer.style.display = 'none';
        if (btnConsulter) {
            btnConsulter.innerHTML = '<i class="fas fa-eye me-1"></i>Consulter';
            btnConsulter.classList.remove('btn-secondary');
            btnConsulter.classList.add('btn-primary');
        }
    }
}

// Fermer le CV si on clique en dehors
document.addEventListener('click', function(event) {
    const cvViewer = document.getElementById('cvViewer');
    const btnConsulter = document.querySelector('button[onclick="toggleCvViewer()"]');

    // Si le CV est affiché et qu'on clique en dehors
    if (cvViewer && cvViewer.style.display === 'block') {
        if (!cvViewer.contains(event.target) && !btnConsulter.contains(event.target)) {
            // Ne pas fermer automatiquement pour éviter les fermetures accidentelles
            // toggleCvViewer();
        }
    }
});
</script>
{% endblock %}