{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- En-tête -->
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-handshake me-2"></i>
                            {{ title }}
                        </h4>
                        <div>
                            <a href="{% url 'stagiaire_detail' stagiaire.id %}" class="btn btn-outline-light">
                                <i class="fas fa-arrow-left me-1"></i>Retour au profil
                            </a>
                            <a href="{% url 'stagiaires_list' %}" class="btn btn-outline-light ms-2">
                                <i class="fas fa-list me-1"></i>Liste des stagiaires
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-user me-1"></i>Informations du stagiaire</h6>
                            <p><strong>Nom :</strong> {{ stagiaire.nom_complet }}</p>
                            <p><strong>Email :</strong> {{ stagiaire.email }}</p>
                            <p><strong>Service :</strong> {{ stagiaire.service.nom|default:"Non défini" }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-calendar me-1"></i>Informations du stage</h6>
                            <p><strong>Date de début :</strong> {{ stagiaire.date_debut|default:"Non définie" }}</p>
                            <p><strong>Date de fin :</strong> {{ stagiaire.date_fin|default:"Non définie" }}</p>
                            <p><strong>Durée :</strong> {{ stagiaire.duree_stage|default:"Non définie" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Formulaire d'ajout de tâche -->
            <div class="card shadow mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        Ajouter une nouvelle tâche
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.titre.id_for_label }}" class="form-label">Titre de la tâche *</label>
                                    {{ form.titre }}
                                </div>
                                <div class="mb-3">
                                    <label for="{{ form.priorite.id_for_label }}" class="form-label">Priorité</label>
                                    {{ form.priorite }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.date_debut.id_for_label }}" class="form-label">Date de début</label>
                                    {{ form.date_debut }}
                                </div>
                                <div class="mb-3">
                                    <label for="{{ form.date_fin_prevue.id_for_label }}" class="form-label">Date limite</label>
                                    {{ form.date_fin_prevue }}
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            {{ form.description }}
                        </div>
                        <button type="submit" name="add_tache" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>Ajouter la tâche
                        </button>
                    </form>
                </div>
            </div>

            <!-- Liste des tâches -->
            <div class="card shadow mb-4">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>
                        Tâches assignées ({{ taches.count }})
                    </h5>
                    {% if taches.exists %}
                    <form method="post" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" name="send_email" class="btn btn-warning" 
                                onclick="return confirm('Envoyer les tâches par email à {{ stagiaire.email }} ?')">
                            <i class="fas fa-envelope me-1"></i>Envoyer par email
                        </button>
                    </form>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if taches.exists %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Titre</th>
                                        <th>Description</th>
                                        <th>Priorité</th>
                                        <th>Statut</th>
                                        <th>Date limite</th>
                                        <th>Créée le</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for tache in taches %}
                                    <tr>
                                        <td><strong>{{ tache.titre }}</strong></td>
                                        <td>{{ tache.description|default:"Non spécifiée"|truncatechars:50 }}</td>
                                        <td>
                                            {% if tache.priorite == 'HAUTE' %}
                                                <span class="badge bg-danger">{{ tache.get_priorite_display }}</span>
                                            {% elif tache.priorite == 'MOYENNE' %}
                                                <span class="badge bg-warning">{{ tache.get_priorite_display }}</span>
                                            {% else %}
                                                <span class="badge bg-success">{{ tache.get_priorite_display }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if tache.statut == 'A_FAIRE' %}
                                                <span class="badge bg-secondary">{{ tache.get_statut_display }}</span>
                                            {% elif tache.statut == 'EN_COURS' %}
                                                <span class="badge bg-primary">{{ tache.get_statut_display }}</span>
                                            {% elif tache.statut == 'TERMINEE' %}
                                                <span class="badge bg-success">{{ tache.get_statut_display }}</span>
                                            {% else %}
                                                <span class="badge bg-dark">{{ tache.get_statut_display }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if tache.date_fin_prevue %}
                                                {{ tache.date_fin_prevue|date:"d/m/Y" }}
                                            {% else %}
                                                <span class="text-muted">Non définie</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ tache.date_creation|date:"d/m/Y H:i" }}</td>
                                        <td>
                                            {% if tache.statut == 'A_FAIRE' %}
                                            <a href="{% url 'demarrer_tache' tache.id %}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-play me-1"></i>Démarrer
                                            </a>
                                            {% elif tache.statut == 'EN_COURS' %}
                                            <a href="{% url 'terminer_tache' tache.id %}" class="btn btn-sm btn-success">
                                                <i class="fas fa-check me-1"></i>Terminer
                                            </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                            <h5>Aucune tâche assignée</h5>
                            <p class="text-muted">Commencez par ajouter une tâche pour ce stagiaire.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.table th {
    border-top: none;
    font-weight: 600;
}

.badge {
    font-size: 0.85em;
}

.card {
    border: none;
    border-radius: 10px;
}

.btn {
    border-radius: 6px;
}
</style>
{% endblock %}