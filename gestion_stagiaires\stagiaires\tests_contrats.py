from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import Stagiaire, ContratStage

User = get_user_model()


class ContratStageTestCase(TestCase):
    """Tests pour la fonctionnalité de création automatique de contrats"""

    def setUp(self):
        """Configuration initiale pour les tests"""
        # Créer un utilisateur RH
        self.rh_user = User.objects.create_user(
            username='rh_test',
            email='<EMAIL>',
            password='testpass123',
            role='RH',
            first_name='RH',
            last_name='Test'
        )

        # Créer un utilisateur encadrant
        self.encadrant_user = User.objects.create_user(
            username='encadrant_test',
            email='<EMAIL>',
            password='testpass123',
            role='ENCADRANT',
            first_name='Encadrant',
            last_name='Test'
        )
        
        # Créer un stagiaire avec convention validée
        from datetime import date, timedelta
        self.stagiaire = Stagiaire.objects.create(
            nom='Dupont',
            prenom='Jean',
            email='<EMAIL>',
            telephone='0123456789',
            date_naissance=date(2000, 1, 1),
            departement='IT',
            date_debut=timezone.now().date(),
            date_fin=timezone.now().date() + timedelta(days=90),
            statut_convention='VALIDEE',
            date_validation_convention=timezone.now(),
            validee_par=self.rh_user,
            encadrant=self.encadrant_user
        )
        
        self.client = Client()

    def test_contrat_creation_form_access(self):
        """Test que le formulaire de création de contrat est accessible"""
        self.client.login(username='rh_test', password='testpass123')
        
        url = reverse('contrat_create', kwargs={'stagiaire_id': self.stagiaire.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Création du Contrat de Stage')
        self.assertContains(response, self.stagiaire.nom_complet)

    def test_contrat_creation_post(self):
        """Test de création d'un contrat via POST"""
        self.client.login(username='rh_test', password='testpass123')
        
        url = reverse('contrat_create', kwargs={'stagiaire_id': self.stagiaire.id})
        data = {
            'type_contrat': 'STAGE_OBLIGATOIRE',
            'titre_stage': 'Stage en développement web',
            'description_missions': 'Développement d\'applications web',
            'objectifs_pedagogiques': 'Apprendre les technologies web modernes',
            'competences_acquises': 'HTML, CSS, JavaScript, Python',
            'duree_hebdomadaire': 35,
            'gratification_mensuelle': 600.00,
            'avantages': 'Tickets restaurant',
            'encadrant_entreprise': self.encadrant_user.id,
            'tuteur_pedagogique': 'Prof. Martin',
            'commentaires_admin': 'Excellent stagiaire'
        }
        
        response = self.client.post(url, data)
        
        # Vérifier que le contrat a été créé
        self.assertTrue(ContratStage.objects.filter(stagiaire=self.stagiaire).exists())
        
        contrat = ContratStage.objects.get(stagiaire=self.stagiaire)
        self.assertEqual(contrat.titre_stage, 'Stage en développement web')
        self.assertEqual(contrat.duree_hebdomadaire, 35)
        self.assertEqual(float(contrat.gratification_mensuelle), 600.00)
        self.assertEqual(contrat.cree_par, self.rh_user)

    def test_contrat_detail_view(self):
        """Test de la vue détail d'un contrat"""
        # Créer un contrat
        contrat = ContratStage.objects.create(
            stagiaire=self.stagiaire,
            type_contrat='STAGE_OBLIGATOIRE',
            titre_stage='Stage test',
            description_missions='Missions de test',
            objectifs_pedagogiques='Objectifs de test',
            duree_hebdomadaire=35,
            gratification_mensuelle=500.00,
            encadrant_entreprise=self.encadrant_user,
            cree_par=self.rh_user
        )
        
        self.client.login(username='rh_test', password='testpass123')
        
        url = reverse('contrat_detail', kwargs={'contrat_id': contrat.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, contrat.reference)
        self.assertContains(response, contrat.titre_stage)
        self.assertContains(response, self.stagiaire.nom_complet)

    def test_contrat_reference_generation(self):
        """Test de génération automatique de référence"""
        contrat = ContratStage.objects.create(
            stagiaire=self.stagiaire,
            type_contrat='STAGE_OBLIGATOIRE',
            titre_stage='Stage test',
            description_missions='Missions de test',
            objectifs_pedagogiques='Objectifs de test',
            duree_hebdomadaire=35,
            cree_par=self.rh_user
        )
        
        # Vérifier que la référence a été générée
        self.assertTrue(contrat.reference.startswith('CTR-'))
        self.assertIn(str(timezone.now().year), contrat.reference)

    def test_contrat_status_update(self):
        """Test de mise à jour du statut selon les signatures"""
        contrat = ContratStage.objects.create(
            stagiaire=self.stagiaire,
            type_contrat='STAGE_OBLIGATOIRE',
            titre_stage='Stage test',
            description_missions='Missions de test',
            objectifs_pedagogiques='Objectifs de test',
            duree_hebdomadaire=35,
            cree_par=self.rh_user
        )
        
        # Initialement en brouillon
        self.assertEqual(contrat.statut, 'BROUILLON')

        # Changer le statut pour déclencher la logique de signature
        contrat.statut = 'EN_ATTENTE_SIGNATURE'
        contrat.save()

        contrat.refresh_from_db()
        self.assertEqual(contrat.statut, 'EN_ATTENTE_SIGNATURE')

        # Ajouter la signature RH (seule signature requise)
        contrat.signature_rh = True
        contrat.date_signature_rh = timezone.now()
        contrat.signature_rh_par = self.rh_user
        contrat.save()

        contrat.refresh_from_db()
        self.assertEqual(contrat.statut, 'ENTIEREMENT_SIGNE')

    def test_permission_access_control(self):
        """Test du contrôle d'accès basé sur les rôles"""
        # Créer un utilisateur sans rôle spécifique (pour tester les permissions)
        stagiaire_user = User.objects.create_user(
            username='stagiaire_test',
            email='<EMAIL>',
            password='testpass123',
            first_name='Stagiaire',
            last_name='Test'
        )
        
        # Tenter d'accéder à la création de contrat en tant que stagiaire
        self.client.login(username='stagiaire_test', password='testpass123')
        
        url = reverse('contrat_create', kwargs={'stagiaire_id': self.stagiaire.id})
        response = self.client.get(url)
        
        # Devrait être redirigé ou refusé (selon l'implémentation)
        self.assertNotEqual(response.status_code, 200)

    def test_contracts_list_integration(self):
        """Test d'intégration avec la liste des contrats"""
        # Créer un contrat
        contrat = ContratStage.objects.create(
            stagiaire=self.stagiaire,
            type_contrat='STAGE_OBLIGATOIRE',
            titre_stage='Stage test',
            description_missions='Missions de test',
            objectifs_pedagogiques='Objectifs de test',
            duree_hebdomadaire=35,
            gratification_mensuelle=500.00,
            cree_par=self.rh_user
        )
        
        self.client.login(username='rh_test', password='testpass123')
        
        # Accéder à la liste des contrats
        url = reverse('contracts')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, contrat.reference)
        self.assertContains(response, self.stagiaire.nom_complet)

    def test_contrat_signature_rh_only(self):
        """Test que seul le RH peut signer les contrats"""
        # Créer un contrat
        contrat = ContratStage.objects.create(
            stagiaire=self.stagiaire,
            type_contrat='STAGE_OBLIGATOIRE',
            titre_stage='Stage de développement',
            description_missions='Développement d\'applications',
            objectifs_pedagogiques='Apprendre le développement web',
            duree_hebdomadaire=35,
            gratification_mensuelle=600.00,
            encadrant_entreprise=self.encadrant_user,
            cree_par=self.rh_user
        )

        # Test avec utilisateur RH
        self.client.login(username='rh_test', password='testpass123')
        response = self.client.post(f'/contrats/{contrat.id}/sign/')
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertTrue(data['success'])

        # Vérifier que le contrat est signé
        contrat.refresh_from_db()
        self.assertTrue(contrat.signature_rh)
        self.assertEqual(contrat.signature_rh_par, self.rh_user)
        self.assertEqual(contrat.statut, 'ENTIEREMENT_SIGNE')

        # Test avec utilisateur non-RH (encadrant)
        contrat2 = ContratStage.objects.create(
            stagiaire=self.stagiaire,
            type_contrat='STAGE_OBLIGATOIRE',
            titre_stage='Stage de test 2',
            description_missions='Test',
            objectifs_pedagogiques='Test',
            duree_hebdomadaire=35,
            encadrant_entreprise=self.encadrant_user,
            cree_par=self.rh_user
        )

        self.client.login(username='encadrant_test', password='testpass123')
        response = self.client.post(f'/contrats/{contrat2.id}/sign/')
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertFalse(data['success'])
        self.assertIn('Accès non autorisé', data['error'])

        # Vérifier que le contrat n'est pas signé
        contrat2.refresh_from_db()
        self.assertFalse(contrat2.signature_rh)
