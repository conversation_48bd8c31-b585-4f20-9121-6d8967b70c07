# Generated by Django 5.1.6 on 2025-07-10 17:30

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stagiaires', '0004_populate_thematique'),
    ]

    operations = [
        migrations.AddField(
            model_name='sujet',
            name='competences_requises',
            field=models.TextField(blank=True, null=True, verbose_name='Compétences requises'),
        ),
        migrations.AddField(
            model_name='sujet',
            name='duree_recommandee',
            field=models.PositiveIntegerField(default=30, verbose_name='Durée recommandée (jours)'),
        ),
        migrations.AlterField(
            model_name='sujet',
            name='date_creation',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Date de création'),
        ),
        migrations.AlterField(
            model_name='sujet',
            name='date_modification',
            field=models.DateTimeField(auto_now=True, verbose_name='Date de modification'),
        ),
        migrations.AlterField(
            model_name='sujet',
            name='description',
            field=models.TextField(default='', verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='sujet',
            name='niveau_difficulte',
            field=models.CharField(choices=[('FACILE', 'Facile'), ('MOYEN', 'Moyen'), ('DIFFICILE', 'Difficile')], default='MOYEN', max_length=20, verbose_name='Niveau de difficulté'),
        ),
        migrations.AlterField(
            model_name='sujet',
            name='thematique',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='sujets', to='stagiaires.thematique', verbose_name='Thématique'),
            preserve_default=False,
        ),
    ]
