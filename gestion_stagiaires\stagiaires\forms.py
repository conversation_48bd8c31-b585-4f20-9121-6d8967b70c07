from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import (CustomUser, Stagiaire, Tache, Mission, RapportStage, 
                    ContratStage, Thematique, Sujet, DureeEstimee, Service)
from django.db.models import Q  # Ajoutez cet import en haut du fichier
from django.db import models

# Si le modèle Service n'existe pas encore, vous devez le commenter ou le supprimer
# class ServiceForm(forms.ModelForm):
#     """Formulaire pour les services"""
#     class Meta:
#         model = Service
#         fields = ['nom', 'description', 'responsable', 'email_contact', 'telephone_contact']
#         widgets = {
#             'nom': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Nom du service'}),
#             'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Description du service'}),
#             'responsable': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Nom du responsable'}),
#             'email_contact': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Email de contact'}),
#             'telephone_contact': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Téléphone de contact'}),
#         }


        
class CustomUserCreationForm(UserCreationForm):
    """Formulaire d'inscription personnalisé avec le champ rôle"""

    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Adresse email'
        })
    )

    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Prénom'
        })
    )

    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Nom'
        })
    )

    role = forms.ChoiceField(
        choices=[('ADMIN', 'Administrateur'), ('RH', 'Gestionnaire RH'), ('ENCADRANT', 'Encadrant')],
        required=True,
        widget=forms.Select(attrs={
            'class': 'form-control',
            'onchange': 'toggleServiceField(this.value)'
        })
    )

    service = forms.ModelChoiceField(
        queryset=Service.objects.filter(actif=True),
        required=False,
        empty_label="Sélectionner un service",
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        help_text="Obligatoire pour les encadrants"
    )

    class Meta:
        model = CustomUser
        fields = ('username', 'email', 'first_name', 'last_name', 'role', 'service', 'password1', 'password2')
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': "Nom d'utilisateur"
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Mot de passe'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Confirmer le mot de passe'
        })

    def clean(self):
        cleaned_data = super().clean()
        role = cleaned_data.get('role')
        service = cleaned_data.get('service')

        # Rendre le service obligatoire pour les encadrants
        if role == 'ENCADRANT' and not service:
            raise forms.ValidationError("Le service est obligatoire pour les encadrants.")

        return cleaned_data

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.role = self.cleaned_data['role']
        user.service = self.cleaned_data.get('service')

        # Les utilisateurs créés via l'inscription ne sont jamais admin
        user.is_superuser = False
        user.is_staff = False

        if commit:
            user.save()
        return user


class CustomAuthenticationForm(AuthenticationForm):
    """Formulaire de connexion personnalisé"""
    
    username = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': "Nom d'utilisateur",
            'autofocus': True
        })
    )
    
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Mot de passe'
        })
    )


class StagiaireForm(forms.ModelForm):
    
    class Meta:
        model = Stagiaire
        fields = [
            'nom', 'prenom', 'email', 'telephone', 'date_naissance',
            'departement', 'service', 'encadrant', 'date_debut', 'date_fin',
            'etablissement', 'niveau_etude', 'specialite', 'statut',
            'thematique', 'sujet', 'technologies', 'cv', 'assurance', 'convention_stage'
        ]
        widgets = {
            'nom': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom du stagiaire'
            }),
            'prenom': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Prénom du stagiaire'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'telephone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+33 1 23 45 67 89'
            }),
            'date_naissance': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'departement': forms.Select(attrs={
                'class': 'form-control'
            }),
            'service': forms.Select(attrs={
                'class': 'form-control'
            }),
            'encadrant': forms.Select(attrs={
                'class': 'form-control'
            }),
            'date_debut': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'date_fin': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'etablissement': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom de l\'établissement'
            }),
            'niveau_etude': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Master 2, Licence 3, etc.'
            }),
            'specialite': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Informatique, Marketing, etc.'
            }),
            'thematique': forms.Select(attrs={
                'class': 'form-control'
            }),
            'sujet': forms.Select(attrs={
                'class': 'form-control'
            }),
            'technologies': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Ex: Python, Django, JavaScript, React, SQL...'
            }),
            'cv': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx',
                'title': 'Télécharger le CV du stagiaire'
            }),
            'assurance': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx,.jpg,.jpeg,.png',
                'title': 'Télécharger l\'assurance responsabilité civile'
            }),
            'convention_stage': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx',
                'title': 'Télécharger la convention de stage'
            }),
        }

    def __init__(self, *args, **kwargs):
        user_role = kwargs.pop('user_role', None)
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Limiter les encadrants selon le service sélectionné
        if 'encadrant' in self.fields:
            if user and hasattr(user, 'role') and user.role == 'ENCADRANT' and hasattr(user, 'service') and user.service:
                # Pour les encadrants, montrer seulement les encadrants de leur service
                self.fields['encadrant'].queryset = CustomUser.objects.filter(
                    role='ENCADRANT',
                    service=user.service,
                    is_active=True
                )
                self.fields['encadrant'].empty_label = f"Sélectionner un encadrant du service {user.service.nom}"
            else:
                # Pour admin et RH, montrer tous les encadrants
                self.fields['encadrant'].queryset = CustomUser.objects.filter(role='ENCADRANT', is_active=True)
                self.fields['encadrant'].empty_label = "Sélectionner un encadrant"
        
        # Limiter les services aux services actifs
        if 'service' in self.fields:
            self.fields['service'].queryset = Service.objects.filter(actif=True)
            self.fields['service'].empty_label = "Sélectionner un service (optionnel)"
        
        # Configuration des champs thématique et sujet pour tous les rôles
        if 'thematique' in self.fields:
            self.fields['thematique'].queryset = Thematique.objects.filter(active=True)
            self.fields['thematique'].empty_label = "Sélectionner une thématique (optionnel)"

        if 'sujet' in self.fields:
            self.fields['sujet'].queryset = Sujet.objects.filter(actif=True)
            self.fields['sujet'].empty_label = "Sélectionner un sujet (optionnel)"
        # Si l'utilisateur n'est pas un RH, supprimer les champs de documents
        else:
            if 'cv' in self.fields:
                del self.fields['cv']
            if 'assurance' in self.fields:
                del self.fields['assurance']
            if 'convention_stage' in self.fields:
                del self.fields['convention_stage']
            

            
            # Si l'utilisateur est un encadrant, filtrer les sujets et thématiques
            if user_role == 'ENCADRANT' and user:
                if 'sujet' in self.fields:
                    # Filtrer les sujets associés à cet encadrant
                    self.fields['sujet'].queryset = Sujet.objects.filter(
                        Q(encadrant=user) | Q(cree_par=user),
                        actif=True
                    ).distinct()

    def clean(self):
        cleaned_data = super().clean()
        date_debut = cleaned_data.get('date_debut')
        date_fin = cleaned_data.get('date_fin')

        if date_debut and date_fin:
            if date_fin <= date_debut:
                raise forms.ValidationError("La date de fin doit être postérieure à la date de début.")

        return cleaned_data

    # Supprimer cette méthode clean() qui est incorrecte et dupliquée
    # def clean(self):
    #     username = self.cleaned_data.get('username')
    #     password = self.cleaned_data.get('password')
    #
    #     if username and password:
    #         self.user_cache = authenticate(
    #             self.request,
    #             username=username,
    #             password=password
    #         )
    #         if self.user_cache is None:
    #             raise forms.ValidationError(
    #                 "Nom d'utilisateur ou mot de passe incorrect.",
    #                 code='invalid_login',
    #             )
    #         else:
    #             self.confirm_login_allowed(self.user_cache)
    #
    #     return self.cleaned_data


class ConventionUploadForm(forms.ModelForm):
    """Formulaire pour l'upload et la validation des conventions de stage"""

    class Meta:
        model = Stagiaire
        fields = ['convention_stage', 'commentaire_convention']
        widgets = {
            'convention_stage': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx'
            }),
            'commentaire_convention': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Commentaires sur la convention...'
            })
        }


class ConventionValidationForm(forms.ModelForm):
    """Formulaire pour la validation des conventions par les RH"""

    class Meta:
        model = Stagiaire
        fields = ['statut_convention', 'commentaire_convention']
        widgets = {
            'statut_convention': forms.Select(attrs={
                'class': 'form-control'
            }),
            'commentaire_convention': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Commentaires de validation...'
            })
        }


class ContratStageForm(forms.ModelForm):
    """Formulaire pour créer/modifier les contrats de stage"""

    class Meta:
        model = ContratStage
        fields = [
            'type_contrat', 'titre_stage', 'description_missions',
            'objectifs_pedagogiques', 'competences_acquises',
            'duree_hebdomadaire', 'gratification_mensuelle', 'avantages',
            'encadrant_entreprise', 'tuteur_pedagogique', 'commentaires_admin'
        ]
        widgets = {
            'type_contrat': forms.Select(attrs={
                'class': 'form-control'
            }),
            'titre_stage': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ex: Stage en développement web'
            }),
            'description_missions': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Décrivez les missions principales du stagiaire...'
            }),
            'objectifs_pedagogiques': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Objectifs pédagogiques du stage...'
            }),
            'competences_acquises': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Compétences que le stagiaire va acquérir...'
            }),
            'duree_hebdomadaire': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '40',
                'placeholder': '35'
            }),
            'gratification_mensuelle': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'avantages': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Tickets restaurant, transport, etc.'
            }),
            'encadrant_entreprise': forms.Select(attrs={
                'class': 'form-control'
            }),
            'tuteur_pedagogique': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom du tuteur pédagogique'
            }),
            'commentaires_admin': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Commentaires administratifs...'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filtrer les encadrants disponibles
        self.fields['encadrant_entreprise'].queryset = CustomUser.objects.filter(
            role='ENCADRANT', is_active=True
        )


class TacheStageForm(forms.ModelForm):
    """Formulaire pour créer/modifier une tâche de stage"""
    
    class Meta:
        model = Tache
        fields = ['titre', 'description', 'priorite', 'date_debut', 'date_fin_prevue']
        widgets = {
            'titre': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Titre de la tâche'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Description détaillée de la tâche...'
            }),
            'priorite': forms.Select(attrs={
                'class': 'form-select'
            }),
            'date_debut': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'date_fin_prevue': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            })
        }
    
    def clean(self):
        cleaned_data = super().clean()
        date_debut = cleaned_data.get('date_debut')
        date_fin_prevue = cleaned_data.get('date_fin_prevue')
        
        if date_debut and date_fin_prevue and date_debut > date_fin_prevue:
            self.add_error('date_fin_prevue', "La date de fin prévue doit être postérieure à la date de début.")
        
        return cleaned_data


class EvaluationStageForm(forms.ModelForm):
    """Formulaire pour l'évaluation du stage par l'encadrant"""

    class Meta:
        model = Stagiaire
        fields = ['statut_taches', 'evaluation_encadrant', 'note_finale']
        widgets = {
            'statut_taches': forms.Select(attrs={
                'class': 'form-control'
            }),
            'evaluation_encadrant': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 6,
                'placeholder': 'Évaluation détaillée du stagiaire...'
            }),
            'note_finale': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'max': '20',
                'step': '0.5',
                'placeholder': 'Note sur 20'
            })
        }

    def clean_note_finale(self):
        note = self.cleaned_data.get('note_finale')
        if note is not None and (note < 0 or note > 20):
            raise forms.ValidationError("La note doit être comprise entre 0 et 20.")
        return note


class AttestationGenerationForm(forms.Form):
    """Formulaire pour générer une attestation de fin de stage"""

    template_choice = forms.ChoiceField(
        choices=[
            ('standard', 'Modèle standard'),
            ('detaille', 'Modèle détaillé'),
            ('personnalise', 'Modèle personnalisé')
        ],
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="Modèle d'attestation"
    )

    commentaires_supplementaires = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Commentaires supplémentaires à inclure dans l\'attestation...'
        }),
        required=False,
        label="Commentaires supplémentaires"
    )


class MissionForm(forms.ModelForm):
    """Formulaire pour créer/modifier une mission"""

    class Meta:
        model = Mission
        fields = ['titre', 'description', 'objectifs', 'livrables_attendus',
                 'date_debut_prevue', 'date_fin_prevue', 'priorite']
        widgets = {
            'titre': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'objectifs': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'livrables_attendus': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'date_debut_prevue': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'date_fin_prevue': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'priorite': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        self.stagiaire = kwargs.pop('stagiaire', None)
        super().__init__(*args, **kwargs)

        if self.stagiaire:
            # Limiter les dates aux dates du stage
            self.fields['date_debut_prevue'].widget.attrs.update({
                'min': self.stagiaire.date_debut.strftime('%Y-%m-%d'),
                'max': self.stagiaire.date_fin.strftime('%Y-%m-%d')
            })
            self.fields['date_fin_prevue'].widget.attrs.update({
                'min': self.stagiaire.date_debut.strftime('%Y-%m-%d'),
                'max': self.stagiaire.date_fin.strftime('%Y-%m-%d')
            })

    def clean(self):
        cleaned_data = super().clean()
        date_debut = cleaned_data.get('date_debut_prevue')
        date_fin = cleaned_data.get('date_fin_prevue')

        if date_debut and date_fin:
            if date_fin <= date_debut:
                raise forms.ValidationError("La date de fin doit être postérieure à la date de début.")

            if self.stagiaire:
                if date_debut < self.stagiaire.date_debut or date_debut > self.stagiaire.date_fin:
                    raise forms.ValidationError("La date de début doit être dans la période du stage.")
                if date_fin < self.stagiaire.date_debut or date_fin > self.stagiaire.date_fin:
                    raise forms.ValidationError("La date de fin doit être dans la période du stage.")

        return cleaned_data


class SuiviAvancementForm(forms.ModelForm):
    """Formulaire pour le suivi d'avancement d'une mission"""

    class Meta:
        model = Mission
        fields = ['pourcentage_avancement', 'commentaire_avancement', 'statut']
        widgets = {
            'pourcentage_avancement': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0,
                'max': 100,
                'step': 5
            }),
            'commentaire_avancement': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'statut': forms.Select(attrs={'class': 'form-select'}),
        }

    def clean_pourcentage_avancement(self):
        pourcentage = self.cleaned_data.get('pourcentage_avancement')
        if pourcentage is not None and (pourcentage < 0 or pourcentage > 100):
            raise forms.ValidationError("Le pourcentage doit être entre 0 et 100.")
        return pourcentage


class RapportStageForm(forms.ModelForm):
    """Formulaire pour soumettre un rapport de stage"""

    class Meta:
        model = RapportStage
        fields = ['titre', 'description', 'fichier_rapport', 'mission']
        widgets = {
            'titre': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'fichier_rapport': forms.FileInput(attrs={'class': 'form-control', 'accept': '.pdf,.doc,.docx'}),
            'mission': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        self.stagiaire = kwargs.pop('stagiaire', None)
        super().__init__(*args, **kwargs)

        if self.stagiaire:
            # Limiter les missions à celles du stagiaire
            self.fields['mission'].queryset = Mission.objects.filter(stagiaire=self.stagiaire)
            self.fields['mission'].empty_label = "Sélectionner une mission (optionnel)"


class ValidationRapportForm(forms.ModelForm):
    """Formulaire pour valider un rapport par l'encadrant"""

    class Meta:
        model = RapportStage
        fields = ['statut', 'commentaires_encadrant', 'note_rapport']
        widgets = {
            'statut': forms.Select(attrs={'class': 'form-select'}),
            'commentaires_encadrant': forms.Textarea(attrs={'class': 'form-control', 'rows': 5}),
            'note_rapport': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 0,
                'max': 20,
                'step': 0.5
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Limiter les choix de statut pour la validation
        self.fields['statut'].choices = [
            ('EN_REVISION', 'En révision'),
            ('VALIDE', 'Validé'),
            ('REJETE', 'Rejeté'),
        ]

    def clean_note_rapport(self):
        note = self.cleaned_data.get('note_rapport')
        if note is not None and (note < 0 or note > 20):
            raise forms.ValidationError("La note doit être entre 0 et 20.")
        return note


class ServiceForm(forms.ModelForm):
    """Formulaire pour créer/modifier un service"""

    class Meta:
        model = Service
        fields = ['nom', 'code_service', 'description', 'responsable', 'actif']
        widgets = {
            'nom': forms.TextInput(attrs={'class': 'form-control'}),
            'code_service': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'responsable': forms.Select(attrs={'class': 'form-select'}),
            'actif': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Filtrer les responsables pour n'afficher que les utilisateurs actifs avec des rôles appropriés
        self.fields['responsable'].queryset = CustomUser.objects.filter(
            is_active=True, 
            role__in=['ADMIN', 'ENCADRANT']
        ).order_by('last_name', 'first_name')
        self.fields['responsable'].required = False
        
        # Si l'utilisateur est un encadrant, limiter les options
        if user and hasattr(user, 'role') and user.role == 'ENCADRANT' and user.service:
            # Pré-remplir avec le service de l'encadrant et rendre en lecture seule
            if 'code_service' in self.fields:
                self.fields['code_service'].widget.attrs['readonly'] = True
                if self.instance and self.instance.code_service:
                    self.fields['code_service'].initial = self.instance.code_service
            
            # Limiter les responsables aux utilisateurs du même service
            if 'responsable' in self.fields:
                self.fields['responsable'].queryset = CustomUser.objects.filter(
                    models.Q(service=user.service) | 
                    models.Q(id=user.id)
                ).distinct()

    def clean_nom(self):
        """Validation du nom du service"""
        nom = self.cleaned_data.get('nom')
        if nom:
            # Vérifier si un service avec ce nom existe déjà (en ignorant la casse)
            existing_service = Service.objects.filter(nom__iexact=nom)

            # Si on modifie un service existant, exclure le service actuel de la vérification
            if self.instance and self.instance.pk:
                existing_service = existing_service.exclude(pk=self.instance.pk)

            if existing_service.exists():
                raise forms.ValidationError(f'Un service avec le nom "{nom}" existe déjà. Veuillez choisir un autre nom.')

        return nom

    def clean_code_service(self):
        """Validation du code du service"""
        code_service = self.cleaned_data.get('code_service')
        if code_service:
            # Vérifier si un service avec ce code existe déjà (en ignorant la casse)
            existing_service = Service.objects.filter(code_service__iexact=code_service)

            # Si on modifie un service existant, exclure le service actuel de la vérification
            if self.instance and self.instance.pk:
                existing_service = existing_service.exclude(pk=self.instance.pk)

            if existing_service.exists():
                raise forms.ValidationError(f'Un service avec le code "{code_service}" existe déjà. Veuillez choisir un autre code.')

        return code_service


class StagiaireEditForm(forms.ModelForm):
    """Formulaire d'édition de stagiaire avec upload de rapport"""

    class Meta:
        model = Stagiaire
        fields = [
            'nom', 'prenom', 'email', 'telephone', 'date_naissance',
            'departement', 'service', 'encadrant', 'date_debut', 'date_fin',
            'etablissement', 'niveau_etude', 'specialite', 'statut',
            'technologies', 'cv', 'assurance', 'convention_stage',
            'rapport_stage', 'commentaire_rapport', 'description_taches',
            'statut_taches', 'evaluation_encadrant'
        ]
        widgets = {
            'nom': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom du stagiaire'
            }),
            'prenom': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Prénom du stagiaire'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'telephone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+33 1 23 45 67 89'
            }),
            'date_naissance': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'departement': forms.Select(attrs={
                'class': 'form-control'
            }),
            'service': forms.Select(attrs={
                'class': 'form-control'
            }),
            'encadrant': forms.Select(attrs={
                'class': 'form-control'
            }),
            'date_debut': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'date_fin': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'etablissement': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom de l\'établissement'
            }),
            'niveau_etude': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ex: Master 2, Licence 3...'
            }),
            'specialite': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Spécialité d\'étude'
            }),
            'statut': forms.Select(attrs={
                'class': 'form-control'
            }),
            'technologies': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Technologies utilisées (séparées par des virgules)'
            }),
            'cv': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx'
            }),
            'assurance': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx'
            }),
            'convention_stage': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx'
            }),
            'rapport_stage': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx'
            }),
            'commentaire_rapport': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Commentaire sur le rapport de stage'
            }),
            'description_taches': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Description des tâches à accomplir'
            }),
            'statut_taches': forms.Select(attrs={
                'class': 'form-control'
            }),
            'evaluation_encadrant': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Évaluation de l\'encadrant'
            }),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Filtrer les encadrants selon le rôle de l'utilisateur
        if user:
            if user.role == 'ENCADRANT':
                # Les encadrants ne peuvent pas changer l'encadrant
                self.fields['encadrant'].widget.attrs['readonly'] = True
                # Limiter les services visibles
                if user.service:
                    self.fields['service'].queryset = Service.objects.filter(id=user.service.id)
            elif user.role in ['RH', 'ADMIN']:
                # RH et Admin peuvent voir tous les encadrants
                self.fields['encadrant'].queryset = CustomUser.objects.filter(
                    role='ENCADRANT', is_active=True
                )


class StagiaireFormEncadrant(forms.ModelForm):
    """Formulaire pour les encadrants qui ajoutent un stagiaire de leur service"""

    class Meta:
        model = Stagiaire
        fields = [
            'nom', 'prenom', 'email', 'telephone', 'date_naissance',
            'etablissement', 'niveau_etude', 'specialite',
            'date_debut', 'date_fin', 'thematique', 'sujet', 'technologies'
        ]
        widgets = {
            'nom': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom du stagiaire'
            }),
            'prenom': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Prénom du stagiaire'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'telephone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+33 1 23 45 67 89'
            }),
            'date_naissance': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'etablissement': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Nom de l\'établissement'
            }),
            'niveau_etude': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ex: Master 2, Licence 3...'
            }),
            'specialite': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Spécialité d\'étude'
            }),
            'date_debut': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'date_fin': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'thematique': forms.Select(attrs={
                'class': 'form-control'
            }),
            'sujet': forms.Select(attrs={
                'class': 'form-control'
            }),
            'technologies': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Technologies utilisées (séparées par des virgules)'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Si l'utilisateur est un encadrant, configurer le formulaire pour son service
        if user and hasattr(user, 'role') and user.role == 'ENCADRANT':

            # Vérifier que l'encadrant a un service assigné
            if not hasattr(user, 'service') or not user.service:
                # Si pas de service, désactiver le formulaire
                for field_name in self.fields:
                    self.fields[field_name].widget.attrs['disabled'] = True
                return

            # Mapper le service au département correspondant
            service_to_dept_mapping = {
                'informatique': 'IT',
                'marketing': 'MARKETING',
                'ressources humaines': 'RH',
                'rh': 'RH',
                'finance': 'FINANCE',
                'commercial': 'COMMERCIAL',
                'production': 'PRODUCTION'
            }

            service_nom = user.service.nom.lower()
            departement_correspondant = service_to_dept_mapping.get(service_nom, 'IT')

            # Pré-remplir et verrouiller le département selon le service de l'encadrant
            if 'departement' in self.fields:
                # Créer un champ caché pour le département
                self.fields['departement'] = forms.CharField(
                    widget=forms.HiddenInput(),
                    initial=departement_correspondant
                )

            # Ajouter un champ d'information sur le service (lecture seule)
            self.fields['service_info'] = forms.CharField(
                label='Service',
                initial=f"{user.service.nom} (Département: {dict(Stagiaire.DEPARTEMENT_CHOICES).get(departement_correspondant, departement_correspondant)})",
                widget=forms.TextInput(attrs={
                    'class': 'form-control',
                    'readonly': True,
                    'style': 'background-color: #e9ecef;'
                }),
                required=False
            )

            # Filtrer les thématiques selon le service de l'encadrant
            if 'thematique' in self.fields:
                self.fields['thematique'].queryset = Thematique.objects.filter(
                    Q(service=user.service) | Q(service__isnull=True),
                    active=True
                ).distinct()
                self.fields['thematique'].empty_label = "Sélectionner une thématique de votre service"

                # Ajouter une aide contextuelle
                self.fields['thematique'].help_text = f"Thématiques disponibles pour le service {user.service.nom}"

            # Filtrer les sujets selon le service de l'encadrant
            if 'sujet' in self.fields:
                self.fields['sujet'].queryset = Sujet.objects.filter(
                    Q(service=user.service) | Q(encadrant=user) | Q(cree_par=user),
                    actif=True
                ).distinct()
                self.fields['sujet'].empty_label = "Sélectionner un sujet de votre service (optionnel)"

                # Ajouter une aide contextuelle
                self.fields['sujet'].help_text = f"Sujets disponibles pour le service {user.service.nom}"

    def clean(self):
        cleaned_data = super().clean()
        date_debut = cleaned_data.get('date_debut')
        date_fin = cleaned_data.get('date_fin')

        if date_debut and date_fin:
            if date_fin <= date_debut:
                raise forms.ValidationError("La date de fin doit être postérieure à la date de début.")

        return cleaned_data

    def save(self, commit=True, user=None):
        """Sauvegarder le stagiaire en assignant automatiquement le service et l'encadrant"""
        stagiaire = super().save(commit=False)

        # Si un utilisateur encadrant est fourni, l'assigner automatiquement
        if user and hasattr(user, 'role') and user.role == 'ENCADRANT':
            stagiaire.encadrant = user
            stagiaire.cree_par = user

            # Assigner le service de l'encadrant
            if hasattr(user, 'service') and user.service:
                stagiaire.service = user.service

        if commit:
            stagiaire.save()

        return stagiaire

class ThematiqueForm(forms.ModelForm):
    """Formulaire pour créer/modifier une thématique"""
    class Meta:
        model = Thematique
        fields = ['titre', 'description', 'active', 'service']
        widgets = {
            'titre': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'service': forms.Select(attrs={'class': 'form-select'}),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Limiter les services aux services actifs
        if 'service' in self.fields:
            from .models import Service  # Import local pour éviter les importations circulaires
            self.fields['service'].queryset = Service.objects.filter(actif=True)
            self.fields['service'].empty_label = "Sélectionner un service (optionnel)"
        
        # Si l'utilisateur est un encadrant, limiter au service de l'encadrant
        if user and hasattr(user, 'role') and user.role == 'ENCADRANT':
            if hasattr(user, 'service') and user.service:
                # Limiter les choix au service de l'encadrant uniquement
                self.fields['service'].queryset = Service.objects.filter(id=user.service.id)
                self.fields['service'].initial = user.service.id

                # Ajouter un champ informatif en lecture seule
                self.fields['service_info'] = forms.CharField(
                    label='Service',
                    initial=user.service.nom,
                    widget=forms.TextInput(attrs={
                        'class': 'form-control',
                        'readonly': True,
                        'style': 'background-color: #e9ecef;'
                    }),
                    required=False
                )

                # Cacher le champ service original
                self.fields['service'].widget = forms.HiddenInput()
            else:
                # Si l'encadrant n'a pas de service, afficher un message d'erreur
                self.fields['service'].help_text = "Vous devez avoir un service assigné pour créer une thématique."


class SujetForm(forms.ModelForm):
    """Formulaire pour créer/modifier un sujet"""
    class Meta:
        model = Sujet
        fields = [
            'titre', 'description', 'thematique', 'service', 'encadrant',
            'niveau_difficulte', 'duree_recommandee', 'competences_requises', 'actif'
        ]
        widgets = {
            'titre': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Titre du sujet de stage'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Description détaillée du sujet de stage'
            }),
            'thematique': forms.Select(attrs={
                'class': 'form-select',
                'required': 'required'
            }),
            'service': forms.Select(attrs={
                'class': 'form-select'
            }),
            'encadrant': forms.Select(attrs={
                'class': 'form-select'
            }),
            'niveau_difficulte': forms.Select(attrs={
                'class': 'form-select'
            }),
            'duree_recommandee': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1,
                'placeholder': 'Durée en jours'
            }),
            'competences_requises': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Compétences et prérequis nécessaires'
            }),
            'actif': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Filtrer les services actifs
        if 'service' in self.fields:
            self.fields['service'].queryset = Service.objects.filter(actif=True)
            self.fields['service'].empty_label = "Sélectionner un service"
        
        # Filtrer les thématiques actives
        if 'thematique' in self.fields:
            self.fields['thematique'].queryset = Thematique.objects.filter(active=True)
            self.fields['thematique'].empty_label = "Sélectionner une thématique"
        
        # Filtrer les encadrants actifs
        if 'encadrant' in self.fields:
            self.fields['encadrant'].queryset = CustomUser.objects.filter(
                role='ENCADRANT', is_active=True
            )
            self.fields['encadrant'].empty_label = "Sélectionner un encadrant (optionnel)"
            self.fields['encadrant'].required = False  # Rendre le champ optionnel

        # Si l'utilisateur est un encadrant
        if user and hasattr(user, 'role') and user.role == 'ENCADRANT':
            # Pour les encadrants, le service n'est pas requis dans le formulaire
            # Il sera assigné automatiquement côté serveur
            if 'service' in self.fields:
                self.fields['service'].required = False
                if hasattr(user, 'service') and user.service:
                    # Afficher le service en lecture seule
                    self.fields['service_info'] = forms.CharField(
                        label='Service',
                        initial=user.service.nom,
                        widget=forms.TextInput(attrs={
                            'class': 'form-control',
                            'readonly': True,
                            'style': 'background-color: #e9ecef;'
                        }),
                        required=False
                    )
                # Cacher le champ service original
                self.fields['service'].widget = forms.HiddenInput()
                self.fields['service'].initial = user.service.id if hasattr(user, 'service') and user.service else None

            # Pré-sélectionner l'encadrant connecté
            if 'encadrant' in self.fields:
                self.fields['encadrant'].initial = user.id
                self.fields['encadrant'].widget = forms.HiddenInput()

            # Filtrer les thématiques selon le service de l'encadrant
            if 'thematique' in self.fields and hasattr(user, 'service') and user.service:
                self.fields['thematique'].queryset = Thematique.objects.filter(
                    models.Q(service=user.service) | models.Q(service__isnull=True),
                    active=True
                ).distinct()
                self.fields['thematique'].help_text = f"Thématiques disponibles pour le service {user.service.nom}"

        else:
            # Pour les autres rôles (Admin, RH), permettre de choisir l'encadrant
            if 'encadrant' in self.fields:
                # Filtrer par service si un service est sélectionné
                self.fields['encadrant'].queryset = CustomUser.objects.filter(
                    role='ENCADRANT', is_active=True
                )




class DureeEstimeeForm(forms.Form):
    """Formulaire pour choisir la durée estimée d'un stage"""
    
    DUREE_CHOICES = [
        (30, '1 mois'),
        (60, '2 mois'),
        (90, '3 mois'),
        (120, '4 mois'),
        (150, '5 mois'),
        (180, '6 mois'),
    ]
    
    duree = forms.ChoiceField(
        choices=DUREE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label="Durée estimée du stage"
    )
    
    commentaire = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Commentaire sur la durée estimée...'
        }),
        required=False,
        label="Commentaire"
    )

class TacheForm(forms.ModelForm):
    """Formulaire pour créer/modifier une tâche"""
    
    class Meta:
        model = Tache
        fields = ['titre', 'description', 'priorite', 'date_fin_prevue']
        widgets = {
            'titre': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Titre de la tâche'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Description détaillée de la tâche...'
            }),
            'priorite': forms.Select(attrs={
                'class': 'form-select'
            }),
            'date_fin_prevue': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            })
        }

class CustomUserForm(forms.ModelForm):
    is_admin = forms.BooleanField(
        required=False,
        label="Privilèges d'administration",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        help_text="Cocher pour donner les privilèges d'administration à cet utilisateur"
    )

    class Meta:
        model = CustomUser
        fields = ['username', 'email', 'first_name', 'last_name', 'role', 'service', 'is_admin', 'is_active']
        widgets = {
            'username': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'role': forms.Select(attrs={'class': 'form-select'}),
            'service': forms.Select(attrs={'class': 'form-select'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Pré-remplir le champ is_admin basé sur is_superuser
        if self.instance and self.instance.pk:
            self.fields['is_admin'].initial = self.instance.is_superuser

    def save(self, commit=True):
        user = super().save(commit=False)

        # Gérer les privilèges d'administration
        is_admin = self.cleaned_data.get('is_admin', False)
        if is_admin:
            user.is_superuser = True
            user.is_staff = True
        else:
            user.is_superuser = False
            user.is_staff = False

        if commit:
            user.save()
        return user


class AdminUserCreationForm(UserCreationForm):
    """Formulaire pour créer un utilisateur via l'interface admin"""

    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Adresse email'
        })
    )

    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Prénom'
        })
    )

    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Nom'
        })
    )

    role = forms.ChoiceField(
        choices=[('ADMIN', 'Administrateur'), ('RH', 'Gestionnaire RH'), ('ENCADRANT', 'Encadrant')],
        required=True,
        widget=forms.Select(attrs={
            'class': 'form-control',
            'onchange': 'toggleServiceField(this.value)'
        })
    )

    service = forms.ModelChoiceField(
        queryset=Service.objects.filter(actif=True),
        required=False,
        empty_label="Sélectionner un service",
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        help_text="Obligatoire pour les encadrants"
    )

    is_admin = forms.BooleanField(
        required=False,
        label="Privilèges d'administration",
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        help_text="Cocher pour donner les privilèges d'administration à cet utilisateur"
    )

    class Meta:
        model = CustomUser
        fields = ('username', 'email', 'first_name', 'last_name', 'role', 'service', 'is_admin', 'password1', 'password2')
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': "Nom d'utilisateur"
            }),
        }

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.role = self.cleaned_data['role']
        user.service = self.cleaned_data.get('service')

        # Gérer les privilèges d'administration
        is_admin = self.cleaned_data.get('is_admin', False)
        if is_admin:
            user.is_superuser = True
            user.is_staff = True
        else:
            user.is_superuser = False
            user.is_staff = False

        if commit:
            user.save()
        return user












