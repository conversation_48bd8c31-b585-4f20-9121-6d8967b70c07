import os
from django import template

register = template.Library()

@register.filter
def basename(value):
    """
    Retourne le nom de fichier sans le chemin complet
    """
    if value:
        return os.path.basename(str(value))
    return value

@register.filter
def filesizeformat_mb(value):
    """
    Formate la taille de fichier en MB
    """
    try:
        size_mb = value / (1024 * 1024)
        if size_mb < 1:
            return f"{size_mb * 1024:.1f} KB"
        else:
            return f"{size_mb:.1f} MB"
    except (TypeError, ValueError):
        return value

@register.filter
def percentage_color(value):
    """
    Retourne une classe CSS de couleur basée sur le pourcentage
    """
    try:
        percentage = float(value)
        if percentage < 30:
            return 'danger'
        elif percentage < 70:
            return 'warning'
        else:
            return 'success'
    except (TypeError, ValueError):
        return 'secondary'

@register.filter
def priority_badge(value):
    """
    Retourne une classe CSS de badge basée sur la priorité
    """
    priority_classes = {
        1: 'danger',    # Très haute
        2: 'warning',   # Haute
        3: 'info',      # Moyenne
        4: 'secondary', # Basse
        5: 'light'      # Très basse
    }
    return priority_classes.get(value, 'secondary')

@register.filter
def status_badge(value):
    """
    Retourne une classe CSS de badge basée sur le statut
    """
    status_classes = {
        'PLANIFIEE': 'secondary',
        'EN_COURS': 'warning',
        'TERMINEE': 'success',
        'VALIDEE': 'primary',
        'REJETEE': 'danger',
        'BROUILLON': 'secondary',
        'SOUMIS': 'warning',
        'EN_REVISION': 'info',
        'VALIDE': 'success',
        'REJETE': 'danger'
    }
    return status_classes.get(value, 'secondary')
