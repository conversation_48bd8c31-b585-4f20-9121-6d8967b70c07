{% extends 'stagiaires/base.html' %}

{% block title %}Ajouter un utilisateur - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        Ajouter un utilisateur
                    </h3>
                    <a href="{% url 'user_management' %}" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                </div>
                <div class="card-body">
                    <!-- Avertissement administrateur -->
                    <div class="alert alert-warning mb-4">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>Interface Administrateur :</strong> Vous créez un nouvel utilisateur avec des privilèges spécifiques.
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user me-1"></i>Prénom
                                </label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.first_name.errors }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                    <i class="fas fa-user me-1"></i>Nom
                                </label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.last_name.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="fas fa-at me-1"></i>Nom d'utilisateur
                            </label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.username.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                <i class="fas fa-envelope me-1"></i>Adresse email
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.email.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.role.id_for_label }}" class="form-label">
                                <i class="fas fa-user-tag me-1"></i>Rôle
                            </label>
                            {{ form.role }}
                            {% if form.role.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.role.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3" id="service-field">
                            <label for="{{ form.service.id_for_label }}" class="form-label">
                                <i class="fas fa-building me-1"></i>Service
                            </label>
                            {{ form.service }}
                            <div class="form-text">{{ form.service.help_text }}</div>
                            {% if form.service.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.service.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_admin }}
                                <label class="form-check-label" for="{{ form.is_admin.id_for_label }}">
                                    <i class="fas fa-shield-alt me-1 text-danger"></i>
                                    <strong>{{ form.is_admin.label }}</strong>
                                </label>
                            </div>
                            <div class="form-text">{{ form.is_admin.help_text }}</div>
                            {% if form.is_admin.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.is_admin.errors }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Mot de passe
                                </label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.password1.errors }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Confirmer le mot de passe
                                </label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.password2.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'user_management' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-user-plus me-1"></i>Créer l'utilisateur
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleServiceField(role) {
    const serviceField = document.getElementById('service-field');
    if (role === 'ENCADRANT') {
        serviceField.style.display = 'block';
        document.getElementById('{{ form.service.id_for_label }}').required = true;
    } else {
        serviceField.style.display = 'none';
        document.getElementById('{{ form.service.id_for_label }}').required = false;
    }
}

// Initialiser l'affichage au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('{{ form.role.id_for_label }}');
    toggleServiceField(roleSelect.value);
});
</script>
{% endblock %}
