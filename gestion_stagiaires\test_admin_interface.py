#!/usr/bin/env python
"""
Script pour tester l'interface d'administration et identifier les problèmes
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.test import Client, TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from stagiaires.models import Stagiaire, Service
from datetime import date, timedelta

User = get_user_model()

def test_admin_interface():
    """Test de l'interface d'administration Django"""
    
    print("=== Test de l'interface d'administration Django ===")
    
    # Créer un client de test
    client = Client()
    
    # Récupérer un admin
    admin_user = User.objects.filter(is_superuser=True).first()
    if not admin_user:
        print("❌ Aucun administrateur trouvé")
        return
    
    print(f"✅ Admin trouvé: {admin_user.username}")
    
    # Se connecter en tant qu'admin
    login_success = client.force_login(admin_user)
    print(f"✅ Connexion admin réussie")
    
    # Test 1: Accès à la page d'administration principale
    print(f"\n🏠 Test de la page d'administration principale:")
    try:
        response = client.get('/admin/')
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ Page d'administration accessible")
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 2: Accès à la liste des stagiaires
    print(f"\n📋 Test de la liste des stagiaires:")
    try:
        response = client.get('/admin/stagiaires/stagiaire/')
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ Liste des stagiaires accessible")
            # Vérifier le contenu
            content = response.content.decode('utf-8')
            if 'Ajouter stagiaire' in content or 'Add stagiaire' in content:
                print(f"   ✅ Bouton d'ajout présent")
            else:
                print(f"   ⚠️ Bouton d'ajout non trouvé")
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 3: Accès au formulaire d'ajout de stagiaire
    print(f"\n➕ Test du formulaire d'ajout de stagiaire:")
    try:
        response = client.get('/admin/stagiaires/stagiaire/add/')
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ Formulaire d'ajout accessible")
            
            # Vérifier la présence des champs obligatoires
            content = response.content.decode('utf-8')
            required_fields = ['nom', 'prenom', 'email', 'date_naissance', 'departement']
            
            for field in required_fields:
                if f'name="{field}"' in content or f'id="id_{field}"' in content:
                    print(f"   ✅ Champ '{field}' présent")
                else:
                    print(f"   ❌ Champ '{field}' manquant")
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 4: Soumission du formulaire d'ajout
    print(f"\n💾 Test de soumission du formulaire:")
    
    # Récupérer un encadrant
    encadrant = User.objects.filter(role='ENCADRANT').first()
    if not encadrant:
        print(f"   ⚠️ Aucun encadrant trouvé - création d'un encadrant de test")
        service_test, _ = Service.objects.get_or_create(
            code_service='TEST',
            defaults={'nom': 'Service Test', 'actif': True}
        )
        encadrant, _ = User.objects.get_or_create(
            username='encadrant_test_admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'Encadrant',
                'role': 'ENCADRANT',
                'service': service_test,
                'is_active': True
            }
        )
    
    # Données du formulaire
    form_data = {
        'nom': 'TestFormulaire',
        'prenom': 'Stagiaire',
        'email': '<EMAIL>',
        'telephone': '0123456789',
        'date_naissance': '2000-01-01',
        'departement': 'IT',
        'service': encadrant.service.id if encadrant.service else '',
        'encadrant': encadrant.id,
        'date_debut': date.today().strftime('%Y-%m-%d'),
        'date_fin': (date.today() + timedelta(days=90)).strftime('%Y-%m-%d'),
        'statut': 'EN_COURS',
        'etablissement': 'Université Test Formulaire',
        'niveau_etude': 'Master 2',
        'specialite': 'Informatique',
        'technologies': 'Python, Django',
        'thematique': '',
        'sujet': '',
        'duree_estimee': '90',
        'description_taches': 'Tâches de test formulaire',
        'statut_taches': 'NON_COMMENCEES',
        'statut_convention': 'EN_ATTENTE',
        'commentaire_convention': '',
        # Inlines vides
        'tache_set-TOTAL_FORMS': '0',
        'tache_set-INITIAL_FORMS': '0',
        'tache_set-MIN_NUM_FORMS': '0',
        'tache_set-MAX_NUM_FORMS': '1000',
        'mission_set-TOTAL_FORMS': '0',
        'mission_set-INITIAL_FORMS': '0',
        'mission_set-MIN_NUM_FORMS': '0',
        'mission_set-MAX_NUM_FORMS': '1000',
        'rapportstage_set-TOTAL_FORMS': '0',
        'rapportstage_set-INITIAL_FORMS': '0',
        'rapportstage_set-MIN_NUM_FORMS': '0',
        'rapportstage_set-MAX_NUM_FORMS': '1000',
        '_save': 'Enregistrer',
    }
    
    try:
        # Supprimer le stagiaire de test s'il existe
        Stagiaire.objects.filter(email='<EMAIL>').delete()
        
        # Soumettre le formulaire
        response = client.post('/admin/stagiaires/stagiaire/add/', data=form_data)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 302:  # Redirection après succès
            print(f"   ✅ Formulaire soumis avec succès (redirection)")
            
            # Vérifier que le stagiaire a été créé
            stagiaire = Stagiaire.objects.filter(email='<EMAIL>').first()
            if stagiaire:
                print(f"   ✅ Stagiaire créé: {stagiaire.nom_complet}")
                print(f"      ID: {stagiaire.id}")
                print(f"      Créé par: {stagiaire.cree_par}")
                
                # Nettoyer
                stagiaire.delete()
                print(f"   🧹 Stagiaire de test supprimé")
            else:
                print(f"   ❌ Stagiaire non trouvé en base après soumission")
        
        elif response.status_code == 200:  # Formulaire avec erreurs
            print(f"   ⚠️ Formulaire retourné avec erreurs")
            content = response.content.decode('utf-8')
            
            # Chercher les erreurs dans le HTML
            if 'errorlist' in content:
                print(f"   ❌ Erreurs détectées dans le formulaire")
                # Extraire les erreurs (basique)
                import re
                errors = re.findall(r'<ul class="errorlist[^>]*">(.*?)</ul>', content, re.DOTALL)
                for error in errors[:3]:  # Limiter à 3 erreurs
                    clean_error = re.sub(r'<[^>]+>', '', error).strip()
                    if clean_error:
                        print(f"      • {clean_error}")
            else:
                print(f"   ❓ Aucune erreur visible détectée")
        
        else:
            print(f"   ❌ Erreur inattendue: {response.status_code}")
    
    except Exception as e:
        print(f"   ❌ Erreur lors de la soumission: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n=== Test terminé ===")

if __name__ == '__main__':
    test_admin_interface()
