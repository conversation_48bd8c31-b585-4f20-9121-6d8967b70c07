#!/usr/bin/env python
"""
Script de diagnostic spécifique pour identifier pourquoi l'ajout de stagiaires ne fonctionne pas dans l'admin
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from datetime import date, timedelta
import traceback

User = get_user_model()

def debug_admin_stagiaire_issue():
    """Diagnostiquer le problème spécifique d'ajout de stagiaires"""
    
    print("=== Diagnostic du problème d'ajout de stagiaires dans l'admin ===")
    
    # 1. Vérifier l'état de la base de données
    print("\n🗄️ Vérification de la base de données:")
    try:
        stagiaires_count = Stagiaire.objects.count()
        print(f"   ✅ Nombre de stagiaires existants: {stagiaires_count}")
        
        # Vérifier les derniers stagiaires
        derniers_stagiaires = Stagiaire.objects.order_by('-date_creation')[:3]
        for stagiaire in derniers_stagiaires:
            print(f"   • {stagiaire.nom_complet} - {stagiaire.date_creation}")
    except Exception as e:
        print(f"   ❌ Erreur d'accès à la base: {e}")
        return
    
    # 2. Vérifier les utilisateurs admin et encadrants
    print("\n👥 Vérification des utilisateurs:")
    admins = User.objects.filter(is_superuser=True)
    encadrants = User.objects.filter(role='ENCADRANT', is_active=True)
    
    print(f"   Administrateurs: {admins.count()}")
    print(f"   Encadrants actifs: {encadrants.count()}")
    
    if admins.count() == 0:
        print("   ❌ Aucun administrateur trouvé!")
        return
    
    admin_user = admins.first()
    print(f"   ✅ Admin principal: {admin_user.username}")
    
    if encadrants.count() == 0:
        print("   ⚠️ Aucun encadrant actif - création d'un encadrant de test")
        # Créer un service et encadrant de test
        service_test, created = Service.objects.get_or_create(
            code_service='DEBUG',
            defaults={
                'nom': 'Service Debug',
                'description': 'Service pour debug',
                'actif': True
            }
        )
        
        encadrant_test, created = User.objects.get_or_create(
            username='encadrant_debug',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Debug',
                'last_name': 'Encadrant',
                'role': 'ENCADRANT',
                'service': service_test,
                'is_active': True
            }
        )
        if created:
            encadrant_test.set_password('debugpassword')
            encadrant_test.save()
        print(f"   ✅ Encadrant de debug créé: {encadrant_test.username}")
    else:
        encadrant_test = encadrants.first()
        print(f"   ✅ Encadrant principal: {encadrant_test.username}")
    
    # 3. Test de création étape par étape
    print("\n🧪 Test de création étape par étape:")
    
    # Données minimales requises
    test_data = {
        'nom': 'DebugTest',
        'prenom': 'Stagiaire',
        'email': '<EMAIL>',
        'date_naissance': date(2000, 1, 1),
        'departement': 'IT',
        'date_debut': date.today(),
        'date_fin': date.today() + timedelta(days=90),
        'etablissement': 'Université Debug',
        'niveau_etude': 'Master',
        'specialite': 'Informatique'
    }
    
    # Supprimer le stagiaire de test s'il existe
    Stagiaire.objects.filter(email='<EMAIL>').delete()
    
    # Étape 1: Création sans champs optionnels
    print("   Étape 1: Création avec champs obligatoires uniquement")
    try:
        stagiaire = Stagiaire(**test_data)
        stagiaire.full_clean()  # Validation
        print("   ✅ Validation des champs obligatoires réussie")
        
        stagiaire.save()
        print(f"   ✅ Stagiaire créé: {stagiaire.nom_complet} (ID: {stagiaire.id})")
        
        # Nettoyer
        stagiaire.delete()
        
    except ValidationError as e:
        print(f"   ❌ Erreur de validation: {e}")
        for field, errors in e.message_dict.items():
            print(f"      • {field}: {errors}")
    except IntegrityError as e:
        print(f"   ❌ Erreur d'intégrité: {e}")
    except Exception as e:
        print(f"   ❌ Erreur inattendue: {e}")
        traceback.print_exc()
    
    # Étape 2: Création avec encadrant
    print("   Étape 2: Création avec encadrant")
    try:
        test_data_with_encadrant = test_data.copy()
        test_data_with_encadrant.update({
            'encadrant': encadrant_test,
            'service': encadrant_test.service,
            'cree_par': admin_user
        })
        
        stagiaire = Stagiaire(**test_data_with_encadrant)
        stagiaire.full_clean()
        stagiaire.save()
        print(f"   ✅ Stagiaire avec encadrant créé: {stagiaire.nom_complet}")
        
        # Vérifier les relations
        print(f"      Encadrant: {stagiaire.encadrant}")
        print(f"      Service: {stagiaire.service}")
        print(f"      Créé par: {stagiaire.cree_par}")
        
        # Nettoyer
        stagiaire.delete()
        
    except Exception as e:
        print(f"   ❌ Erreur avec encadrant: {e}")
        traceback.print_exc()
    
    # 4. Test de l'interface d'administration
    print("\n🖥️ Test de l'interface d'administration:")
    
    from django.test import Client
    from django.contrib.admin.sites import site
    from stagiaires.admin import StagiaireAdmin
    
    # Créer un client de test
    client = Client()
    client.force_login(admin_user)
    
    # Test d'accès aux pages admin
    urls_to_test = [
        ('/admin/', 'Page principale'),
        ('/admin/stagiaires/', 'App stagiaires'),
        ('/admin/stagiaires/stagiaire/', 'Liste stagiaires'),
        ('/admin/stagiaires/stagiaire/add/', 'Ajout stagiaire')
    ]
    
    for url, description in urls_to_test:
        try:
            response = client.get(url)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"   {status} {description}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {description}: Erreur - {e}")
    
    # 5. Test de soumission de formulaire admin
    print("\n📝 Test de soumission du formulaire admin:")
    
    form_data = {
        'nom': 'FormTest',
        'prenom': 'Stagiaire',
        'email': '<EMAIL>',
        'telephone': '0123456789',
        'date_naissance': '2000-01-01',
        'departement': 'IT',
        'service': encadrant_test.service.id if encadrant_test.service else '',
        'encadrant': encadrant_test.id,
        'date_debut': date.today().strftime('%Y-%m-%d'),
        'date_fin': (date.today() + timedelta(days=90)).strftime('%Y-%m-%d'),
        'statut': 'EN_COURS',
        'etablissement': 'Université Form Test',
        'niveau_etude': 'Master',
        'specialite': 'Informatique',
        'technologies': '',
        'thematique': '',
        'sujet': '',
        'duree_estimee': '0',
        'description_taches': '',
        'statut_taches': 'NON_COMMENCEES',
        'statut_convention': 'EN_ATTENTE',
        'commentaire_convention': '',
        'evaluation_encadrant': '',
        'note_finale': '',
        'cv': '',
        'assurance': '',
        'convention_stage': '',
        'date_validation_convention': '',
        'validee_par': '',
        'attestation_fin_stage': '',
        'date_generation_attestation': '',
        'attestation_generee_par': '',
        # Inlines
        'tache_set-TOTAL_FORMS': '0',
        'tache_set-INITIAL_FORMS': '0',
        'tache_set-MIN_NUM_FORMS': '0',
        'tache_set-MAX_NUM_FORMS': '1000',
        'mission_set-TOTAL_FORMS': '0',
        'mission_set-INITIAL_FORMS': '0',
        'mission_set-MIN_NUM_FORMS': '0',
        'mission_set-MAX_NUM_FORMS': '1000',
        'rapportstage_set-TOTAL_FORMS': '0',
        'rapportstage_set-INITIAL_FORMS': '0',
        'rapportstage_set-MIN_NUM_FORMS': '0',
        'rapportstage_set-MAX_NUM_FORMS': '1000',
        '_save': 'Enregistrer',
    }
    
    try:
        # Supprimer le stagiaire de test s'il existe
        Stagiaire.objects.filter(email='<EMAIL>').delete()
        
        response = client.post('/admin/stagiaires/stagiaire/add/', data=form_data)
        
        print(f"   Status de réponse: {response.status_code}")
        
        if response.status_code == 302:  # Redirection = succès
            print("   ✅ Formulaire soumis avec succès!")
            
            # Vérifier que le stagiaire a été créé
            stagiaire_cree = Stagiaire.objects.filter(email='<EMAIL>').first()
            if stagiaire_cree:
                print(f"   ✅ Stagiaire trouvé en base: {stagiaire_cree.nom_complet}")
                stagiaire_cree.delete()
                print("   🧹 Stagiaire de test supprimé")
            else:
                print("   ❌ Stagiaire non trouvé en base après création")
        
        elif response.status_code == 200:
            print("   ⚠️ Formulaire retourné avec erreurs")
            content = response.content.decode('utf-8')
            
            # Chercher les erreurs
            if 'errorlist' in content:
                print("   ❌ Erreurs détectées:")
                import re
                errors = re.findall(r'<ul class="errorlist[^>]*">(.*?)</ul>', content, re.DOTALL)
                for i, error in enumerate(errors[:5]):  # Limiter à 5 erreurs
                    clean_error = re.sub(r'<[^>]+>', '', error).strip()
                    if clean_error:
                        print(f"      {i+1}. {clean_error}")
            
            # Chercher les champs avec erreurs
            field_errors = re.findall(r'<div class="form-row[^>]*errors[^>]*">.*?<label[^>]*>([^<]+)</label>', content, re.DOTALL)
            if field_errors:
                print("   📋 Champs avec erreurs:")
                for field in field_errors[:5]:
                    print(f"      • {field.strip()}")
        
        else:
            print(f"   ❌ Erreur inattendue: {response.status_code}")
    
    except Exception as e:
        print(f"   ❌ Erreur lors du test de formulaire: {e}")
        traceback.print_exc()
    
    print(f"\n=== Diagnostic terminé ===")
    print(f"\n💡 Recommandations:")
    print(f"   1. Vérifiez que tous les champs obligatoires sont remplis")
    print(f"   2. Assurez-vous que l'email est unique")
    print(f"   3. Vérifiez que les dates sont cohérentes (fin > début)")
    print(f"   4. Consultez les logs Django pour plus de détails")

if __name__ == '__main__':
    debug_admin_stagiaire_issue()
