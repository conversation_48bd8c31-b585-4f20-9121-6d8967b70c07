#!/usr/bin/env python
"""
Script pour corriger les services des thématiques et sujets
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import Thematique, Sujet, Service

def corriger_services_thematiques_sujets():
    """Corriger les services des thématiques et sujets"""
    
    print("=== CORRECTION SERVICES THÉMATIQUES ET SUJETS ===")
    
    # Récupérer les services
    services = {
        'informatique': Service.objects.filter(nom__icontains='informatique').first(),
        'marketing': Service.objects.filter(nom__icontains='marketing').first(),
        'production': Service.objects.filter(nom__icontains='production').first(),
        'communication': Service.objects.filter(nom__icontains='communication').first(),
    }
    
    print("🏢 Services disponibles:")
    for nom, service in services.items():
        if service:
            print(f"   ✅ {nom}: {service.nom}")
        else:
            print(f"   ❌ {nom}: Non trouvé")
    
    # Mapping thématiques -> services basé sur le contenu
    thematiques_mapping = {
        # Informatique
        'cybersécurité': 'informatique',
        'intelligence artificielle': 'informatique',
        'développement web': 'informatique',
        'develeppement web': 'informatique',
        'reseau': 'informatique',
        'dev web': 'informatique',
        
        # Marketing
        'marketing': 'marketing',
        'communication': 'marketing',
        'publicité': 'marketing',
        'digital': 'marketing',
        
        # Production
        'société et vie quotidienne': 'production',
        'gestion financière': 'production',
        'finance': 'production',
        'transformation digitale': 'production',
        
        # Communication
        'relations publiques': 'communication',
        'médias': 'communication',
    }
    
    # Mapping sujets -> services basé sur le contenu
    sujets_mapping = {
        # Informatique
        'java': 'informatique',
        'js': 'informatique',
        'html css': 'informatique',
        'application': 'informatique',
        'audit de sécurité': 'informatique',
        'système de recommandation': 'informatique',
        'application de gestion': 'informatique',
        
        # Production
        'transformation digitale': 'production',
        'politique de dividendes': 'production',
        'analyse de la structure': 'production',
        'gestion financière': 'production',
        
        # Marketing
        'stratégie marketing': 'marketing',
        'campagne publicitaire': 'marketing',
        
        # Communication
        'relations publiques': 'communication',
        'communication interne': 'communication',
    }
    
    # 1. Corriger les thématiques
    print(f"\n📋 CORRECTION DES THÉMATIQUES:")
    
    thematiques_sans_service = Thematique.objects.filter(service__isnull=True)
    print(f"   Thématiques sans service: {thematiques_sans_service.count()}")
    
    corrections_thematiques = 0
    
    for thematique in thematiques_sans_service:
        titre_lower = thematique.titre.lower()
        service_assigne = None
        
        # Chercher une correspondance
        for mot_cle, service_nom in thematiques_mapping.items():
            if mot_cle in titre_lower:
                service_assigne = services.get(service_nom)
                break
        
        if service_assigne:
            print(f"   ✅ {thematique.titre} → {service_assigne.nom}")
            thematique.service = service_assigne
            thematique.save()
            corrections_thematiques += 1
        else:
            # Assigner par défaut à Informatique si contient des mots techniques
            mots_techniques = ['web', 'dev', 'cyber', 'ia', 'intelligence', 'réseau', 'application']
            if any(mot in titre_lower for mot in mots_techniques):
                service_assigne = services.get('informatique')
                if service_assigne:
                    print(f"   🔧 {thematique.titre} → {service_assigne.nom} (par défaut technique)")
                    thematique.service = service_assigne
                    thematique.save()
                    corrections_thematiques += 1
            else:
                print(f"   ⚠️ {thematique.titre} → Aucun service trouvé")
    
    # 2. Corriger les sujets
    print(f"\n📝 CORRECTION DES SUJETS:")
    
    sujets_sans_service = Sujet.objects.filter(service__isnull=True)
    print(f"   Sujets sans service: {sujets_sans_service.count()}")
    
    corrections_sujets = 0
    
    for sujet in sujets_sans_service:
        titre_lower = sujet.titre.lower()
        service_assigne = None
        
        # Chercher une correspondance
        for mot_cle, service_nom in sujets_mapping.items():
            if mot_cle in titre_lower:
                service_assigne = services.get(service_nom)
                break
        
        # Si pas trouvé, utiliser le service de la thématique
        if not service_assigne and sujet.thematique and sujet.thematique.service:
            service_assigne = sujet.thematique.service
            print(f"   🔗 {sujet.titre} → {service_assigne.nom} (via thématique)")
        elif service_assigne:
            print(f"   ✅ {sujet.titre} → {service_assigne.nom}")
        
        if service_assigne:
            sujet.service = service_assigne
            sujet.save()
            corrections_sujets += 1
        else:
            # Assigner par défaut à Informatique si contient des mots techniques
            mots_techniques = ['java', 'js', 'html', 'css', 'application', 'audit', 'système', 'web']
            if any(mot in titre_lower for mot in mots_techniques):
                service_assigne = services.get('informatique')
                if service_assigne:
                    print(f"   🔧 {sujet.titre} → {service_assigne.nom} (par défaut technique)")
                    sujet.service = service_assigne
                    sujet.save()
                    corrections_sujets += 1
            else:
                print(f"   ⚠️ {sujet.titre} → Aucun service trouvé")
    
    # 3. Créer des thématiques et sujets pour les services vides
    print(f"\n➕ CRÉATION THÉMATIQUES/SUJETS POUR SERVICES VIDES:")
    
    # Marketing
    service_marketing = services.get('marketing')
    if service_marketing:
        thematiques_marketing = Thematique.objects.filter(service=service_marketing).count()
        if thematiques_marketing == 0:
            print(f"   📋 Création thématiques Marketing...")
            
            # Créer thématiques Marketing
            thematiques_a_creer = [
                {
                    'titre': 'Marketing Digital',
                    'description': 'Stratégies et outils de marketing numérique'
                },
                {
                    'titre': 'Communication d\'Entreprise',
                    'description': 'Techniques de communication interne et externe'
                },
                {
                    'titre': 'Analyse de Marché',
                    'description': 'Études de marché et analyse concurrentielle'
                }
            ]
            
            for them_data in thematiques_a_creer:
                thematique = Thematique.objects.create(
                    titre=them_data['titre'],
                    description=them_data['description'],
                    service=service_marketing,
                    active=True
                )
                print(f"      ✅ Thématique créée: {thematique.titre}")
                
                # Créer des sujets pour cette thématique
                if 'Digital' in them_data['titre']:
                    sujets_data = [
                        'Stratégie de contenu sur les réseaux sociaux',
                        'Optimisation SEO et référencement',
                        'Campagnes publicitaires en ligne'
                    ]
                elif 'Communication' in them_data['titre']:
                    sujets_data = [
                        'Plan de communication interne',
                        'Gestion de crise et communication',
                        'Relations presse et médias'
                    ]
                else:  # Analyse de Marché
                    sujets_data = [
                        'Étude de satisfaction client',
                        'Analyse de la concurrence',
                        'Segmentation de marché'
                    ]
                
                for sujet_titre in sujets_data:
                    sujet = Sujet.objects.create(
                        titre=sujet_titre,
                        description=f'Sujet de stage en {them_data["titre"]}',
                        thematique=thematique,
                        service=service_marketing
                    )
                    print(f"         • Sujet créé: {sujet.titre}")
    
    # Communication
    service_communication = services.get('communication')
    if service_communication:
        thematiques_communication = Thematique.objects.filter(service=service_communication).count()
        if thematiques_communication == 0:
            print(f"   📋 Création thématiques Communication...")
            
            thematique = Thematique.objects.create(
                titre='Relations Publiques',
                description='Gestion des relations publiques et communication externe',
                service=service_communication,
                active=True
            )
            print(f"      ✅ Thématique créée: {thematique.titre}")
            
            sujets_data = [
                'Gestion des relations médias',
                'Organisation d\'événements corporate',
                'Communication de crise'
            ]
            
            for sujet_titre in sujets_data:
                sujet = Sujet.objects.create(
                    titre=sujet_titre,
                    description='Sujet de stage en Relations Publiques',
                    thematique=thematique,
                    service=service_communication
                )
                print(f"         • Sujet créé: {sujet.titre}")
    
    # 4. Vérification finale
    print(f"\n🔍 VÉRIFICATION FINALE:")
    
    for nom, service in services.items():
        if service:
            thematiques_count = Thematique.objects.filter(service=service, active=True).count()
            sujets_count = Sujet.objects.filter(service=service).count()
            
            print(f"   🏢 {service.nom}:")
            print(f"      📋 Thématiques: {thematiques_count}")
            print(f"      📝 Sujets: {sujets_count}")
    
    # Éléments sans service
    thematiques_sans_service_final = Thematique.objects.filter(service__isnull=True, active=True).count()
    sujets_sans_service_final = Sujet.objects.filter(service__isnull=True).count()
    
    print(f"\n   ❓ Sans service:")
    print(f"      📋 Thématiques: {thematiques_sans_service_final}")
    print(f"      📝 Sujets: {sujets_sans_service_final}")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ CORRECTION:")
    print("")
    print("✅ CORRECTIONS EFFECTUÉES :")
    print(f"   • Thématiques corrigées: {corrections_thematiques}")
    print(f"   • Sujets corrigés: {corrections_sujets}")
    print("   • Thématiques/sujets créés pour services vides ✅")
    print("")
    print("✅ RÉSULTAT :")
    if thematiques_sans_service_final == 0 and sujets_sans_service_final == 0:
        print("   • Toutes les thématiques et sujets ont un service ✅")
    else:
        print(f"   • {thematiques_sans_service_final + sujets_sans_service_final} éléments sans service restants ⚠️")
    print("")
    print("🎉 CORRECTION TERMINÉE !")
    print(f"{'='*60}")

if __name__ == '__main__':
    corriger_services_thematiques_sujets()
