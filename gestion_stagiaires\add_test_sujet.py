#!/usr/bin/env python
import os
import django
import sys

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Sujet, Thematique, Service

User = get_user_model()

print("=== CRÉATION D'UN SUJET DE TEST ===")

# 1. Vérifier si des thématiques existent
thematiques = Thematique.objects.all()
if not thematiques.exists():
    print("Aucune thématique trouvée. Création d'une thématique de test...")
    thematique = Thematique.objects.create(
        titre="Thématique de test",
        description="Description de la thématique de test",
        active=True
    )
    print(f"✓ Thématique créée: {thematique.titre} (ID: {thematique.id})")
else:
    thematique = thematiques.first()
    print(f"Utilisation de la thématique existante: {thematique.titre} (ID: {thematique.id})")

# 2. Vérifier si des utilisateurs existent
users = User.objects.all()
if not users.exists():
    print("Aucun utilisateur trouvé. Veuillez créer un utilisateur d'abord.")
    sys.exit(1)
else:
    user = users.first()
    print(f"Utilisation de l'utilisateur: {user.username} (ID: {user.id})")

# 3. Vérifier si des services existent
services = Service.objects.all()
if services.exists():
    service = services.first()
    print(f"Utilisation du service: {service.nom} (ID: {service.id})")
else:
    service = None
    print("Aucun service trouvé. Le sujet sera créé sans service.")

# 4. Créer un sujet de test
sujet = Sujet.objects.create(
    titre="Sujet de test",
    description="Description du sujet de test",
    thematique=thematique,
    service=service,
    cree_par=user,
    actif=True,
    duree_recommandee=30
)
print(f"✓ Sujet créé: {sujet.titre} (ID: {sujet.id})")

# 5. Vérifier les sujets existants
sujets = Sujet.objects.all()
print(f"\nListe des sujets ({sujets.count()}):")
for s in sujets:
    print(f"- {s.id}: {s.titre} (Thématique: {s.thematique.titre}, Actif: {s.actif})")

print("\n=== TERMINÉ ===")
print("Vous pouvez maintenant vérifier si le sujet apparaît dans la liste des sujets.")