{% extends 'stagiaires/base.html' %}

{% block title %}Créer un Contrat de Stage - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-file-contract me-2"></i>
                        Création du Contrat de Stage
                    </h4>
                    <small>{{ stagiaire.nom_complet }} - {{ stagiaire.get_departement_display }}</small>
                </div>
                <div class="card-body">
                    <!-- Informations du stagiaire -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Informations du stagiaire</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Nom :</strong> {{ stagiaire.nom_complet }}</p>
                                    <p><strong>Email :</strong> {{ stagiaire.email }}</p>
                                    <p><strong>Téléphone :</strong> {{ stagiaire.telephone|default:"Non renseigné" }}</p>
                                    <p><strong>Département :</strong> {{ stagiaire.get_departement_display }}</p>
                                    <p><strong>Encadrant :</strong> {{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-check-circle me-2"></i>Convention validée</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Statut :</strong> 
                                        <span class="badge bg-success">{{ stagiaire.get_statut_convention_display }}</span>
                                    </p>
                                    <p><strong>Validée le :</strong> {{ stagiaire.date_validation_convention|date:"d/m/Y à H:i" }}</p>
                                    <p><strong>Validée par :</strong> {{ stagiaire.validee_par.get_full_name }}</p>
                                    <p><strong>Période :</strong> {{ stagiaire.date_debut|date:"d/m/Y" }} - {{ stagiaire.date_fin|date:"d/m/Y" }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Formulaire de création du contrat -->
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-edit me-2"></i>Détails du contrat de stage</h6>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                <div class="row">
                                    <!-- Type de contrat -->
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.type_contrat.id_for_label }}" class="form-label">
                                            <i class="fas fa-tag me-1"></i>Type de contrat
                                        </label>
                                        {{ form.type_contrat }}
                                        {% if form.type_contrat.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.type_contrat.errors }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- Titre du stage -->
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.titre_stage.id_for_label }}" class="form-label">
                                            <i class="fas fa-heading me-1"></i>Titre du stage
                                        </label>
                                        {{ form.titre_stage }}
                                        {% if form.titre_stage.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.titre_stage.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Description des missions -->
                                <div class="mb-3">
                                    <label for="{{ form.description_missions.id_for_label }}" class="form-label">
                                        <i class="fas fa-tasks me-1"></i>Description des missions
                                    </label>
                                    {{ form.description_missions }}
                                    {% if form.description_missions.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.description_missions.errors }}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Objectifs pédagogiques -->
                                <div class="mb-3">
                                    <label for="{{ form.objectifs_pedagogiques.id_for_label }}" class="form-label">
                                        <i class="fas fa-graduation-cap me-1"></i>Objectifs pédagogiques
                                    </label>
                                    {{ form.objectifs_pedagogiques }}
                                    {% if form.objectifs_pedagogiques.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.objectifs_pedagogiques.errors }}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Compétences à acquérir -->
                                <div class="mb-3">
                                    <label for="{{ form.competences_acquises.id_for_label }}" class="form-label">
                                        <i class="fas fa-lightbulb me-1"></i>Compétences à acquérir
                                    </label>
                                    {{ form.competences_acquises }}
                                    {% if form.competences_acquises.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.competences_acquises.errors }}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="row">
                                    <!-- Durée et gratification -->
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.duree_hebdomadaire.id_for_label }}" class="form-label">
                                            <i class="fas fa-clock me-1"></i>Durée hebdomadaire (h)
                                        </label>
                                        {{ form.duree_hebdomadaire }}
                                        {% if form.duree_hebdomadaire.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.duree_hebdomadaire.errors }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.gratification_mensuelle.id_for_label }}" class="form-label">
                                            <i class="fas fa-euro-sign me-1"></i>Gratification mensuelle (€)
                                        </label>
                                        {{ form.gratification_mensuelle }}
                                        {% if form.gratification_mensuelle.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.gratification_mensuelle.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="row">

                                    <!-- Encadrant -->
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.encadrant_entreprise.id_for_label }}" class="form-label">
                                            <i class="fas fa-user-tie me-1"></i>Encadrant entreprise
                                        </label>
                                        {{ form.encadrant_entreprise }}
                                        {% if form.encadrant_entreprise.errors %}
                                            <div class="text-danger small mt-1">
                                                {{ form.encadrant_entreprise.errors }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Avantages -->
                                <div class="mb-3">
                                    <label for="{{ form.avantages.id_for_label }}" class="form-label">
                                        <i class="fas fa-gift me-1"></i>Avantages
                                    </label>
                                    {{ form.avantages }}
                                    {% if form.avantages.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.avantages.errors }}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Tuteur pédagogique -->
                                <div class="mb-3">
                                    <label for="{{ form.tuteur_pedagogique.id_for_label }}" class="form-label">
                                        <i class="fas fa-chalkboard-teacher me-1"></i>Tuteur pédagogique
                                    </label>
                                    {{ form.tuteur_pedagogique }}
                                    {% if form.tuteur_pedagogique.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.tuteur_pedagogique.errors }}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Commentaires -->
                                <div class="mb-3">
                                    <label for="{{ form.commentaires_admin.id_for_label }}" class="form-label">
                                        <i class="fas fa-comment me-1"></i>Commentaires administratifs
                                    </label>
                                    {{ form.commentaires_admin }}
                                    {% if form.commentaires_admin.errors %}
                                        <div class="text-danger small mt-1">
                                            {{ form.commentaires_admin.errors }}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Boutons d'action -->
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'conventions_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>Retour aux conventions
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save me-1"></i>Créer le contrat
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}
