#!/usr/bin/env python
"""
Test des filtres pour les encadrants
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service

User = get_user_model()

def test_filtres_encadrant():
    """Test des filtres pour les encadrants"""
    
    print("=== TEST DES FILTRES POUR LES ENCADRANTS ===")
    
    # 1. Préparation
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not encadrant:
        print("❌ Aucun encadrant trouvé")
        return
    
    print(f"✅ Encadrant: {encadrant.get_full_name()}")
    print(f"   Service: {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    # 2. Statistiques des stagiaires
    print(f"\n📊 Statistiques des stagiaires:")
    
    tous_stagiaires = Stagiaire.objects.all()
    print(f"   Total en base: {tous_stagiaires.count()}")
    
    # Stagiaires du service de l'encadrant
    from stagiaires.views import filter_stagiaires_by_user_role
    mes_stagiaires = filter_stagiaires_by_user_role(encadrant, tous_stagiaires)
    print(f"   Stagiaires de mon service: {mes_stagiaires.count()}")
    
    # Afficher les stagiaires par département
    departements = {}
    for stagiaire in tous_stagiaires:
        dept = stagiaire.get_departement_display()
        if dept not in departements:
            departements[dept] = 0
        departements[dept] += 1
    
    print(f"   Répartition par département:")
    for dept, count in departements.items():
        print(f"     • {dept}: {count}")
    
    # 3. Test de la vue avec filtre "tous"
    print(f"\n🌐 Test du filtre 'Tous les stagiaires':")
    
    client = Client()
    client.force_login(encadrant)
    
    response = client.get('/stagiaires/?filtre=tous')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        context = response.context
        stagiaires_affiches = context['stagiaires']
        stats = context['stats']
        filtre_actuel = context['filtre_actuel']
        
        print(f"   ✅ Filtre actuel: {filtre_actuel}")
        print(f"   ✅ Stagiaires affichés: {stagiaires_affiches.count()}")
        print(f"   ✅ Stats - Total: {stats['total_stagiaires']}")
        print(f"   ✅ Stats - Mes stagiaires: {stats['mes_stagiaires']}")
        print(f"   ✅ Stats - Service: {stats['service_nom']}")
        
        # Vérifier que tous les stagiaires sont affichés
        if stagiaires_affiches.count() == tous_stagiaires.count():
            print("   ✅ Tous les stagiaires sont bien affichés")
        else:
            print(f"   ⚠️ Différence: {tous_stagiaires.count()} en base vs {stagiaires_affiches.count()} affichés")
    
    # 4. Test de la vue avec filtre "mon_service"
    print(f"\n🏢 Test du filtre 'Mon service':")
    
    response = client.get('/stagiaires/?filtre=mon_service')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        context = response.context
        stagiaires_affiches = context['stagiaires']
        stats = context['stats']
        filtre_actuel = context['filtre_actuel']
        
        print(f"   ✅ Filtre actuel: {filtre_actuel}")
        print(f"   ✅ Stagiaires affichés: {stagiaires_affiches.count()}")
        print(f"   ✅ Stats - Total: {stats['total_stagiaires']}")
        print(f"   ✅ Stats - Mes stagiaires: {stats['mes_stagiaires']}")
        
        # Vérifier que seuls les stagiaires du service sont affichés
        if stagiaires_affiches.count() == mes_stagiaires.count():
            print("   ✅ Seuls les stagiaires du service sont affichés")
        else:
            print(f"   ⚠️ Différence: {mes_stagiaires.count()} attendus vs {stagiaires_affiches.count()} affichés")
        
        # Lister les stagiaires affichés
        print(f"   Stagiaires de mon service:")
        for stagiaire in stagiaires_affiches:
            print(f"     • {stagiaire.nom_complet} - {stagiaire.get_departement_display()}")
    
    # 5. Test de l'interface HTML
    print(f"\n🎨 Test de l'interface HTML:")
    
    response = client.get('/stagiaires/?filtre=tous')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier la présence des éléments de filtre
        checks = [
            ('Filtres d\'affichage', 'Section filtres'),
            ('Tous les stagiaires', 'Bouton tous'),
            ('Mon service', 'Bouton mon service'),
            ('btn-group', 'Groupe de boutons'),
            ('searchInput', 'Champ de recherche'),
        ]
        
        for check, desc in checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc} manquant")
        
        # Vérifier les statistiques dans l'interface
        if f"({stats['total_stagiaires']})" in content:
            print("   ✅ Statistiques affichées dans l'interface")
        else:
            print("   ⚠️ Statistiques non visibles dans l'interface")
    
    # 6. Test avec un admin (pour comparaison)
    print(f"\n👑 Test avec un admin (pour comparaison):")
    
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    if admin:
        client.force_login(admin)
        response = client.get('/stagiaires/')
        
        if response.status_code == 200:
            context = response.context
            is_encadrant = context.get('is_encadrant', False)
            
            print(f"   ✅ Admin connecté: {admin.username}")
            print(f"   ✅ is_encadrant pour admin: {is_encadrant}")
            
            if not is_encadrant:
                print("   ✅ Les filtres ne s'affichent pas pour l'admin (correct)")
            else:
                print("   ⚠️ Les filtres s'affichent pour l'admin (incorrect)")
    
    # 7. Test avec un utilisateur RH
    print(f"\n🏥 Test avec un utilisateur RH:")
    
    user_rh = User.objects.filter(role='RH', is_active=True).first()
    if user_rh:
        client.force_login(user_rh)
        response = client.get('/stagiaires/')
        
        if response.status_code == 200:
            context = response.context
            is_encadrant = context.get('is_encadrant', False)
            stagiaires_affiches = context['stagiaires']
            
            print(f"   ✅ RH connecté: {user_rh.username}")
            print(f"   ✅ is_encadrant pour RH: {is_encadrant}")
            print(f"   ✅ Stagiaires visibles: {stagiaires_affiches.count()}")
            
            if not is_encadrant and stagiaires_affiches.count() == tous_stagiaires.count():
                print("   ✅ RH voit tous les stagiaires sans filtres (correct)")
            else:
                print("   ⚠️ Comportement RH incorrect")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DES FONCTIONNALITÉS AJOUTÉES:")
    print("")
    print("✅ POUR LES ENCADRANTS :")
    print("   • Bouton 'Tous les stagiaires' - Voir tous les stagiaires")
    print("   • Bouton 'Mon service' - Voir uniquement ses stagiaires")
    print("   • Statistiques en temps réel (total vs mes stagiaires)")
    print("   • Interface de filtrage intuitive")
    print("   • Recherche en temps réel dans la liste")
    print("")
    print("✅ POUR LES AUTRES RÔLES :")
    print("   • Admin/RH : Pas de filtres, voient tous les stagiaires")
    print("   • Comportement normal maintenu")
    print("")
    print("🎯 UTILISATION :")
    print("   1. Connectez-vous en tant qu'encadrant")
    print("   2. Allez sur /stagiaires/")
    print("   3. Utilisez les boutons de filtre en haut")
    print("   4. Utilisez la recherche pour filtrer davantage")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_filtres_encadrant()
