#!/usr/bin/env python
"""
Script pour créer un utilisateur encadrant de test
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Service

User = get_user_model()

def create_test_encadrant():
    """Créer un encadrant de test"""
    
    # Vérifier si l'utilisateur existe déjà
    username = 'encadrant_test'
    if User.objects.filter(username=username).exists():
        user = User.objects.get(username=username)
        print(f"✅ L'utilisateur {username} existe déjà")
        print(f"   Email: {user.email}")
        print(f"   Rôle: {user.role}")
        print(f"   Service: {user.service.nom if user.service else 'Aucun'}")
        return user
    
    # C<PERSON>er ou récupérer un service
    service, created = Service.objects.get_or_create(
        nom="Informatique",
        defaults={
            'code_service': 'INFO',
            'description': 'Service informatique',
            'actif': True
        }
    )
    
    # Créer l'utilisateur encadrant
    user = User.objects.create_user(
        username=username,
        email='<EMAIL>',
        password='test123',
        first_name='Ahmed',
        last_name='Benali',
        role='ENCADRANT',
        service=service,
        is_active=True
    )
    
    print(f"✅ Utilisateur encadrant créé avec succès !")
    print(f"   Username: {user.username}")
    print(f"   Password: test123")
    print(f"   Email: {user.email}")
    print(f"   Nom: {user.get_full_name()}")
    print(f"   Rôle: {user.get_role_display()}")
    print(f"   Service: {user.service.nom}")
    
    print(f"\n🔗 Pour vous connecter :")
    print(f"   URL: http://127.0.0.1:8000/login/")
    print(f"   Username: {user.username}")
    print(f"   Password: test123")
    
    return user

if __name__ == '__main__':
    create_test_encadrant()
