# Guide de test du filtrage des stagiaires par service

## 🎯 Objectif
Vérifier que chaque encadrant ne voit que les stagiaires de son département correspondant à son service.

## 📋 Comptes de test disponibles

### Encadrants avec service informatique :
- **aya souya** - Service: informatique → Doit voir les stagiaires du département "Informatique"
- **arwa arwa** - Service: informatique → Doit voir les stagiaires du département "Informatique"

### Encadrants sans service :
- **salma rahmani** - Aucun service → Voit tous ses stagiaires assignés
- **ahmed servi** - Aucun service → Voit seulement ses stagiaires assignés (aucun actuellement)

### Administrateurs :
- **admin** - Voit tous les stagiaires
- **salim@48** - Voit tous les stagiaires

## 🧪 Tests à effectuer

### Test 1: Encadrant avec service informatique
1. **Connectez-vous avec `aya souya`**
2. **Allez dans "Stagiaires" → "Liste des stagiaires"**
3. **Résultat attendu :** Vous devriez voir **3 stagiaires** :
   - ilyass mimoun (Informatique)
   - naoual soussi (Informatique)  
   - aya rahimi (Informatique)
4. **Vous ne devriez PAS voir :**
   - paul xx (Marketing)
   - aya samin (Ressources Humaines)
   - salmane aitali (Finance)

### Test 2: Autre encadrant avec service informatique
1. **Connectez-vous avec `arwa arwa`**
2. **Allez dans "Stagiaires" → "Liste des stagiaires"**
3. **Résultat attendu :** Même résultat que le Test 1 (3 stagiaires IT)

### Test 3: Encadrant sans service
1. **Connectez-vous avec `salma rahmani`**
2. **Allez dans "Stagiaires" → "Liste des stagiaires"**
3. **Résultat attendu :** Vous devriez voir **6 stagiaires** (tous ceux qui lui sont assignés)

### Test 4: Administrateur
1. **Connectez-vous avec `admin`**
2. **Allez dans "Stagiaires" → "Liste des stagiaires"**
3. **Résultat attendu :** Vous devriez voir **tous les 6 stagiaires**

## 🔍 Mapping service → département

Le système utilise cette correspondance :

| Service | Département correspondant |
|---------|---------------------------|
| informatique | Informatique (IT) |
| marketing | Marketing |
| ressources humaines / rh | Ressources Humaines |
| finance | Finance |
| commercial | Commercial |
| production | Production |

## ✅ Vérifications supplémentaires

### Dans le calendrier des stagiaires :
1. **Allez dans "Calendrier des stagiaires"**
2. **Le même filtrage doit s'appliquer**
3. **Les encadrants ne voient que leurs stagiaires selon leur service**

### Ajout de nouveaux stagiaires :
1. **Créez un nouveau stagiaire dans le département "Marketing"**
2. **Connectez-vous en tant qu'encadrant informatique**
3. **Vérifiez qu'il n'apparaît pas dans votre liste**

## 🐛 Problèmes potentiels et solutions

### Si un encadrant voit tous les stagiaires :
- Vérifiez qu'il a bien un service assigné
- Vérifiez que le nom du service correspond au mapping
- Le service doit être exactement "informatique" (en minuscules)

### Si un encadrant ne voit aucun stagiaire :
- Vérifiez qu'il y a des stagiaires dans le département correspondant
- Vérifiez que son service est correctement configuré

### Si le filtrage ne fonctionne pas :
- Rafraîchissez la page (Ctrl+F5)
- Vérifiez que vous êtes bien connecté avec le bon compte
- Consultez les logs du serveur

## 📊 État actuel des données

**Stagiaires par département :**
- Informatique : 3 stagiaires
- Marketing : 1 stagiaire  
- Ressources Humaines : 1 stagiaire
- Finance : 1 stagiaire

**Encadrants par service :**
- Service informatique : 2 encadrants (aya souya, arwa arwa)
- Aucun service : 2 encadrants (salma rahmani, ahmed servi)

## 🎉 Résultat attendu

✅ **Chaque encadrant ne voit que les stagiaires de son département**  
✅ **Les admins voient tous les stagiaires**  
✅ **Le filtrage fonctionne dans toutes les vues (liste, calendrier)**  
✅ **Les encadrants sans service voient leurs stagiaires assignés**
