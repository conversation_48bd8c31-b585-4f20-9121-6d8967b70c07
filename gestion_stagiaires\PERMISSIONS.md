# 🔐 Système de Permissions - Gestion des Stagiaires

## Vue d'ensemble

L'application de gestion des stagiaires utilise un système de permissions basé sur les rôles pour contrôler l'accès aux différentes fonctionnalités.

## 👥 Rôles Utilisateurs

### 🔴 ADMINISTRATEUR (`ADMIN`)
- **Accès complet** à toutes les fonctionnalités
- **Seul autorisé** à gérer les comptes utilisateurs
- <PERSON><PERSON><PERSON> créer, modifier, activer/désactiver et supprimer des utilisateurs
- Accès à toutes les fonctions RH et de gestion

**Fonctionnalités accessibles :**
- ✅ Gestion des utilisateurs (exclusif)
- ✅ Contrats de stage
- ✅ Liste des stagiaires
- ✅ Ajouter un stagiaire
- ✅ Rapports et statistiques
- ✅ Export des données

### 🟡 GESTIONNAIRE RH (`RH`)
- Accès aux fonctions de gestion des ressources humaines
- Peut gérer les contrats de stage
- **PAS d'accès** à la gestion des utilisateurs

**Fonctionnalités accessibles :**
- ❌ Gestion des utilisateurs
- ✅ Contrats de stage
- ✅ Liste des stagiaires
- ✅ Ajouter un stagiaire
- ✅ Rapports et statistiques
- ✅ Export des données

### 🔵 ENCADRANT (`ENCADRANT`)
- Accès aux fonctions de base pour le suivi des stagiaires
- **PAS d'accès** aux contrats de stage ni à la gestion des utilisateurs

**Fonctionnalités accessibles :**
- ❌ Gestion des utilisateurs
- ❌ Contrats de stage
- ✅ Liste des stagiaires
- ✅ Ajouter un stagiaire
- ✅ Rapports et statistiques
- ✅ Export des données

## 🛡️ Sécurité

### Protection des vues
Toutes les vues sont protégées par :
- `@login_required` : Authentification obligatoire
- Vérification du rôle utilisateur
- Redirection avec message d'erreur en cas d'accès non autorisé

### Exemple de protection
```python
@login_required
def user_management_view(request):
    """Vue pour la gestion des utilisateurs (ADMIN uniquement)"""
    if not hasattr(request.user, 'role') or request.user.role != 'ADMIN':
        messages.error(request, 'Accès non autorisé. Cette page est réservée aux administrateurs.')
        return redirect('dashboard')
    # ... reste de la vue
```

## 🎯 Interface Adaptative

Le tableau de bord s'adapte automatiquement selon le rôle de l'utilisateur :

### Pour les ADMINISTRATEURS
- Section "Fonctions Administrateur" (rouge) avec accès à la gestion des utilisateurs
- Accès complet aux contrats de stage

### Pour les GESTIONNAIRES RH
- Section "Fonctions RH" (jaune) avec accès aux contrats
- Message informatif indiquant que la gestion des utilisateurs est réservée aux administrateurs

### Pour les ENCADRANTS
- Accès uniquement aux fonctions de base
- Pas de section spéciale dans le tableau de bord

## 📋 URLs et Permissions

| URL | Fonctionnalité | Admin | RH | Encadrant |
|-----|---------------|-------|----|-----------| 
| `/users/` | Gestion des utilisateurs | ✅ | ❌ | ❌ |
| `/contracts/` | Contrats de stage | ✅ | ✅ | ❌ |
| `/stagiaires/` | Liste des stagiaires | ✅ | ✅ | ✅ |
| `/stagiaires/add/` | Ajouter un stagiaire | ✅ | ✅ | ✅ |
| `/reports/` | Rapports | ✅ | ✅ | ✅ |
| `/export/` | Export | ✅ | ✅ | ✅ |

## 🔧 Configuration

### Création d'un administrateur
```python
# Via le shell Django
python manage.py shell

from stagiaires.models import CustomUser
admin_user = CustomUser.objects.create_user(
    username='admin',
    email='<EMAIL>',
    password='motdepasse',
    role='ADMIN',
    is_superuser=True
)
```

### Modification du rôle d'un utilisateur existant
```python
user = CustomUser.objects.get(username='nom_utilisateur')
user.role = 'ADMIN'  # ou 'RH' ou 'ENCADRANT'
user.save()
```

## 🧪 Tests

Pour tester les permissions, utilisez le script fourni :
```bash
python test_permissions.py
```

Ce script affiche :
- La matrice des permissions
- Les utilisateurs existants et leurs rôles
- Les URLs de test pour chaque fonctionnalité

## 📝 Notes importantes

1. **Seuls les administrateurs** peuvent gérer les comptes utilisateurs
2. **Les gestionnaires RH** ont accès aux contrats mais pas à la gestion des utilisateurs
3. **Les encadrants** ont un accès limité aux fonctions de base
4. **Toutes les vues** nécessitent une authentification
5. **Les messages d'erreur** informent clairement l'utilisateur en cas d'accès refusé

## 🔄 Évolutions futures

- Permissions granulaires par département
- Rôles personnalisés
- Audit des accès
- Gestion des permissions temporaires
