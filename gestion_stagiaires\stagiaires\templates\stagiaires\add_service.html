{% extends 'stagiaires/base.html' %}

{% block title %}Ajouter un Service{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-plus me-2"></i>Ajouter un nouveau service
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="{{ form.nom.id_for_label }}" class="form-label">
                                        <i class="fas fa-tag me-1"></i>Nom du service *
                                    </label>
                                    {{ form.nom }}
                                    {% if form.nom.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.nom.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.code_service.id_for_label }}" class="form-label">
                                        <i class="fas fa-code me-1"></i>Code du service *
                                    </label>
                                    {{ form.code_service }}
                                    <div class="form-text">Code unique (ex: IT, RH, COMPTA)</div>
                                    {% if form.code_service.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.code_service.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-1"></i>Description
                            </label>
                            {{ form.description }}
                            <div class="form-text">Description détaillée du service et de ses missions</div>
                            {% if form.description.errors %}
                                <div class="text-danger small">
                                    {% for error in form.description.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <!-- Commentons temporairement la section responsable -->
                            <!--
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="{{ form.responsable.id_for_label }}" class="form-label">
                                        <i class="fas fa-user-tie me-1"></i>Responsable du service
                                    </label>
                                    {{ form.responsable }}
                                    <div class="form-text">Utilisateur responsable de ce service</div>
                                    {% if form.responsable.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.responsable.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.actif.id_for_label }}" class="form-label">
                                        <i class="fas fa-toggle-on me-1"></i>Statut
                                    </label>
                                    <div class="form-check">
                                        {{ form.actif }}
                                        <label class="form-check-label" for="{{ form.actif.id_for_label }}">
                                            Service actif
                                        </label>
                                    </div>
                                    <div class="form-text">Un service inactif n'apparaîtra pas dans les formulaires</div>
                                    {% if form.actif.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.actif.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'services_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Enregistrer le service
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Aide -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li><strong>Nom du service :</strong> Nom complet et descriptif du service</li>
                        <li><strong>Code du service :</strong> Identifiant court et unique (sera automatiquement converti en majuscules)</li>
                        <li><strong>Description :</strong> Détails sur les missions et responsabilités du service</li>
                        <li><strong>Responsable :</strong> Utilisateur qui supervise ce service (optionnel)</li>
                        <li><strong>Statut :</strong> Seuls les services actifs apparaîtront dans les formulaires de stagiaires</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

