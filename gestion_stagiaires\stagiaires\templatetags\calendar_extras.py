from django import template

register = template.Library()

@register.filter
def lookup(dictionary, key):
    """
    Filtre pour accéder aux valeurs d'un dictionnaire dans les templates Django
    Usage: {{ dict|lookup:key }}
    """
    if dictionary and key:
        return dictionary.get(str(key), [])
    return []

@register.filter
def get_item(dictionary, key):
    """
    Autre nom pour le même filtre
    Usage: {{ dict|get_item:key }}
    """
    return lookup(dictionary, key)
