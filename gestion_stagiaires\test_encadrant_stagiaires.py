#!/usr/bin/env python
"""
Test simple pour vérifier les stagiaires de l'encadrant
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire
from datetime import datetime, timedelta
import calendar

User = get_user_model()

def test_encadrant_stagiaires():
    """Test simple des stagiaires de l'encadrant"""
    
    print("=== TEST STAGIAIRES ENCADRANT ===")
    
    # Récupérer l'encadrant
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    print(f"✅ Encadrant: {encadrant.get_full_name()}")
    
    # Récupérer ses stagiaires
    stagiaires = Stagiaire.objects.filter(encadrant=encadrant)
    print(f"📋 Total stagiaires: {stagiaires.count()}")
    
    # Date actuelle
    today = datetime.now()
    print(f"📅 Date actuelle: {today.date()}")
    print(f"📅 Mois actuel: {calendar.month_name[today.month]} {today.year}")
    
    # Calculer les dates du mois
    start_date = datetime(today.year, today.month, 1)
    days_in_month = calendar.monthrange(today.year, today.month)[1]
    end_date = datetime(today.year, today.month, days_in_month)
    
    print(f"📅 Période mois: {start_date.date()} → {end_date.date()}")
    
    # Stagiaires en cours ce mois
    stagiaires_mois = Stagiaire.objects.filter(
        encadrant=encadrant,
        date_debut__lte=end_date,
        date_fin__gte=start_date
    )
    
    print(f"\n🎯 Stagiaires pour le calendrier ce mois: {stagiaires_mois.count()}")
    
    for stagiaire in stagiaires_mois:
        print(f"   ✅ {stagiaire.nom_complet}")
        print(f"      📅 {stagiaire.date_debut} → {stagiaire.date_fin}")
        
        # Vérifier les conditions
        condition1 = stagiaire.date_debut <= end_date.date()
        condition2 = stagiaire.date_fin >= start_date.date()
        
        print(f"      🔍 Début <= fin_mois: {condition1}")
        print(f"      🔍 Fin >= début_mois: {condition2}")
        print()
    
    # Test avec le mois prochain (août)
    print(f"📅 TEST MOIS PROCHAIN (Août 2025):")
    
    start_aout = datetime(2025, 8, 1)
    end_aout = datetime(2025, 8, 31)
    
    stagiaires_aout = Stagiaire.objects.filter(
        encadrant=encadrant,
        date_debut__lte=end_aout,
        date_fin__gte=start_aout
    )
    
    print(f"   Stagiaires en août: {stagiaires_aout.count()}")
    for stagiaire in stagiaires_aout:
        print(f"      ✅ {stagiaire.nom_complet} ({stagiaire.date_debut} → {stagiaire.date_fin})")

if __name__ == '__main__':
    test_encadrant_stagiaires()
