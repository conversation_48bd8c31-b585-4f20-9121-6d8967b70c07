#!/usr/bin/env python
"""
Script de test pour vérifier la fonctionnalité de rencontre avec les stagiaires
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service, Tache
from django.utils import timezone
from datetime import date, timedelta

User = get_user_model()

def test_rencontre_functionality():
    """Test de la fonctionnalité de rencontre"""
    
    print("🧪 TEST DE LA FONCTIONNALITÉ RENCONTRE")
    print("=" * 50)
    
    # 1. Vérifier qu'il y a des utilisateurs encadrants
    encadrants = User.objects.filter(role='ENCADRANT', is_active=True)
    print(f"📊 Encadrants trouvés : {encadrants.count()}")
    
    if not encadrants.exists():
        print("❌ Aucun encadrant trouvé. Création d'un encadrant de test...")
        # Créer un service de test
        service_test, created = Service.objects.get_or_create(
            nom="Service Test",
            defaults={
                'code_service': 'TEST',
                'description': 'Service de test pour les fonctionnalités',
                'actif': True
            }
        )
        
        # Créer un encadrant de test
        encadrant_test = User.objects.create_user(
            username='encadrant_test',
            email='<EMAIL>',
            password='test123',
            role='ENCADRANT',
            service=service_test,
            first_name='Jean',
            last_name='Dupont'
        )
        print(f"✅ Encadrant créé : {encadrant_test.username}")
        encadrants = [encadrant_test]
    
    # 2. Vérifier qu'il y a des stagiaires
    stagiaires = Stagiaire.objects.all()
    print(f"📊 Stagiaires trouvés : {stagiaires.count()}")
    
    if not stagiaires.exists():
        print("❌ Aucun stagiaire trouvé. Création d'un stagiaire de test...")
        encadrant = encadrants.first()
        
        stagiaire_test = Stagiaire.objects.create(
            nom='Martin',
            prenom='Alice',
            email='<EMAIL>',
            telephone='0123456789',
            date_naissance=date(2000, 1, 1),
            departement='IT',
            service=encadrant.service,
            encadrant=encadrant,
            date_debut=date.today(),
            date_fin=date.today() + timedelta(days=90),
            etablissement='Université Test',
            niveau_etude='Master',
            specialite='Informatique',
            statut='EN_COURS'
        )
        print(f"✅ Stagiaire créé : {stagiaire_test.nom_complet}")
        stagiaires = [stagiaire_test]
    
    # 3. Tester la création de tâches
    stagiaire = stagiaires.first()
    encadrant = encadrants.first()
    
    print(f"\n📝 Test de création de tâches pour {stagiaire.nom_complet}")
    
    # Créer quelques tâches de test
    taches_test = [
        {
            'titre': 'Analyse des besoins',
            'description': 'Analyser les besoins du projet et rédiger un document de spécifications',
            'priorite': 'HAUTE',
            'date_fin_prevue': date.today() + timedelta(days=7)
        },
        {
            'titre': 'Développement du prototype',
            'description': 'Développer un prototype fonctionnel de l\'application',
            'priorite': 'NORMALE',
            'date_fin_prevue': date.today() + timedelta(days=14)
        },
        {
            'titre': 'Tests et validation',
            'description': 'Effectuer les tests unitaires et d\'intégration',
            'priorite': 'NORMALE',
            'date_fin_prevue': date.today() + timedelta(days=21)
        }
    ]
    
    taches_creees = []
    for tache_data in taches_test:
        tache = Tache.objects.create(
            stagiaire=stagiaire,
            creee_par=encadrant,
            **tache_data
        )
        taches_creees.append(tache)
        print(f"  ✅ Tâche créée : {tache.titre}")
    
    # 4. Vérifier les tâches créées
    total_taches = Tache.objects.filter(stagiaire=stagiaire).count()
    print(f"\n📊 Total des tâches pour {stagiaire.nom_complet} : {total_taches}")
    
    # 5. Afficher les URLs importantes
    print(f"\n🔗 URLs importantes :")
    print(f"  • Liste des stagiaires : http://127.0.0.1:8000/stagiaires/")
    print(f"  • Rencontre avec {stagiaire.prenom} : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/rencontre/")
    print(f"  • Détail du stagiaire : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/")
    
    # 6. Informations de connexion
    print(f"\n🔐 Informations de connexion :")
    print(f"  • Encadrant : {encadrant.username}")
    print(f"  • Email : {encadrant.email}")
    print(f"  • Service : {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    print(f"\n✅ Test terminé avec succès !")
    print(f"📧 Note : Les emails seront affichés dans la console (EMAIL_BACKEND = console)")
    
    return {
        'encadrant': encadrant,
        'stagiaire': stagiaire,
        'taches': taches_creees
    }

if __name__ == '__main__':
    try:
        result = test_rencontre_functionality()
        print(f"\n🎯 RÉSUMÉ :")
        print(f"  • Encadrant : {result['encadrant'].username}")
        print(f"  • Stagiaire : {result['stagiaire'].nom_complet}")
        print(f"  • Tâches créées : {len(result['taches'])}")
        
    except Exception as e:
        print(f"❌ Erreur lors du test : {e}")
        import traceback
        traceback.print_exc()
