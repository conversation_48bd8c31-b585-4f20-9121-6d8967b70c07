#!/usr/bin/env python
"""
Script pour corriger les permissions des administrateurs
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

def fix_admin_permissions():
    """Corriger les permissions des administrateurs"""
    
    print("=== Correction des permissions administrateurs ===")
    
    # Trouver tous les utilisateurs avec le rôle ADMIN
    admin_users = User.objects.filter(role='ADMIN')
    
    print(f"Nombre d'utilisateurs avec le rôle ADMIN: {admin_users.count()}")
    
    for admin in admin_users:
        print(f"\n👤 Utilisateur: {admin.username}")
        print(f"   Email: {admin.email}")
        print(f"   Nom complet: {admin.get_full_name()}")
        print(f"   Rôle: {admin.role}")
        print(f"   is_superuser: {admin.is_superuser}")
        print(f"   is_staff: {admin.is_staff}")
        print(f"   is_active: {admin.is_active}")
        print(f"   Propriété is_admin: {admin.is_admin}")
        
        # Corriger les permissions si nécessaire
        needs_update = False
        
        if not admin.is_superuser:
            admin.is_superuser = True
            needs_update = True
            print("   ✅ is_superuser activé")
        
        if not admin.is_staff:
            admin.is_staff = True
            needs_update = True
            print("   ✅ is_staff activé")
        
        if not admin.is_active:
            admin.is_active = True
            needs_update = True
            print("   ✅ is_active activé")
        
        if needs_update:
            admin.save()
            print("   💾 Utilisateur mis à jour")
        else:
            print("   ✅ Permissions déjà correctes")
    
    # Créer un admin par défaut si aucun n'existe
    if admin_users.count() == 0:
        print("\n🚨 Aucun administrateur trouvé. Création d'un admin par défaut...")
        
        admin = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='Admin',
            last_name='Système',
            role='ADMIN',
            is_superuser=True,
            is_staff=True,
            is_active=True
        )
        
        print(f"✅ Administrateur créé: {admin.username}")
        print(f"   Mot de passe: admin123")
        print(f"   Email: {admin.email}")
    
    print("\n=== Vérification finale ===")
    
    # Vérifier que tous les admins ont les bonnes permissions
    valid_admins = User.objects.filter(
        role='ADMIN',
        is_superuser=True,
        is_staff=True,
        is_active=True
    )
    
    print(f"✅ Administrateurs valides: {valid_admins.count()}")
    
    for admin in valid_admins:
        print(f"   👑 {admin.username} - {admin.get_full_name()}")
        print(f"      is_admin property: {admin.is_admin}")
    
    # Vérifier les admins invalides
    invalid_admins = User.objects.filter(
        role='ADMIN'
    ).exclude(
        is_superuser=True,
        is_staff=True,
        is_active=True
    )
    
    if invalid_admins.count() > 0:
        print(f"\n⚠️  Administrateurs avec permissions incomplètes: {invalid_admins.count()}")
        for admin in invalid_admins:
            print(f"   ❌ {admin.username} - Permissions à corriger")
    
    print("\n=== Test de la logique d'affichage ===")
    
    for admin in valid_admins:
        # Simuler la condition du template
        show_admin_menu = admin.role == 'ADMIN' and admin.is_superuser
        show_admin_dashboard = admin.role == 'ADMIN' and admin.is_superuser
        
        print(f"\n👤 {admin.username}:")
        print(f"   Menu Administration affiché: {show_admin_menu}")
        print(f"   Dashboard Admin affiché: {show_admin_dashboard}")
        print(f"   Propriété is_admin: {admin.is_admin}")
    
    print("\n=== Correction terminée ===")

if __name__ == '__main__':
    fix_admin_permissions()
