#!/usr/bin/env python
"""
Script pour vérifier l'unicité du service par encadrant
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Service

User = get_user_model()

def verifier_unicite_service_encadrant():
    """Vérifier que chaque encadrant n'a qu'un seul service"""
    
    print("=== VÉRIFICATION UNICITÉ SERVICE PAR ENCADRANT ===")
    
    # 1. Analyser tous les encadrants
    print("👥 ANALYSE DES ENCADRANTS:")
    
    encadrants = User.objects.filter(role='ENCADRANT', is_active=True)
    print(f"   Total encadrants actifs: {encadrants.count()}")
    
    encadrants_avec_service = 0
    encadrants_sans_service = 0
    problemes_detectes = []
    
    for encadrant in encadrants:
        print(f"\n   👨‍💼 {encadrant.get_full_name()}")
        print(f"      Email: {encadrant.email}")
        print(f"      Username: {encadrant.username}")
        
        if encadrant.service:
            encadrants_avec_service += 1
            print(f"      ✅ Service: {encadrant.service.nom} (ID: {encadrant.service.id})")
        else:
            encadrants_sans_service += 1
            print(f"      ❌ Service: Aucun")
            problemes_detectes.append({
                'type': 'sans_service',
                'encadrant': encadrant,
                'description': 'Encadrant sans service assigné'
            })
    
    print(f"\n📊 STATISTIQUES:")
    print(f"   Encadrants avec service: {encadrants_avec_service}")
    print(f"   Encadrants sans service: {encadrants_sans_service}")
    
    # 2. Vérifier les services et leurs encadrants
    print(f"\n🏢 ANALYSE DES SERVICES:")
    
    services = Service.objects.filter(actif=True)
    print(f"   Total services actifs: {services.count()}")
    
    for service in services:
        encadrants_service = User.objects.filter(
            role='ENCADRANT', 
            service=service, 
            is_active=True
        )
        
        print(f"\n   🏢 Service: {service.nom}")
        print(f"      Encadrants assignés: {encadrants_service.count()}")
        
        if encadrants_service.count() == 0:
            print(f"      ⚠️ Aucun encadrant assigné à ce service")
            problemes_detectes.append({
                'type': 'service_sans_encadrant',
                'service': service,
                'description': 'Service sans encadrant'
            })
        elif encadrants_service.count() > 1:
            print(f"      ℹ️ Plusieurs encadrants pour ce service:")
            for enc in encadrants_service:
                print(f"         • {enc.get_full_name()}")
        else:
            enc = encadrants_service.first()
            print(f"      ✅ Encadrant unique: {enc.get_full_name()}")
    
    # 3. Vérifier la cohérence du modèle
    print(f"\n🔍 VÉRIFICATION COHÉRENCE MODÈLE:")
    
    # Vérifier que le champ service est bien unique par utilisateur
    print(f"   📋 Structure du modèle CustomUser:")
    
    from django.db import models
    user_model = User
    service_field = user_model._meta.get_field('service')
    
    print(f"      Champ service: {service_field}")
    print(f"      Type: {type(service_field)}")
    print(f"      Null autorisé: {service_field.null}")
    print(f"      Blank autorisé: {service_field.blank}")
    
    if hasattr(service_field, 'unique'):
        print(f"      Contrainte unique: {service_field.unique}")
    else:
        print(f"      Contrainte unique: Non applicable (ForeignKey)")
    
    # 4. Détecter les doublons potentiels
    print(f"\n🔍 DÉTECTION DOUBLONS POTENTIELS:")
    
    # Vérifier s'il y a des utilisateurs avec le même email/username
    emails_dupliques = []
    usernames_dupliques = []
    
    for encadrant in encadrants:
        # Vérifier email
        autres_meme_email = User.objects.filter(
            email=encadrant.email,
            role='ENCADRANT'
        ).exclude(id=encadrant.id)
        
        if autres_meme_email.exists():
            emails_dupliques.append({
                'email': encadrant.email,
                'encadrants': [encadrant] + list(autres_meme_email)
            })
        
        # Vérifier username
        autres_meme_username = User.objects.filter(
            username=encadrant.username,
            role='ENCADRANT'
        ).exclude(id=encadrant.id)
        
        if autres_meme_username.exists():
            usernames_dupliques.append({
                'username': encadrant.username,
                'encadrants': [encadrant] + list(autres_meme_username)
            })
    
    if emails_dupliques:
        print(f"   ⚠️ Emails dupliqués détectés:")
        for dup in emails_dupliques:
            print(f"      Email: {dup['email']}")
            for enc in dup['encadrants']:
                print(f"         • {enc.get_full_name()} (ID: {enc.id})")
    
    if usernames_dupliques:
        print(f"   ⚠️ Usernames dupliqués détectés:")
        for dup in usernames_dupliques:
            print(f"      Username: {dup['username']}")
            for enc in dup['encadrants']:
                print(f"         • {enc.get_full_name()} (ID: {enc.id})")
    
    # 5. Recommandations
    print(f"\n💡 RECOMMANDATIONS:")
    
    if problemes_detectes:
        print(f"   ⚠️ {len(problemes_detectes)} problèmes détectés:")
        
        for probleme in problemes_detectes:
            if probleme['type'] == 'sans_service':
                print(f"      • {probleme['encadrant'].get_full_name()}: {probleme['description']}")
                print(f"        → Assigner un service à cet encadrant")
            elif probleme['type'] == 'service_sans_encadrant':
                print(f"      • Service {probleme['service'].nom}: {probleme['description']}")
                print(f"        → Assigner un encadrant ou désactiver le service")
    else:
        print(f"   ✅ Aucun problème détecté")
    
    # 6. Vérifier les contraintes de base de données
    print(f"\n🔒 VÉRIFICATION CONTRAINTES DB:")
    
    try:
        from django.db import connection
        cursor = connection.cursor()
        
        # Vérifier la structure de la table
        cursor.execute("PRAGMA table_info(stagiaires_customuser)")
        columns = cursor.fetchall()
        
        service_column = None
        for col in columns:
            if col[1] == 'service_id':  # nom de la colonne pour ForeignKey
                service_column = col
                break
        
        if service_column:
            print(f"   📋 Colonne service_id trouvée:")
            print(f"      Type: {service_column[2]}")
            print(f"      Not Null: {bool(service_column[3])}")
            print(f"      Default: {service_column[4]}")
        
        # Vérifier les contraintes
        cursor.execute("PRAGMA foreign_key_list(stagiaires_customuser)")
        foreign_keys = cursor.fetchall()
        
        service_fk = None
        for fk in foreign_keys:
            if fk[3] == 'service_id':
                service_fk = fk
                break
        
        if service_fk:
            print(f"   🔗 Contrainte Foreign Key service:")
            print(f"      Table référencée: {service_fk[2]}")
            print(f"      Colonne référencée: {service_fk[4]}")
        
    except Exception as e:
        print(f"   ⚠️ Erreur lors de la vérification DB: {e}")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ VÉRIFICATION UNICITÉ:")
    print("")
    print("✅ POINTS VÉRIFIÉS :")
    print("   • Nombre d'encadrants par service ✅")
    print("   • Encadrants sans service ✅")
    print("   • Services sans encadrant ✅")
    print("   • Doublons potentiels ✅")
    print("   • Structure du modèle ✅")
    print("")
    print("✅ CONTRAINTE UNICITÉ :")
    print("   • Un encadrant = Un service maximum ✅")
    print("   • Un service = Plusieurs encadrants possibles ✅")
    print("   • Relation ForeignKey respectée ✅")
    print("")
    if problemes_detectes:
        print(f"⚠️ ACTIONS REQUISES : {len(problemes_detectes)} problèmes à corriger")
    else:
        print("🎉 UNICITÉ RESPECTÉE : Aucun problème détecté !")
    print(f"{'='*60}")

if __name__ == '__main__':
    verifier_unicite_service_encadrant()
