{% extends 'stagiaires/base.html' %}

{% block title %}Sujets de stage | Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="card shadow-sm">
        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
            <h3 class="card-title mb-0">
                <i class="fas fa-tasks me-2"></i>
                Sujets de stage
            </h3>
            <div>
                <a href="{% url 'add_sujet' %}" class="btn btn-light btn-sm">
                    <i class="fas fa-plus me-1"></i>Ajouter un sujet
                </a>
            </div>
        </div>
<div class="card-body">
    {% if sujets %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Titre</th>
                        <th>Thématique</th>
                        <th>Du<PERSON>e (jours)</th>
                        <th>Statut</th>
                        <th><PERSON><PERSON><PERSON> par</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sujet in sujets %}
                        <tr>
                            <td>{{ sujet.titre }}</td>
                            <td>
                                {% if sujet.thematique %}
                                    {{ sujet.thematique.titre }}
                                {% else %}
                                    <span class="text-muted">Non définie</span>
                                {% endif %}
                            </td>
                            <td>{{ sujet.duree_recommandee|default:"N/A" }}</td>
                            <td>
                                {% if sujet.actif %}
                                    <span class="badge bg-success">Actif</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactif</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if sujet.cree_par %}
                                    {{ sujet.cree_par.get_full_name|default:sujet.cree_par.username }}
                                {% else %}
                                    <span class="text-muted">Inconnu</span>
                                {% endif %}
                            </td>
                 <td>
    <a href="{% url 'edit_sujet' sujet.id %}" class="btn btn-sm btn-outline-primary" title="Modifier">
        <i class="fas fa-edit"></i>
    </a>
    
    <!-- Bouton Activer/Désactiver -->
    {% if user.role == 'ADMIN' or user.role == 'RH' or sujet.cree_par == user %}
        <a href="{% url 'toggle_sujet' sujet.id %}" class="btn btn-sm btn-outline-warning">
            {% if sujet.actif %}
                <i class="fas fa-toggle-off"></i>
            {% else %}
                <i class="fas fa-toggle-on"></i>
            {% endif %}
        </a>
    {% endif %}
    
    <!-- Bouton Supprimer (UNIQUEMENT pour ADMIN/RH) -->
    {% if user.role == 'ADMIN' or user.role == 'RH' %}
        <a href="{% url 'delete_sujet' sujet.id %}" class="btn btn-sm btn-outline-danger" 
           title="Supprimer" 
           onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce sujet ?');">
            <i class="fas fa-trash"></i>
        </a>
    {% endif %}
</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Aucun sujet n'a été créé pour le moment.
        </div>
    {% endif %}
</div>
    </div>
</div>
{% endblock %}



