@startuml Diagramme_Activite_Gestion_Stagiaire

!theme plain
skinparam activityDiagramTitleFontSize 16
skinparam activityFontSize 12

title Diagramme d'Activité - Processus Complet de Gestion d'un Stagiaire

|#LightBlue|RH|
start
:Réception demande de stage;
:<PERSON><PERSON><PERSON> profil stagiaire;
note right : Saisie des informations\npersonnelles et académiques

:Uploader documents\n(CV, Convention, Assurance);

if (Documents complets ?) then (oui)
  :Valider les documents;
  :Assigner un service;
  :Assigner un encadrant;
else (non)
  :Demander documents\nmanquants;
  stop
endif

|#LightGreen|Encadrant|
:Recevoir notification\nnouveau stagiaire;
:Consulter profil stagiaire;
:Consulter CV;

:Planifier rencontre\nd'accueil;

|#LightYellow|Système|
:Envoyer notification\nrencontre programmée;

|#LightGreen|Encadrant|
:Organiser rencontre\navec stagiaire;
:Consulter CV pendant\nla rencontre;
:Définir objectifs\ndu stage;

:Créer tâches initiales;
note right : Tâches d'intégration :\n- Formation outils\n- Présentation équipe\n- Lecture documentation

:Définir priorités\net échéances;
:Envoyer récapitulatif\npar email;

|#LightYellow|Système|
:Calculer statut période\n(jours restants);

if (Plus de 10 jours ?) then (oui)
  :Afficher statut VERT;
elseif (Exactement 10 jours ?) then (oui)
  :Afficher statut ORANGE;
elseif (Moins de 10 jours ?) then (oui)
  :Afficher statut ROUGE;
else (terminé)
  :Afficher statut NOIR;
endif

|#LightGreen|Encadrant|
repeat
  :Suivre progression\ndes tâches;
  :Organiser rencontres\nde suivi;
  
  if (Nouvelles tâches ?) then (oui)
    :Ajouter nouvelles tâches;
    :Envoyer mise à jour\npar email;
  endif
  
  if (Tâches terminées ?) then (oui)
    :Valider tâches;
    :Mettre à jour statuts;
  endif
  
  :Évaluer progression\nglobale;
  
repeat while (Stage en cours ?) is (oui)
-> non;

:Demander rapport\nde stage;

|#LightBlue|RH|
:Recevoir rapport\nde stage;
:Valider rapport;

if (Rapport satisfaisant ?) then (oui)
  :Créer contrat de stage;
  :Faire signer contrat;
  :Archiver contrat;
  :Générer attestation\nde fin de stage;
else (non)
  :Demander corrections;
  |#LightGreen|Encadrant|
  :Accompagner corrections;
  |#LightBlue|RH|
endif

:Clôturer dossier\nstagiaire;
:Archiver documents;

|#LightYellow|Système|
:Mettre à jour statut\nstagiaire = TERMINÉ;
:Envoyer notifications\nde fin de stage;

stop

note top : Processus complet de gestion\nd'un stagiaire du début à la fin

' Légende des couleurs
note bottom
  **Légende des couleurs :**
  • Bleu clair : Actions RH
  • Vert clair : Actions Encadrant  
  • Jaune clair : Actions Système automatiques
end note

@enduml
