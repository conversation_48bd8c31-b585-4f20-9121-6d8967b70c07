#!/usr/bin/env python
"""
Script pour générer les diagrammes PlantUML
"""

import os
import subprocess
import sys

def generer_diagrammes():
    """Génère tous les diagrammes PlantUML"""
    
    print("=== Génération des diagrammes PlantUML ===")
    
    # Vérifier si PlantUML est installé
    try:
        result = subprocess.run(['plantuml', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ PlantUML trouvé")
        else:
            print("❌ PlantUML non trouvé")
            print("📥 Installation requise:")
            print("   1. Téléchargez plantuml.jar depuis http://plantuml.com/download")
            print("   2. Ou installez via: npm install -g plantuml")
            print("   3. Ou utilisez l'extension VSCode PlantUML")
            return
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ PlantUML non trouvé dans le PATH")
        print("📥 Installation requise:")
        print("   1. Téléchargez plantuml.jar depuis http://plantuml.com/download")
        print("   2. Ou installez via: npm install -g plantuml")
        print("   3. Ou utilisez l'extension VSCode PlantUML")
        return
    
    # Créer le dossier de sortie
    output_dir = "diagrammes_output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 Dossier créé: {output_dir}")
    
    # Lire le fichier markdown avec les diagrammes
    markdown_file = "diagrammes_plantuml.md"
    if not os.path.exists(markdown_file):
        print(f"❌ Fichier {markdown_file} non trouvé")
        return
    
    print(f"📖 Lecture du fichier: {markdown_file}")
    
    with open(markdown_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extraire et générer chaque diagramme
    diagrammes = [
        ("Diagramme_Classes_Gestion_Stagiaires", "diagramme_classes"),
        ("Diagramme_Cas_Usage_Gestion_Stagiaires", "diagramme_cas_usage"),
        ("Sequence_Ajout_Stagiaire", "sequence_ajout_stagiaire"),
        ("Sequence_Validation_Convention", "sequence_validation_convention"),
        ("Sequence_Filtrage_Stagiaires", "sequence_filtrage_stagiaires"),
        ("Activite_Gestion_Stage", "activite_gestion_stage"),
        ("Etats_Stagiaire", "etats_stagiaire")
    ]
    
    for diagram_name, file_name in diagrammes:
        print(f"\n🔄 Génération: {diagram_name}")
        
        # Extraire le code PlantUML
        start_marker = f"@startuml {diagram_name}"
        end_marker = "@enduml"
        
        start_idx = content.find(start_marker)
        if start_idx == -1:
            print(f"   ❌ Diagramme {diagram_name} non trouvé")
            continue
        
        end_idx = content.find(end_marker, start_idx)
        if end_idx == -1:
            print(f"   ❌ Fin du diagramme {diagram_name} non trouvée")
            continue
        
        diagram_code = content[start_idx:end_idx + len(end_marker)]
        
        # Sauvegarder le code PlantUML
        puml_file = os.path.join(output_dir, f"{file_name}.puml")
        with open(puml_file, 'w', encoding='utf-8') as f:
            f.write(diagram_code)
        
        print(f"   💾 Sauvegardé: {puml_file}")
        
        # Générer l'image
        try:
            result = subprocess.run([
                'plantuml', 
                '-tpng',  # Format PNG
                '-o', os.path.abspath(output_dir),  # Dossier de sortie
                puml_file
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                png_file = os.path.join(output_dir, f"{file_name}.png")
                print(f"   ✅ Image générée: {png_file}")
            else:
                print(f"   ❌ Erreur génération: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"   ⏰ Timeout lors de la génération")
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
    
    print(f"\n📊 Résumé:")
    print(f"   📁 Dossier de sortie: {output_dir}")
    print(f"   📄 Fichiers .puml: Code source PlantUML")
    print(f"   🖼️ Fichiers .png: Images générées")
    
    # Lister les fichiers générés
    if os.path.exists(output_dir):
        files = os.listdir(output_dir)
        puml_files = [f for f in files if f.endswith('.puml')]
        png_files = [f for f in files if f.endswith('.png')]
        
        print(f"\n📋 Fichiers générés:")
        print(f"   Code source ({len(puml_files)}):")
        for f in sorted(puml_files):
            print(f"     • {f}")
        
        print(f"   Images ({len(png_files)}):")
        for f in sorted(png_files):
            print(f"     • {f}")
    
    print(f"\n🎯 Utilisation:")
    print(f"   • Ouvrez les fichiers .png pour voir les diagrammes")
    print(f"   • Modifiez les fichiers .puml pour personnaliser")
    print(f"   • Utilisez l'extension VSCode PlantUML pour l'édition")

def afficher_aide():
    """Affiche l'aide pour l'utilisation des diagrammes"""
    
    print("=== Guide d'utilisation des diagrammes PlantUML ===")
    print()
    print("📋 Diagrammes disponibles:")
    print("   1. Diagramme de Classes - Structure complète du système")
    print("   2. Diagramme de Cas d'Usage - Fonctionnalités par rôle")
    print("   3. Diagrammes de Séquence - Processus détaillés")
    print("   4. Diagramme d'Activité - Workflow complet")
    print("   5. Diagramme d'État - Cycle de vie des stagiaires")
    print()
    print("🛠️ Outils recommandés:")
    print("   • VSCode avec extension PlantUML")
    print("   • PlantUML en ligne: http://www.plantuml.com/plantuml/")
    print("   • PlantUML Desktop")
    print()
    print("📝 Modification des diagrammes:")
    print("   1. Éditez le fichier diagrammes_plantuml.md")
    print("   2. Relancez ce script pour régénérer")
    print("   3. Ou utilisez l'aperçu en temps réel dans VSCode")
    print()
    print("🎨 Personnalisation:")
    print("   • Modifiez les couleurs avec skinparam")
    print("   • Ajoutez des notes avec 'note'")
    print("   • Changez la disposition avec !define")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        afficher_aide()
    else:
        generer_diagrammes()
