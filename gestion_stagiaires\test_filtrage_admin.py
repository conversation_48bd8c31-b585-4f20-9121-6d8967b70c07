#!/usr/bin/env python
"""
Test du filtrage des stagiaires pour les admins
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service
from stagiaires.views import filter_stagiaires_by_user_role
from datetime import date, timedelta

User = get_user_model()

def test_filtrage_admin():
    """Test du filtrage des stagiaires pour les admins"""
    
    print("=== Test du filtrage des stagiaires pour les admins ===")
    
    # 1. Récupérer les utilisateurs
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not admin:
        print("❌ Aucun admin trouvé")
        return
    
    print(f"✅ Admin: {admin.username}")
    print(f"✅ Encadrant: {encadrant.get_full_name() if encadrant else 'Aucun'}")
    
    # 2. Compter tous les stagiaires en base
    print("\n📊 Statistiques des stagiaires:")
    
    total_stagiaires = Stagiaire.objects.count()
    print(f"   Total en base: {total_stagiaires}")
    
    stagiaires_par_statut = {}
    for statut, _ in Stagiaire.STATUT_CHOICES:
        count = Stagiaire.objects.filter(statut=statut).count()
        stagiaires_par_statut[statut] = count
        print(f"   {statut}: {count}")
    
    # 3. Test du filtrage pour l'admin
    print(f"\n🔍 Test du filtrage pour l'admin:")
    
    # Récupérer tous les stagiaires sans filtrage
    tous_stagiaires = Stagiaire.objects.all()
    print(f"   Tous les stagiaires (sans filtrage): {tous_stagiaires.count()}")
    
    # Appliquer le filtrage admin
    stagiaires_admin = filter_stagiaires_by_user_role(admin, tous_stagiaires)
    print(f"   Stagiaires visibles par admin: {stagiaires_admin.count()}")
    
    if stagiaires_admin.count() == tous_stagiaires.count():
        print("   ✅ L'admin voit tous les stagiaires (correct)")
    else:
        print("   ❌ L'admin ne voit pas tous les stagiaires (problème)")
        
        # Analyser la différence
        ids_tous = set(tous_stagiaires.values_list('id', flat=True))
        ids_admin = set(stagiaires_admin.values_list('id', flat=True))
        ids_manquants = ids_tous - ids_admin
        
        if ids_manquants:
            print(f"   Stagiaires manquants: {len(ids_manquants)}")
            for stagiaire_id in list(ids_manquants)[:5]:  # Afficher les 5 premiers
                stagiaire = Stagiaire.objects.get(id=stagiaire_id)
                print(f"     • {stagiaire.nom_complet} (ID: {stagiaire_id})")
    
    # 4. Test via la vue web
    print(f"\n🌐 Test via la vue web:")
    
    client = Client()
    client.force_login(admin)
    
    response = client.get('/stagiaires/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        # Analyser le contenu HTML pour compter les stagiaires affichés
        content = response.content.decode('utf-8')
        
        # Compter les lignes de tableau (approximatif)
        import re
        rows = re.findall(r'<tr[^>]*>.*?</tr>', content, re.DOTALL)
        # Exclure l'en-tête
        data_rows = [row for row in rows if 'table-dark' not in row and 'thead' not in row]
        
        print(f"   Lignes de données dans le HTML: {len(data_rows)}")
        
        # Vérifier s'il y a un message "Aucun stagiaire"
        if 'Aucun stagiaire' in content or 'No stagiaires' in content:
            print("   ⚠️ Message 'Aucun stagiaire' détecté")
        
        # Vérifier la présence de noms de stagiaires
        stagiaires_recents = Stagiaire.objects.order_by('-date_creation')[:3]
        for stagiaire in stagiaires_recents:
            if stagiaire.nom in content and stagiaire.prenom in content:
                print(f"   ✅ {stagiaire.nom_complet} trouvé dans la page")
            else:
                print(f"   ❌ {stagiaire.nom_complet} non trouvé dans la page")
    
    # 5. Créer un nouveau stagiaire et vérifier s'il apparaît
    print(f"\n➕ Test avec un nouveau stagiaire:")
    
    # Supprimer le stagiaire de test s'il existe
    Stagiaire.objects.filter(email='<EMAIL>').delete()
    
    # Créer un nouveau stagiaire
    nouveau_stagiaire = Stagiaire.objects.create(
        nom='AdminTest',
        prenom='Visibility',
        email='<EMAIL>',
        date_naissance=date(2000, 1, 1),
        departement='IT',
        encadrant=encadrant,
        date_debut=date.today(),
        date_fin=date.today() + timedelta(days=30),
        etablissement='Test Admin Visibility',
        niveau_etude='Master',
        specialite='Test',
        statut='EN_COURS',
        cree_par=admin
    )
    
    print(f"   ✅ Nouveau stagiaire créé: {nouveau_stagiaire.nom_complet} (ID: {nouveau_stagiaire.id})")
    
    # Vérifier le filtrage
    stagiaires_admin_apres = filter_stagiaires_by_user_role(admin, Stagiaire.objects.all())
    nouveau_visible = stagiaires_admin_apres.filter(id=nouveau_stagiaire.id).exists()
    
    if nouveau_visible:
        print("   ✅ Le nouveau stagiaire est visible par l'admin")
    else:
        print("   ❌ Le nouveau stagiaire n'est pas visible par l'admin")
    
    # Test via la vue web
    response = client.get('/stagiaires/')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        if 'AdminTest' in content and 'Visibility' in content:
            print("   ✅ Le nouveau stagiaire apparaît dans la page web")
        else:
            print("   ❌ Le nouveau stagiaire n'apparaît pas dans la page web")
    
    # 6. Test avec différents rôles
    print(f"\n👥 Comparaison avec d'autres rôles:")
    
    # Test RH
    user_rh = User.objects.filter(role='RH', is_active=True).first()
    if user_rh:
        stagiaires_rh = filter_stagiaires_by_user_role(user_rh, Stagiaire.objects.all())
        print(f"   RH ({user_rh.username}): {stagiaires_rh.count()} stagiaires")
    
    # Test Encadrant
    if encadrant:
        stagiaires_encadrant = filter_stagiaires_by_user_role(encadrant, Stagiaire.objects.all())
        print(f"   Encadrant ({encadrant.username}): {stagiaires_encadrant.count()} stagiaires")
    
    # Nettoyer
    nouveau_stagiaire.delete()
    print(f"\n🧹 Stagiaire de test supprimé")
    
    print(f"\n{'='*60}")
    print("📊 DIAGNOSTIC:")
    print("Si l'admin ne voit pas tous les stagiaires:")
    print("1. Vérifiez la fonction filter_stagiaires_by_user_role")
    print("2. Vérifiez les permissions dans la vue stagiaires_list_view")
    print("3. Vérifiez s'il y a des filtres cachés dans le template")
    print("4. Vérifiez la base de données pour des contraintes")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_filtrage_admin()
