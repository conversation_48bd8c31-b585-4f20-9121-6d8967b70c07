#!/usr/bin/env python
"""
Test de l'option "Tous les stagiaires" pour les encadrants
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service

User = get_user_model()

def test_tous_stagiaires():
    """Test de l'option 'Tous les stagiaires' pour les encadrants"""
    
    print("=== TEST OPTION 'TOUS LES STAGIAIRES' ===")
    
    # 1. Récupérer l'encadrant et son service
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    print(f"✅ Encadrant: {encadrant.get_full_name()}")
    print(f"📋 Service de l'encadrant: {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    # 2. Compter les stagiaires par catégorie
    print(f"\n📊 COMPTAGE DES STAGIAIRES:")
    
    # Tous les stagiaires
    tous_stagiaires = Stagiaire.objects.all()
    print(f"   🌍 Total stagiaires (tous services): {tous_stagiaires.count()}")
    
    # Stagiaires du service de l'encadrant
    stagiaires_service = Stagiaire.objects.filter(service=encadrant.service)
    print(f"   🏢 Stagiaires du service {encadrant.service.nom}: {stagiaires_service.count()}")
    
    # Stagiaires des autres services
    stagiaires_autres = Stagiaire.objects.exclude(service=encadrant.service)
    print(f"   🏢 Stagiaires d'autres services: {stagiaires_autres.count()}")
    
    # Détail par service
    print(f"\n📋 DÉTAIL PAR SERVICE:")
    services = Service.objects.filter(actif=True)
    for service in services:
        count = Stagiaire.objects.filter(service=service).count()
        print(f"   🏢 {service.nom}: {count} stagiaires")
    
    # 3. Test avec filtre "mon_service"
    print(f"\n📋 TEST FILTRE 'MON SERVICE':")
    
    client = Client()
    client.force_login(encadrant)
    
    response = client.get('/stagiaires/?filtre=mon_service')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Compter les stagiaires affichés
        stagiaires_affiches = 0
        for stagiaire in tous_stagiaires:
            if stagiaire.nom_complet in content:
                stagiaires_affiches += 1
        
        print(f"   📊 Stagiaires affichés avec 'mon_service': {stagiaires_affiches}")
        print(f"   🎯 Attendu: {stagiaires_service.count()} (stagiaires du service)")
        
        if stagiaires_affiches == stagiaires_service.count():
            print(f"   ✅ Filtrage 'mon_service' correct")
        else:
            print(f"   ❌ Problème avec le filtrage 'mon_service'")
    
    # 4. Test avec filtre "tous"
    print(f"\n📋 TEST FILTRE 'TOUS':")
    
    response = client.get('/stagiaires/?filtre=tous')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Compter les stagiaires affichés
        stagiaires_affiches = 0
        stagiaires_details = []
        
        for stagiaire in tous_stagiaires:
            if stagiaire.nom_complet in content:
                stagiaires_affiches += 1
                stagiaires_details.append({
                    'nom': stagiaire.nom_complet,
                    'service': stagiaire.service.nom if stagiaire.service else 'Aucun',
                    'encadrant': stagiaire.encadrant.get_full_name() if stagiaire.encadrant else 'Aucun'
                })
        
        print(f"   📊 Stagiaires affichés avec 'tous': {stagiaires_affiches}")
        print(f"   🎯 Attendu: {tous_stagiaires.count()} (tous les stagiaires)")
        
        if stagiaires_affiches == tous_stagiaires.count():
            print(f"   ✅ Filtrage 'tous' correct - Tous les stagiaires affichés")
        else:
            print(f"   ❌ Problème avec le filtrage 'tous' - Manque {tous_stagiaires.count() - stagiaires_affiches} stagiaires")
        
        # Afficher le détail des stagiaires affichés
        print(f"\n   📋 DÉTAIL DES STAGIAIRES AFFICHÉS:")
        for detail in stagiaires_details:
            print(f"      • {detail['nom']} (Service: {detail['service']}, Encadrant: {detail['encadrant']})")
    
    # 5. Test par défaut (sans filtre)
    print(f"\n📋 TEST PAR DÉFAUT (sans filtre):")
    
    response = client.get('/stagiaires/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Compter les stagiaires affichés
        stagiaires_affiches = 0
        for stagiaire in tous_stagiaires:
            if stagiaire.nom_complet in content:
                stagiaires_affiches += 1
        
        print(f"   📊 Stagiaires affichés par défaut: {stagiaires_affiches}")
        
        # Vérifier quel filtre est actif par défaut
        if 'filtre_actuel' in content:
            if 'mon_service' in content:
                print(f"   🎯 Filtre par défaut: mon_service")
            elif 'tous' in content:
                print(f"   🎯 Filtre par défaut: tous")
    
    # 6. Test des boutons de navigation
    print(f"\n🔘 TEST DES BOUTONS DE NAVIGATION:")
    
    response = client.get('/stagiaires/')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier la présence des boutons
        elements = [
            ('filtre=mon_service', 'Bouton "Mon service"'),
            ('filtre=tous', 'Bouton "Tous"'),
            ('Mon service', 'Texte "Mon service"'),
            ('Tous', 'Texte "Tous"'),
        ]
        
        for element, description in elements:
            if element in content:
                print(f"   ✅ {description} présent")
            else:
                print(f"   ❌ {description} absent")
    
    # 7. Comparaison avec Admin
    print(f"\n👨‍💼 COMPARAISON AVEC ADMIN:")
    
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    if admin:
        client.force_login(admin)
        response = client.get('/stagiaires/')
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            stagiaires_affiches_admin = 0
            for stagiaire in tous_stagiaires:
                if stagiaire.nom_complet in content:
                    stagiaires_affiches_admin += 1
            
            print(f"   📊 Admin voit: {stagiaires_affiches_admin} stagiaires")
            print(f"   📊 Encadrant avec 'tous': {stagiaires_affiches} stagiaires")
            
            if stagiaires_affiches_admin == stagiaires_affiches:
                print(f"   ✅ Encadrant avec 'tous' voit autant que l'admin")
            else:
                print(f"   ⚠️ Différence entre admin et encadrant avec 'tous'")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DU TEST 'TOUS LES STAGIAIRES':")
    print("")
    print("✅ FONCTIONNALITÉS TESTÉES :")
    print("   • Filtre 'mon_service' : Stagiaires du service uniquement")
    print("   • Filtre 'tous' : TOUS les stagiaires (tous services)")
    print("   • Navigation entre les filtres")
    print("   • Comparaison avec les droits admin")
    print("")
    print("✅ COMPORTEMENT ATTENDU :")
    print("   • 'Mon service' = Stagiaires du service de l'encadrant")
    print("   • 'Tous' = Tous les stagiaires (tous services confondus)")
    print("   • Admin = Tous les stagiaires (comme encadrant avec 'tous')")
    print("")
    print("🎉 TEST DE L'OPTION 'TOUS LES STAGIAIRES' TERMINÉ !")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_tous_stagiaires()
