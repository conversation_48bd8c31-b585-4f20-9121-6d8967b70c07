from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service, Tache
from datetime import datetime, timedelta
import random

User = get_user_model()

class Command(BaseCommand):
    help = 'Crée des données de test pour le calendrier'

    def handle(self, *args, **options):
        self.stdout.write('Création des données de test pour le calendrier...')
        
        # Créer un service de test s'il n'existe pas
        service, created = Service.objects.get_or_create(
            nom='Informatique Test',
            defaults={'description': 'Service informatique pour tests'}
        )
        
        # Créer un encadrant de test s'il n'existe pas
        encadrant, created = User.objects.get_or_create(
            username='encadrant_test',
            defaults={
                'email': '<EMAIL>',
                'first_name': '<PERSON>',
                'last_name': '<PERSON><PERSON>',
                'role': 'ENCADRANT',
                'service': service
            }
        )
        if created:
            encadrant.set_password('test123')
            encadrant.save()
        
        # Créer des stagiaires de test
        stagiaires_data = [
            {'nom': '<PERSON>', 'prenom': 'Alice', 'email': '<EMAIL>'},
            {'nom': '<PERSON>', 'prenom': 'Bob', 'email': '<EMAIL>'},
            {'nom': 'Durand', 'prenom': 'Claire', 'email': '<EMAIL>'},
        ]
        
        today = datetime.now().date()
        
        for i, data in enumerate(stagiaires_data):
            stagiaire, created = Stagiaire.objects.get_or_create(
                email=data['email'],
                defaults={
                    'nom': data['nom'],
                    'prenom': data['prenom'],
                    'telephone': f'0123456{i:03d}',
                    'date_naissance': today - timedelta(days=365*22),  # 22 ans
                    'date_debut': today - timedelta(days=30),
                    'date_fin': today + timedelta(days=30),
                    'service': service,
                    'encadrant': encadrant,
                    'statut': 'EN_COURS'
                }
            )
            
            if created:
                self.stdout.write(f'Stagiaire créé: {stagiaire.nom_complet}')
                
                # Créer des tâches pour ce stagiaire
                taches_data = [
                    {
                        'titre': 'Formation outils',
                        'description': 'Se familiariser avec les outils de développement',
                        'priorite': 'HAUTE',
                        'date_fin_prevue': today + timedelta(days=random.randint(1, 15))
                    },
                    {
                        'titre': 'Projet web',
                        'description': 'Développer une application web simple',
                        'priorite': 'MOYENNE',
                        'date_fin_prevue': today + timedelta(days=random.randint(5, 20))
                    },
                    {
                        'titre': 'Documentation',
                        'description': 'Rédiger la documentation technique',
                        'priorite': 'BASSE',
                        'date_fin_prevue': today + timedelta(days=random.randint(10, 25))
                    },
                    {
                        'titre': 'Tests unitaires',
                        'description': 'Écrire des tests pour le projet',
                        'priorite': 'MOYENNE',
                        'date_fin_prevue': today + timedelta(days=random.randint(7, 18))
                    },
                ]
                
                for tache_data in taches_data:
                    Tache.objects.create(
                        stagiaire=stagiaire,
                        creee_par=encadrant,
                        **tache_data
                    )
                
                self.stdout.write(f'  - {len(taches_data)} tâches créées pour {stagiaire.nom_complet}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Données de test créées avec succès!\n'
                f'- Service: {service.nom}\n'
                f'- Encadrant: {encadrant.username} (mot de passe: test123)\n'
                f'- {len(stagiaires_data)} stagiaires avec leurs tâches\n'
                f'Vous pouvez maintenant tester le calendrier à: http://127.0.0.1:8000/calendrier-encadrant/'
            )
        )
