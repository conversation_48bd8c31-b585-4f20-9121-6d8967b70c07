#!/usr/bin/env python
"""
Script pour supprimer les services en double
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import Service
from django.db.models import Count

def supprimer_services_doublons():
    """Supprimer les services en double"""
    
    print("=== SUPPRESSION DES SERVICES EN DOUBLE ===")
    
    # 1. Identifier les doublons par nom
    print(f"\n🔍 Recherche des doublons par nom:")
    
    doublons_nom = Service.objects.values('nom').annotate(
        count=Count('nom')
    ).filter(count__gt=1).order_by('nom')
    
    print(f"   Noms en double trouvés: {doublons_nom.count()}")
    
    services_supprimes_nom = 0
    for doublon in doublons_nom:
        nom = doublon['nom']
        count = doublon['count']
        print(f"\n   📋 Nom '{nom}' trouvé {count} fois:")
        
        # Récupérer tous les services avec ce nom
        services_meme_nom = Service.objects.filter(nom=nom).order_by('date_creation')
        
        # Garder le premier (plus ancien) et supprimer les autres
        premier_service = services_meme_nom.first()
        services_a_supprimer = services_meme_nom.exclude(id=premier_service.id)
        
        print(f"      ✅ Garder: ID {premier_service.id} - {premier_service.nom} (créé le {premier_service.date_creation.strftime('%d/%m/%Y')})")
        
        for service in services_a_supprimer:
            print(f"      ❌ Supprimer: ID {service.id} - {service.nom} (créé le {service.date_creation.strftime('%d/%m/%Y')})")
            
            # Vérifier les dépendances avant suppression
            utilisateurs_lies = service.utilisateurs.count()
            thematiques_liees = service.thematiques.count()
            sujets_lies = service.sujets.count()
            stagiaires_lies = service.stagiaires_service.count()

            if utilisateurs_lies > 0 or thematiques_liees > 0 or sujets_lies > 0 or stagiaires_lies > 0:
                print(f"         ⚠️ Service a des dépendances:")
                print(f"            - Utilisateurs: {utilisateurs_lies}")
                print(f"            - Thématiques: {thematiques_liees}")
                print(f"            - Sujets: {sujets_lies}")
                print(f"            - Stagiaires: {stagiaires_lies}")
                print(f"         🔄 Transfert des dépendances vers le service principal...")

                # Transférer les dépendances vers le premier service
                service.utilisateurs.update(service=premier_service)
                service.thematiques.update(service=premier_service)
                service.sujets.update(service=premier_service)
                service.stagiaires_service.update(service=premier_service)
                
                print(f"         ✅ Dépendances transférées")
            
            service.delete()
            services_supprimes_nom += 1
            print(f"         🗑️ Service supprimé")
    
    # 2. Identifier les doublons par code_service
    print(f"\n🔍 Recherche des doublons par code_service:")
    
    doublons_code = Service.objects.values('code_service').annotate(
        count=Count('code_service')
    ).filter(count__gt=1).order_by('code_service')
    
    print(f"   Codes en double trouvés: {doublons_code.count()}")
    
    services_supprimes_code = 0
    for doublon in doublons_code:
        code = doublon['code_service']
        count = doublon['count']
        print(f"\n   📋 Code '{code}' trouvé {count} fois:")
        
        # Récupérer tous les services avec ce code
        services_meme_code = Service.objects.filter(code_service=code).order_by('date_creation')
        
        # Garder le premier (plus ancien) et supprimer les autres
        premier_service = services_meme_code.first()
        services_a_supprimer = services_meme_code.exclude(id=premier_service.id)
        
        print(f"      ✅ Garder: ID {premier_service.id} - {premier_service.nom} (Code: {premier_service.code_service})")
        
        for service in services_a_supprimer:
            print(f"      ❌ Supprimer: ID {service.id} - {service.nom} (Code: {service.code_service})")
            
            # Vérifier les dépendances avant suppression
            utilisateurs_lies = service.utilisateurs.count()
            thematiques_liees = service.thematiques.count()
            sujets_lies = service.sujets.count()
            stagiaires_lies = service.stagiaires_service.count()

            if utilisateurs_lies > 0 or thematiques_liees > 0 or sujets_lies > 0 or stagiaires_lies > 0:
                print(f"         ⚠️ Service a des dépendances:")
                print(f"            - Utilisateurs: {utilisateurs_lies}")
                print(f"            - Thématiques: {thematiques_liees}")
                print(f"            - Sujets: {sujets_lies}")
                print(f"            - Stagiaires: {stagiaires_lies}")
                print(f"         🔄 Transfert des dépendances vers le service principal...")

                # Transférer les dépendances vers le premier service
                service.utilisateurs.update(service=premier_service)
                service.thematiques.update(service=premier_service)
                service.sujets.update(service=premier_service)
                service.stagiaires_service.update(service=premier_service)
                
                print(f"         ✅ Dépendances transférées")
            
            service.delete()
            services_supprimes_code += 1
            print(f"         🗑️ Service supprimé")
    
    # 3. Identifier les doublons par nom similaire (insensible à la casse)
    print(f"\n🔍 Recherche des doublons par nom similaire (casse):")
    
    tous_services = Service.objects.all().order_by('nom')
    services_supprimes_casse = 0
    services_traites = set()
    
    for service in tous_services:
        if service.id in services_traites:
            continue
        
        # Chercher des services avec le même nom en ignorant la casse
        services_similaires = Service.objects.filter(
            nom__iexact=service.nom
        ).exclude(id=service.id).order_by('date_creation')
        
        if services_similaires.exists():
            print(f"\n   📋 Nom similaire '{service.nom}':")
            print(f"      ✅ Garder: ID {service.id} - {service.nom}")
            
            for service_similaire in services_similaires:
                if service_similaire.id not in services_traites:
                    print(f"      ❌ Supprimer: ID {service_similaire.id} - {service_similaire.nom}")
                    
                    # Transférer les dépendances
                    utilisateurs_lies = service_similaire.utilisateurs.count()
                    thematiques_liees = service_similaire.thematiques.count()
                    sujets_lies = service_similaire.sujets.count()
                    stagiaires_lies = service_similaire.stagiaires_service.count()

                    if utilisateurs_lies > 0 or thematiques_liees > 0 or sujets_lies > 0 or stagiaires_lies > 0:
                        print(f"         🔄 Transfert des dépendances...")
                        service_similaire.utilisateurs.update(service=service)
                        service_similaire.thematiques.update(service=service)
                        service_similaire.sujets.update(service=service)
                        service_similaire.stagiaires_service.update(service=service)
                    
                    service_similaire.delete()
                    services_supprimes_casse += 1
                    services_traites.add(service_similaire.id)
                    print(f"         🗑️ Service supprimé")
        
        services_traites.add(service.id)
    
    # 4. Statistiques finales
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DE LA SUPPRESSION:")
    print("")
    print(f"✅ SERVICES SUPPRIMÉS :")
    print(f"   • Doublons par nom: {services_supprimes_nom}")
    print(f"   • Doublons par code: {services_supprimes_code}")
    print(f"   • Doublons par casse: {services_supprimes_casse}")
    print(f"   • Total supprimé: {services_supprimes_nom + services_supprimes_code + services_supprimes_casse}")
    print("")
    
    # Compter les services restants
    services_restants = Service.objects.count()
    print(f"📈 SERVICES RESTANTS: {services_restants}")
    
    # Vérifier qu'il n'y a plus de doublons
    doublons_nom_restants = Service.objects.values('nom').annotate(
        count=Count('nom')
    ).filter(count__gt=1).count()
    
    doublons_code_restants = Service.objects.values('code_service').annotate(
        count=Count('code_service')
    ).filter(count__gt=1).count()
    
    print(f"🔍 VÉRIFICATION FINALE:")
    print(f"   • Doublons de nom restants: {doublons_nom_restants}")
    print(f"   • Doublons de code restants: {doublons_code_restants}")
    
    if doublons_nom_restants == 0 and doublons_code_restants == 0:
        print(f"   ✅ Aucun doublon restant - Nettoyage réussi !")
    else:
        print(f"   ⚠️ Des doublons persistent - Vérification manuelle requise")
    
    print("")
    print("✅ ACTIONS EFFECTUÉES :")
    print("   • Identification des doublons ✅")
    print("   • Conservation du service le plus ancien ✅")
    print("   • Transfert des dépendances ✅")
    print("   • Suppression sécurisée ✅")
    print("   • Vérification finale ✅")
    print("")
    print("🎉 NETTOYAGE DES SERVICES TERMINÉ !")
    print(f"{'='*60}")

if __name__ == '__main__':
    # Demander confirmation avant suppression
    print("⚠️  ATTENTION: Ce script va supprimer les services en double.")
    print("   Les dépendances seront transférées vers le service le plus ancien.")
    print("   Cette action est irréversible.")
    print("")
    
    confirmation = input("Voulez-vous continuer ? (oui/non): ").lower().strip()
    
    if confirmation in ['oui', 'o', 'yes', 'y']:
        supprimer_services_doublons()
    else:
        print("❌ Opération annulée.")
