#!/usr/bin/env python
"""
Test du filtrage des stagiaires par service/département
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service
from stagiaires.views import filter_stagiaires_by_user_role
from datetime import date, timedelta

User = get_user_model()

def test_filtrage_stagiaires():
    """Test du filtrage des stagiaires selon le service de l'encadrant"""
    
    print("=== Test du filtrage des stagiaires par service/département ===")
    
    # 1. État actuel des données
    print("\n1️⃣ État actuel des données:")
    print("-" * 40)
    
    # Services
    services = Service.objects.all()
    print(f"Services disponibles: {services.count()}")
    for service in services:
        print(f"   • {service.nom} ({service.code_service})")
    
    # Encadrants
    encadrants = User.objects.filter(role='ENCADRANT', is_active=True)
    print(f"\nEncadrants disponibles: {encadrants.count()}")
    for enc in encadrants:
        service_nom = enc.service.nom if enc.service else "Aucun service"
        print(f"   • {enc.get_full_name()} - Service: {service_nom}")
    
    # Stagiaires par département
    stagiaires = Stagiaire.objects.all()
    print(f"\nStagiaires par département: {stagiaires.count()} total")
    
    departements = {}
    for stagiaire in stagiaires:
        dept = stagiaire.get_departement_display()
        if dept not in departements:
            departements[dept] = []
        departements[dept].append(stagiaire)
    
    for dept, stag_list in departements.items():
        print(f"   • {dept}: {len(stag_list)} stagiaires")
        for s in stag_list[:3]:  # Afficher les 3 premiers
            encadrant_nom = s.encadrant.get_full_name() if s.encadrant else "Non assigné"
            print(f"     - {s.nom_complet} (Encadrant: {encadrant_nom})")
        if len(stag_list) > 3:
            print(f"     ... et {len(stag_list) - 3} autres")
    
    # 2. Test du filtrage pour chaque encadrant
    print("\n2️⃣ Test du filtrage pour chaque encadrant:")
    print("-" * 40)
    
    for encadrant in encadrants:
        print(f"\n👨‍🏫 Encadrant: {encadrant.get_full_name()}")
        print(f"   Service: {encadrant.service.nom if encadrant.service else 'Aucun'}")
        
        # Appliquer le filtrage
        stagiaires_filtres = filter_stagiaires_by_user_role(encadrant)
        
        print(f"   Stagiaires visibles: {stagiaires_filtres.count()}")
        
        if stagiaires_filtres.count() > 0:
            print("   Liste des stagiaires:")
            for stagiaire in stagiaires_filtres:
                dept_display = stagiaire.get_departement_display()
                print(f"     • {stagiaire.nom_complet} - {dept_display}")
        else:
            print("   ❌ Aucun stagiaire visible")
    
    # 3. Test avec un admin
    print("\n3️⃣ Test avec un administrateur:")
    print("-" * 40)
    
    admin_user = User.objects.filter(is_superuser=True).first()
    if admin_user:
        print(f"👑 Admin: {admin_user.get_full_name()}")
        stagiaires_admin = filter_stagiaires_by_user_role(admin_user)
        print(f"   Stagiaires visibles: {stagiaires_admin.count()} (tous)")
    else:
        print("❌ Aucun administrateur trouvé")
    
    # 4. Créer un stagiaire de test pour vérifier le filtrage
    print("\n4️⃣ Test avec création d'un stagiaire de test:")
    print("-" * 40)
    
    # Prendre un encadrant avec un service
    encadrant_test = None
    for enc in encadrants:
        if enc.service and enc.service.nom.lower() == 'informatique':
            encadrant_test = enc
            break
    
    if encadrant_test:
        print(f"Encadrant de test: {encadrant_test.get_full_name()}")
        print(f"Service: {encadrant_test.service.nom}")
        
        # Créer un stagiaire dans le département IT
        test_email = f"test.filtrage.{int(date.today().strftime('%Y%m%d'))}@example.com"
        
        # Supprimer le stagiaire de test s'il existe
        Stagiaire.objects.filter(email=test_email).delete()
        
        try:
            stagiaire_test = Stagiaire.objects.create(
                nom='TestFiltrage',
                prenom='Stagiaire',
                email=test_email,
                date_naissance=date(2000, 1, 1),
                departement='IT',  # Département informatique
                date_debut=date.today(),
                date_fin=date.today() + timedelta(days=90),
                etablissement='Université Test Filtrage',
                niveau_etude='Master',
                specialite='Informatique',
                cree_par=admin_user,
                encadrant=encadrant_test,
                service=encadrant_test.service
            )
            
            print(f"✅ Stagiaire de test créé: {stagiaire_test.nom_complet}")
            print(f"   Département: {stagiaire_test.get_departement_display()}")
            
            # Tester le filtrage
            stagiaires_filtres = filter_stagiaires_by_user_role(encadrant_test)
            stagiaire_visible = stagiaires_filtres.filter(id=stagiaire_test.id).exists()
            
            if stagiaire_visible:
                print("✅ Le stagiaire est visible par l'encadrant (filtrage correct)")
            else:
                print("❌ Le stagiaire n'est pas visible par l'encadrant (problème de filtrage)")
            
            # Tester avec un autre encadrant
            autre_encadrant = None
            for enc in encadrants:
                if enc != encadrant_test and enc.service and enc.service.nom.lower() != 'informatique':
                    autre_encadrant = enc
                    break
            
            if autre_encadrant:
                stagiaires_autre = filter_stagiaires_by_user_role(autre_encadrant)
                stagiaire_visible_autre = stagiaires_autre.filter(id=stagiaire_test.id).exists()
                
                if not stagiaire_visible_autre:
                    print(f"✅ Le stagiaire n'est pas visible par {autre_encadrant.get_full_name()} (filtrage correct)")
                else:
                    print(f"❌ Le stagiaire est visible par {autre_encadrant.get_full_name()} (problème de filtrage)")
            
            # Nettoyer
            stagiaire_test.delete()
            print("🧹 Stagiaire de test supprimé")
            
        except Exception as e:
            print(f"❌ Erreur lors de la création du stagiaire de test: {e}")
    else:
        print("⚠️ Aucun encadrant avec service 'informatique' trouvé pour le test")
    
    # 5. Résumé et recommandations
    print("\n5️⃣ Résumé et recommandations:")
    print("-" * 40)
    
    print("✅ Mapping service → département:")
    mapping = {
        'informatique': 'IT',
        'marketing': 'MARKETING', 
        'ressources humaines': 'RH',
        'rh': 'RH',
        'finance': 'FINANCE',
        'commercial': 'COMMERCIAL',
        'production': 'PRODUCTION'
    }
    
    for service_nom, dept in mapping.items():
        print(f"   • Service '{service_nom}' → Département '{dept}'")
    
    print(f"\n💡 Pour tester le filtrage:")
    print(f"   1. Connectez-vous en tant qu'encadrant")
    print(f"   2. Allez dans la liste des stagiaires")
    print(f"   3. Vous ne devriez voir que les stagiaires de votre département")
    print(f"   4. Les admins et RH voient tous les stagiaires")

if __name__ == '__main__':
    test_filtrage_stagiaires()
