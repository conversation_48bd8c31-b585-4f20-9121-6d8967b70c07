{% extends 'stagiaires/base.html' %}

{% block title %}Planifier une mission - {{ stagiaire.nom_complet }} - Gestion des Stagiaires{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-plus-circle me-2"></i>
                            Planifier une mission pour {{ stagiaire.nom_complet }}
                        </h4>
                        <a href="{% url 'missions_stagiaire' stagiaire.id %}" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-1"></i>Retour
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Informations du stagiaire -->
                    <div class="alert alert-info">
                        <div class="row">
                            <div class="col-md-6">
                                <strong><i class="fas fa-user me-1"></i>Stagiaire :</strong> {{ stagiaire.nom_complet }}<br>
                                <strong><i class="fas fa-envelope me-1"></i>Email :</strong> {{ stagiaire.email }}
                            </div>
                            <div class="col-md-6">
                                <strong><i class="fas fa-calendar me-1"></i>Période de stage :</strong><br>
                                {{ stagiaire.date_debut|date:"d/m/Y" }} - {{ stagiaire.date_fin|date:"d/m/Y" }}
                                ({{ stagiaire.duree_stage }} jours)
                            </div>
                        </div>
                    </div>

                    <!-- Formulaire de planification -->
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="{{ form.titre.id_for_label }}" class="form-label">
                                        <i class="fas fa-heading me-1"></i>Titre de la mission *
                                    </label>
                                    {{ form.titre }}
                                    {% if form.titre.errors %}
                                        <div class="text-danger small">{{ form.titre.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">Donnez un titre clair et descriptif à la mission</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">
                                        <i class="fas fa-align-left me-1"></i>Description détaillée *
                                    </label>
                                    {{ form.description }}
                                    {% if form.description.errors %}
                                        <div class="text-danger small">{{ form.description.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">Décrivez en détail ce que le stagiaire devra accomplir</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.objectifs.id_for_label }}" class="form-label">
                                        <i class="fas fa-bullseye me-1"></i>Objectifs à atteindre *
                                    </label>
                                    {{ form.objectifs }}
                                    {% if form.objectifs.errors %}
                                        <div class="text-danger small">{{ form.objectifs.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">Listez les objectifs pédagogiques et professionnels</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.livrables_attendus.id_for_label }}" class="form-label">
                                        <i class="fas fa-clipboard-check me-1"></i>Livrables attendus *
                                    </label>
                                    {{ form.livrables_attendus }}
                                    {% if form.livrables_attendus.errors %}
                                        <div class="text-danger small">{{ form.livrables_attendus.errors.0 }}</div>
                                    {% endif %}
                                    <div class="form-text">Précisez les documents ou résultats attendus</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.date_debut_prevue.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-plus me-1"></i>Date de début prévue *
                                    </label>
                                    {{ form.date_debut_prevue }}
                                    {% if form.date_debut_prevue.errors %}
                                        <div class="text-danger small">{{ form.date_debut_prevue.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.date_fin_prevue.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-minus me-1"></i>Date de fin prévue *
                                    </label>
                                    {{ form.date_fin_prevue }}
                                    {% if form.date_fin_prevue.errors %}
                                        <div class="text-danger small">{{ form.date_fin_prevue.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.priorite.id_for_label }}" class="form-label">
                                        <i class="fas fa-exclamation-circle me-1"></i>Priorité *
                                    </label>
                                    {{ form.priorite }}
                                    {% if form.priorite.errors %}
                                        <div class="text-danger small">{{ form.priorite.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Affichage des erreurs générales -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Conseils pour la planification -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h6 class="card-title text-primary">
                                    <i class="fas fa-lightbulb me-1"></i>Conseils pour une bonne planification
                                </h6>
                                <ul class="mb-0 small">
                                    <li>Définissez des objectifs SMART (Spécifiques, Mesurables, Atteignables, Réalistes, Temporels)</li>
                                    <li>Prévoyez des jalons intermédiaires pour faciliter le suivi</li>
                                    <li>Adaptez la complexité au niveau et à l'expérience du stagiaire</li>
                                    <li>Assurez-vous que les livrables sont clairement définis</li>
                                    <li>Laissez une marge de manœuvre dans les délais</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'missions_stagiaire' stagiaire.id %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Planifier la mission
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Calcul automatique de la durée
document.addEventListener('DOMContentLoaded', function() {
    const dateDebut = document.getElementById('{{ form.date_debut_prevue.id_for_label }}');
    const dateFin = document.getElementById('{{ form.date_fin_prevue.id_for_label }}');
    
    function calculateDuration() {
        if (dateDebut.value && dateFin.value) {
            const debut = new Date(dateDebut.value);
            const fin = new Date(dateFin.value);
            const diffTime = Math.abs(fin - debut);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
            
            // Afficher la durée calculée
            let durationInfo = document.getElementById('duration-info');
            if (!durationInfo) {
                durationInfo = document.createElement('div');
                durationInfo.id = 'duration-info';
                durationInfo.className = 'form-text text-info';
                dateFin.parentNode.appendChild(durationInfo);
            }
            
            if (fin >= debut) {
                durationInfo.innerHTML = `<i class="fas fa-clock me-1"></i>Durée prévue : ${diffDays} jour(s)`;
                durationInfo.className = 'form-text text-info';
            } else {
                durationInfo.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>La date de fin doit être postérieure à la date de début`;
                durationInfo.className = 'form-text text-danger';
            }
        }
    }
    
    dateDebut.addEventListener('change', calculateDuration);
    dateFin.addEventListener('change', calculateDuration);
    
    // Calcul initial si les dates sont déjà remplies
    calculateDuration();
});

// Validation côté client
document.querySelector('form').addEventListener('submit', function(e) {
    const dateDebut = document.getElementById('{{ form.date_debut_prevue.id_for_label }}').value;
    const dateFin = document.getElementById('{{ form.date_fin_prevue.id_for_label }}').value;
    
    if (dateDebut && dateFin && new Date(dateFin) <= new Date(dateDebut)) {
        e.preventDefault();
        alert('La date de fin doit être postérieure à la date de début.');
        return false;
    }
});
</script>
{% endblock %}
