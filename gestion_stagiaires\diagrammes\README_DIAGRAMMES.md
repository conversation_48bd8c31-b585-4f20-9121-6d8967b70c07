# 📊 DIAGRAMMES UML - SYSTÈME DE GESTION DES STAGIAIRES

## 📋 Vue d'Ensemble

Ce dossier contient tous les diagrammes UML du système de gestion des stagiaires, créés avec PlantUML pour documenter l'architecture, les processus et les interactions du système.

---

## 📁 Fichiers Disponibles

### 1. **diagramme_classes.puml**
**Type :** Diagramme de Classes  
**Description :** Architecture complète du système avec tous les modèles de données, leurs attributs, méthodes et relations.

**Contenu :**
- 🏗️ **12 classes principales** (CustomUser, Stagiaire, Service, Tache, etc.)
- 🔗 **Relations complètes** (OneToMany, ManyToOne, ManyToMany)
- 📦 **Packages organisés** par domaine fonctionnel
- 🎯 **Énumérations** pour les choix (statuts, priorités, rôles)

### 2. **diagramme_cas_utilisation.puml**
**Type :** Diagramme de Cas d'Utilisation  
**Description :** Fonctionnalités du système organisées par acteur avec leurs permissions respectives.

**Contenu :**
- 👥 **3 acteurs principaux** (Administrateur, RH, Encadrant)
- 📋 **64+ cas d'utilisation** organisés en packages
- 🔐 **Permissions différenciées** par rôle
- 🔗 **Relations d'inclusion/extension** entre cas d'usage

### 3. **diagramme_sequence_rencontre.puml**
**Type :** Diagramme de Séquence  
**Description :** Processus détaillé d'une rencontre encadrant-stagiaire avec consultation CV et gestion des tâches.

**Contenu :**
- 🤝 **Processus complet** de rencontre
- 📄 **Consultation CV** intégrée
- ✅ **Gestion des tâches** en temps réel
- 📧 **Envoi d'email** automatique
- 🛡️ **Gestion des erreurs** et permissions

### 4. **diagramme_activite_gestion_stagiaire.puml**
**Type :** Diagramme d'Activité  
**Description :** Processus métier complet de la gestion d'un stagiaire du début à la fin.

**Contenu :**
- 🔄 **Workflow complet** (candidature → archivage)
- 👥 **Acteurs multiples** (RH, Encadrant, Système)
- 🎯 **Points de décision** et conditions
- 📊 **Calcul automatique** des statuts de période

### 5. **diagramme_etat_stagiaire.puml**
**Type :** Diagramme d'États  
**Description :** Cycle de vie complet d'un stagiaire avec tous les états possibles et leurs transitions.

**Contenu :**
- 🔄 **8 états principaux** (Candidature → Archive)
- 🎯 **Sous-états** pour le suivi détaillé
- ➡️ **Transitions** avec conditions
- 🎨 **Indicateurs visuels** (codes couleur)

---

## 🛠️ Utilisation des Diagrammes

### Génération des Images

#### Option 1 : PlantUML Online
1. Aller sur [PlantUML Online Server](http://www.plantuml.com/plantuml/uml/)
2. Copier le contenu d'un fichier `.puml`
3. Cliquer sur "Submit" pour générer l'image
4. Télécharger en PNG, SVG ou PDF

#### Option 2 : Extension VS Code
1. Installer l'extension "PlantUML" dans VS Code
2. Ouvrir un fichier `.puml`
3. Utiliser `Ctrl+Shift+P` → "PlantUML: Preview Current Diagram"
4. Exporter avec `Ctrl+Shift+P` → "PlantUML: Export Current Diagram"

#### Option 3 : Ligne de Commande
```bash
# Installation Java et PlantUML
java -jar plantuml.jar diagramme_classes.puml

# Génération en PNG
java -jar plantuml.jar -tpng *.puml

# Génération en SVG
java -jar plantuml.jar -tsvg *.puml
```

### Formats de Sortie Recommandés
- **PNG** : Pour documentation et présentations
- **SVG** : Pour intégration web (vectoriel)
- **PDF** : Pour documents officiels
- **ASCII** : Pour documentation texte

---

## 📖 Guide de Lecture

### Diagramme de Classes
```
📦 Package : Domaine fonctionnel
├── 🏛️ Classe : Entité métier
│   ├── +attribut : Type (public)
│   ├── -attribut : Type (privé)
│   └── +méthode() : Type
└── 🔗 Relations :
    ├── ──── : Association
    ├── ──▷ : Héritage
    ├── ◆──── : Composition
    └── ◇──── : Agrégation
```

### Diagramme de Cas d'Utilisation
```
👤 Acteur : Utilisateur du système
📋 Cas d'usage : Fonctionnalité
🔗 Relations :
├── ──── : Association
├── <<include>> : Inclusion obligatoire
└── <<extend>> : Extension conditionnelle
```

### Diagramme de Séquence
```
👤 Acteur : Initiateur
📱 Participant : Composant système
📨 Message : Interaction
├── ──→ : Appel synchrone
├── ──→ : Retour
└── ⚡ : Message asynchrone
```

---

## 🎯 Utilisation par Rôle

### 👨‍💼 Chef de Projet
- **Diagramme de cas d'utilisation** : Vue fonctionnelle complète
- **Diagramme d'activité** : Processus métier
- **Diagramme d'états** : Cycle de vie des données

### 👨‍💻 Développeur
- **Diagramme de classes** : Architecture technique
- **Diagramme de séquence** : Implémentation détaillée
- **Tous les diagrammes** : Compréhension globale

### 👨‍🏫 Formateur/Utilisateur
- **Diagramme de cas d'utilisation** : Fonctionnalités disponibles
- **Diagramme d'activité** : Processus à suivre
- **Diagramme d'états** : États possibles

### 🏛️ Direction/Client
- **Diagramme de cas d'utilisation** : Vue fonctionnelle
- **Diagramme d'activité** : Processus métier
- **Synthèse visuelle** des capacités

---

## 🔧 Personnalisation

### Modification des Diagrammes
1. **Éditer le fichier .puml** avec un éditeur de texte
2. **Respecter la syntaxe** PlantUML
3. **Tester la génération** avant finalisation
4. **Documenter les changements** dans les commentaires

### Ajout de Nouveaux Diagrammes
```plantuml
@startuml Nouveau_Diagramme
!theme plain
title Mon Nouveau Diagramme

' Contenu du diagramme ici

@enduml
```

### Thèmes Disponibles
- `!theme plain` : Thème simple et clair
- `!theme bluegray` : Thème bleu-gris moderne
- `!theme sketchy` : Style dessiné à la main
- `!theme spacelab` : Thème coloré

---

## 📊 Métriques des Diagrammes

| Diagramme | Classes/Éléments | Relations | Complexité |
|-----------|------------------|-----------|------------|
| **Classes** | 12 classes | 25+ relations | Élevée |
| **Cas d'usage** | 64+ cas | 3 acteurs | Moyenne |
| **Séquence** | 8 participants | 30+ messages | Élevée |
| **Activité** | 20+ activités | 3 couloirs | Moyenne |
| **États** | 8 états | 15+ transitions | Moyenne |

---

## 🔄 Maintenance

### Mise à Jour
- **Synchroniser** avec les évolutions du code
- **Valider** la cohérence entre diagrammes
- **Tester** la génération après modifications
- **Archiver** les versions précédentes

### Versioning
```
v1.0 - Version initiale (juillet 2025)
v1.1 - Ajout diagramme de déploiement
v1.2 - Mise à jour suite aux évolutions
```

---

## 📞 Support

### Ressources PlantUML
- **Site officiel** : [plantuml.com](https://plantuml.com/)
- **Guide de référence** : [plantuml.com/guide](https://plantuml.com/guide)
- **Exemples** : [real-world-plantuml.com](https://real-world-plantuml.com/)

### Outils Recommandés
- **VS Code** + Extension PlantUML
- **IntelliJ IDEA** + Plugin PlantUML
- **Eclipse** + Plugin PlantUML
- **Serveur en ligne** PlantUML

---

**📋 Ces diagrammes constituent la documentation technique de référence du système de gestion des stagiaires MEF.**

*Dernière mise à jour : 17 juillet 2025*
