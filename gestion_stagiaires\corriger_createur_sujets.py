#!/usr/bin/env python
"""
Script pour corriger les créateurs des sujets selon leur service
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Sujet

User = get_user_model()

def corriger_createur_sujets():
    """Corriger les créateurs des sujets selon leur service"""
    
    print("=== CORRECTION CRÉATEURS DES SUJETS ===")
    
    # Récupérer tous les sujets
    sujets = Sujet.objects.all()
    print(f"📝 Total sujets: {sujets.count()}")
    
    corrections = 0
    
    for sujet in sujets:
        print(f"\n📝 Sujet: {sujet.titre}")
        print(f"   Service: {sujet.service.nom if sujet.service else 'Aucun'}")
        print(f"   Créé par: {sujet.cree_par.get_full_name() if hasattr(sujet, 'cree_par') and sujet.cree_par else 'Aucun'}")
        
        if hasattr(sujet, 'cree_par') and sujet.cree_par and sujet.service:
            # Vérifier si le créateur est du même service que le sujet
            if sujet.cree_par.service != sujet.service:
                print(f"   ⚠️ Incohérence: Créateur du service {sujet.cree_par.service.nom if sujet.cree_par.service else 'Aucun'}, sujet du service {sujet.service.nom}")
                
                # Trouver un encadrant du bon service
                encadrant_service = User.objects.filter(
                    role='ENCADRANT',
                    service=sujet.service,
                    is_active=True
                ).first()
                
                if encadrant_service:
                    print(f"   🔧 Correction: {sujet.cree_par.get_full_name()} → {encadrant_service.get_full_name()}")
                    sujet.cree_par = encadrant_service
                    sujet.save()
                    corrections += 1
                else:
                    print(f"   ❌ Aucun encadrant trouvé pour le service {sujet.service.nom}")
            else:
                print(f"   ✅ Cohérent: Créateur et sujet du même service")
        elif not hasattr(sujet, 'cree_par') or not sujet.cree_par:
            # Assigner un créateur par défaut selon le service
            if sujet.service:
                encadrant_service = User.objects.filter(
                    role='ENCADRANT',
                    service=sujet.service,
                    is_active=True
                ).first()
                
                if encadrant_service:
                    print(f"   🔧 Assignation créateur: {encadrant_service.get_full_name()}")
                    sujet.cree_par = encadrant_service
                    sujet.save()
                    corrections += 1
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"   Corrections effectuées: {corrections}")
    
    # Vérification finale
    print(f"\n🔍 VÉRIFICATION FINALE:")
    
    sujets_incoherents = 0
    
    for sujet in Sujet.objects.all():
        if hasattr(sujet, 'cree_par') and sujet.cree_par and sujet.service:
            if sujet.cree_par.service != sujet.service:
                sujets_incoherents += 1
                print(f"   ⚠️ {sujet.titre}: Créateur {sujet.cree_par.service.nom if sujet.cree_par.service else 'Aucun'} ≠ Sujet {sujet.service.nom}")
    
    if sujets_incoherents == 0:
        print(f"   ✅ Tous les sujets sont cohérents")
    else:
        print(f"   ❌ {sujets_incoherents} sujets incohérents restants")
    
    print(f"\n🎉 CORRECTION TERMINÉE !")

if __name__ == '__main__':
    corriger_createur_sujets()
