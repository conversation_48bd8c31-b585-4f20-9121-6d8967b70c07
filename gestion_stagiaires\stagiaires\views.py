from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout
from django.contrib.auth.views import LoginView
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import CreateView
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, JsonResponse
from django.utils import timezone
from django.template.loader import render_to_string
from django.views.decorators.http import require_POST
from django.db import models
from django.db.models import Q
from .forms import (CustomUserCreationForm, CustomAuthenticationForm, StagiaireForm,
                   ConventionUploadForm, ConventionValidationForm, TacheStageForm,
                   EvaluationStageForm, AttestationGenerationForm, MissionForm,
                   SuiviAvancementForm, RapportStageForm, ValidationRapportForm,
                   ContratStageForm, ServiceForm, ThematiqueForm, SujetForm,
                   StagiaireFormEncadrant, DureeEstimeeForm, TacheForm, CustomUserForm,
                   AdminUserCreationForm)
from .models import CustomUser, Stagiaire, TacheStage, Mission, RapportStage, ContratStage, Service, Thematique, Sujet, DureeEstimee, Tache
import os
import json
from datetime import date, datetime, timedelta
import csv
from django.core.mail import send_mail
from django.conf import settings
# Import des fonctions d'exportation
from . import export_utils


def filter_stagiaires_by_user_role(user, queryset=None):
    """
    Fonction utilitaire pour filtrer les stagiaires selon le rôle de l'utilisateur
    Filtre par département du stagiaire correspondant au service de l'encadrant
    """
    if queryset is None:
        queryset = Stagiaire.objects.all()

    # Si l'utilisateur est un encadrant, filtrer selon son service
    if hasattr(user, 'role') and user.role == 'ENCADRANT':
        if hasattr(user, 'service') and user.service:
            # Mapping entre les services et les départements
            service_to_departement_mapping = {
                'informatique': 'IT',
                'marketing': 'MARKETING',
                'ressources humaines': 'RH',
                'rh': 'RH',
                'finance': 'FINANCE',
                'commercial': 'COMMERCIAL',
                'production': 'PRODUCTION'
            }

            # Récupérer le nom du service en minuscules
            service_nom = user.service.nom.lower()
            departement_correspondant = service_to_departement_mapping.get(service_nom, None)

            if departement_correspondant:
                # Filtrer par département correspondant au service
                return queryset.filter(departement=departement_correspondant)
            else:
                # Si pas de correspondance, montrer les stagiaires assignés à cet encadrant
                return queryset.filter(encadrant=user)
        else:
            # Si l'encadrant n'a pas de service, montrer seulement ses stagiaires assignés
            return queryset.filter(encadrant=user)
    else:
        # Les admins et RH voient tous les stagiaires
        return queryset


class CustomLoginView(LoginView):
    """Vue de connexion personnalisée"""
    form_class = CustomAuthenticationForm
    template_name = 'stagiaires/login.html'
    redirect_authenticated_user = True

    def get_success_url(self):
        return reverse_lazy('dashboard')

    def form_valid(self, form):
        messages.success(self.request, f'Bienvenue {form.get_user().first_name}!')
        return super().form_valid(form)


class RegisterView(CreateView):
    """Vue d'inscription personnalisée"""
    model = CustomUser
    form_class = CustomUserCreationForm
    template_name = 'stagiaires/register.html'
    success_url = reverse_lazy('login')

    def form_valid(self, form):
        # Enregistrer qui a créé l'utilisateur si quelqu'un est connecté
        user = form.save(commit=False)
        if self.request.user.is_authenticated:
            user.cree_par = self.request.user
        user.save()

        messages.success(
            self.request,
            'Le compte a été créé avec succès!'
        )

        # Si c'est un admin qui crée l'utilisateur, rediriger vers la gestion des utilisateurs
        if self.request.user.is_authenticated and self.request.user.is_superuser:
            return redirect('user_management')

        return redirect(self.success_url)

    def dispatch(self, request, *args, **kwargs):
        # Permettre aux admins d'accéder à cette vue pour créer des utilisateurs
        if request.user.is_authenticated and request.user.is_superuser:
            return super(CreateView, self).dispatch(request, *args, **kwargs)
        elif request.user.is_authenticated:
            return redirect('dashboard')
        return super().dispatch(request, *args, **kwargs)


def dashboard_view(request):
    """Vue du tableau de bord après connexion"""
    if not request.user.is_authenticated:
        return redirect('login')

    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur'
    }
    return render(request, 'stagiaires/dashboard.html', context)


def logout_view(request):
    """Vue de déconnexion"""
    logout(request)
    messages.info(request, 'Vous avez été déconnecté avec succès.')
    return redirect('login')


def home_view(request):
    """Vue de la page d'accueil avant authentification"""
    # Si l'utilisateur est déjà connecté, rediriger vers le tableau de bord
    if request.user.is_authenticated:
        return redirect('dashboard')
    
    return render(request, 'stagiaires/home.html')


@login_required
def stagiaires_list_view(request):
    """Vue pour afficher la liste des stagiaires avec filtres pour les encadrants"""

    # Récupérer tous les stagiaires
    stagiaires_queryset = Stagiaire.objects.select_related('encadrant', 'cree_par', 'service')

    # Paramètre de filtre pour les encadrants
    filtre = request.GET.get('filtre', 'tous')  # 'tous' ou 'mon_service'

    # Pour les encadrants, préparer les deux vues
    if hasattr(request.user, 'role') and request.user.role == 'ENCADRANT':
        # Tous les stagiaires (vue complète)
        tous_stagiaires = stagiaires_queryset.all()

        # Stagiaires de mon service uniquement
        mes_stagiaires = filter_stagiaires_by_user_role(request.user, stagiaires_queryset)

        # Déterminer quels stagiaires afficher selon le filtre
        if filtre == 'mon_service':
            stagiaires_affiches = mes_stagiaires
        else:
            # filtre == 'tous' : afficher TOUS les stagiaires
            stagiaires_affiches = tous_stagiaires

        # Statistiques pour l'encadrant
        stats = {
            'total_stagiaires': tous_stagiaires.count(),
            'mes_stagiaires': mes_stagiaires.count(),
            'service_nom': request.user.service.nom if hasattr(request.user, 'service') and request.user.service else 'Non défini'
        }

    else:
        # Pour les admins et RH, utiliser le filtrage normal
        stagiaires_affiches = filter_stagiaires_by_user_role(request.user, stagiaires_queryset)
        stats = {
            'total_stagiaires': stagiaires_affiches.count(),
            'mes_stagiaires': 0,
            'service_nom': 'Tous services'
        }

    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'stagiaires': stagiaires_affiches,
        'filtre_actuel': filtre,
        'stats': stats,
        'is_encadrant': hasattr(request.user, 'role') and request.user.role == 'ENCADRANT'
    }
    return render(request, 'stagiaires/stagiaires_list.html', context)


@login_required
def add_thematique_view(request):
    """Vue pour ajouter une thématique de stage"""
    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH', 'ENCADRANT']:
        messages.error(request, "Vous n'avez pas les droits pour ajouter une thématique.")
        return redirect('thematiques_list')
   

    
    if request.method == 'POST':
        form = ThematiqueForm(request.POST, user=request.user)
        if form.is_valid():
            thematique = form.save(commit=False)
            thematique.cree_par = request.user

            # Pour les encadrants, assigner automatiquement leur service
            if request.user.role == 'ENCADRANT' and hasattr(request.user, 'service') and request.user.service:
                thematique.service = request.user.service

            thematique.save()
            messages.success(request, f'La thématique "{thematique.titre}" a été ajoutée avec succès.')
            return redirect('thematiques_list')
    else:
        form = ThematiqueForm(user=request.user)
    
    context = {
        'form': form,
        'title': 'Ajouter une thématique'
    }
    return render(request, 'stagiaires/add_thematique.html', context)








# Vue sujets_list_view supprimée - utiliser celle de la ligne 1085


@login_required
def thematiques_list_view(request):
    """Vue pour afficher les thématiques selon le rôle de l'utilisateur"""
    if request.user.role == 'ENCADRANT':
        # Les encadrants voient seulement les thématiques de leur service
        if hasattr(request.user, 'service') and request.user.service:
            thematiques = Thematique.objects.filter(
                Q(service=request.user.service) | Q(service__isnull=True),
                active=True
            ).distinct()
        else:
            thematiques = Thematique.objects.filter(active=True)
    else:
        # Les admins et RH voient toutes les thématiques
        thematiques = Thematique.objects.filter(active=True)

    context = {
        'thematiques': thematiques,
        'title': 'Gestion des thématiques',
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur'
    }
    return render(request, 'stagiaires/thematiques_list.html', context)


@login_required
def service_content_view(request):
    """Vue pour afficher les thématiques et sujets du service de l'encadrant"""
    if not hasattr(request.user, 'role') or request.user.role != 'ENCADRANT':
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    if not hasattr(request.user, 'service') or not request.user.service:
        messages.error(request, 'Aucun service assigné. Contactez l\'administrateur.')
        return redirect('dashboard')

    service = request.user.service

    # Récupérer les thématiques du service
    thematiques = Thematique.objects.filter(
        Q(service=service) | Q(service__isnull=True),
        active=True
    ).distinct()

    # Récupérer les sujets du service
    sujets = Sujet.objects.filter(
        Q(service=service) | Q(encadrant=request.user),
        actif=True
    ).distinct()

    context = {
        'service': service,
        'thematiques': thematiques,
        'sujets': sujets,
        'user': request.user,
        'role_display': request.user.get_role_display(),
        'title': f'Contenu du service {service.nom}'
    }
    return render(request, 'stagiaires/service_content.html', context)


@login_required
def get_sujets_by_thematique(request, thematique_id):
    """API pour récupérer les sujets d'une thématique donnée"""
    # Vérifier que la thématique existe
    thematique = get_object_or_404(Thematique, id=thematique_id)
    
    # Récupérer les sujets de cette thématique
    sujets = Sujet.objects.filter(thematique=thematique, actif=True)
    
    # Préparer les données pour la réponse JSON
    sujets_data = [
        {
            'id': sujet.id,
            'titre': sujet.titre,
            'description': sujet.description,
            'encadrant': sujet.encadrant.get_full_name() if sujet.encadrant else 'Non assigné'
        }
        for sujet in sujets
    ]
    
    return JsonResponse(sujets_data, safe=False)


@login_required
def durees_estimees_view(request):
    """Vue pour afficher et gérer les durées estimées des stages"""
    # Vérifier les permissions (RH, ADMIN ou ENCADRANT)
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN', 'ENCADRANT']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    if request.method == 'POST':
        form = DureeEstimeeForm(request.POST)
        if form.is_valid():
            duree = form.cleaned_data['duree']
            commentaire = form.cleaned_data['commentaire']
            
            # Enregistrer la durée estimée dans la base de données
            DureeEstimee.objects.create(
                duree=duree,
                commentaire=commentaire,
                cree_par=request.user
            )
            
            messages.success(request, f'Durée estimée de {duree} jours enregistrée avec succès!')
            return redirect('durees_estimees')
    else:
        form = DureeEstimeeForm()
    
    # Récupérer les durées estimées existantes
    durees_estimees = DureeEstimee.objects.all().select_related('cree_par')
    
    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'form': form,
        'durees_estimees': durees_estimees,
        'title': 'Durées Estimées des Stages'
    }
    return render(request, 'stagiaires/durees_estimees.html', context)


# Utiliser les fonctions d'exportation depuis export_utils.py
# Les fonctions export_durees_csv et export_durees_excel ont été déplacées dans export_utils.py
@login_required
def add_stagiaire_view(request):
    """Vue pour ajouter un stagiaire"""
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN', 'ENCADRANT']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    # Choisir le formulaire en fonction du rôle de l'utilisateur
    form_class = StagiaireForm
    if request.user.role == 'ENCADRANT':
        form_class = StagiaireFormEncadrant

    if request.method == 'POST':
        # Utiliser le bon formulaire selon le rôle
        if request.user.role == 'ENCADRANT':
            form = form_class(request.POST, request.FILES, user=request.user)
        else:
            form = form_class(request.POST, request.FILES, user_role=request.user.role, user=request.user)

        if form.is_valid():
            try:
                # Pour les encadrants, utiliser la méthode save personnalisée
                if request.user.role == 'ENCADRANT':
                    stagiaire = form.save(commit=True, user=request.user)
                else:
                    stagiaire = form.save(commit=False)
                    stagiaire.cree_par = request.user

                    # Récupérer les champs thématique et sujet si présents
                    if 'thematique' in form.cleaned_data and form.cleaned_data['thematique']:
                        stagiaire.thematique = form.cleaned_data['thematique']
                    if 'sujet' in form.cleaned_data and form.cleaned_data['sujet']:
                        stagiaire.sujet = form.cleaned_data['sujet']

                    stagiaire.save()

                # Messages de succès
                service_info = f" (Service: {stagiaire.service.nom})" if stagiaire.service else ""
                messages.success(request, f'✅ SUCCÈS : Le stagiaire {stagiaire.prenom} {stagiaire.nom} a été ajouté avec succès{service_info} ! (ID: {stagiaire.id})')
                messages.info(request, f'📊 Total de stagiaires en base : {Stagiaire.objects.count()}')

                # Log pour debug
                print(f"DEBUG: Stagiaire créé avec succès - ID: {stagiaire.id}, Nom: {stagiaire.nom_complet}, Service: {stagiaire.service}")

                return redirect('stagiaires_list')

            except Exception as e:
                messages.error(request, f'❌ Erreur lors de la création du stagiaire : {str(e)}')
                print(f"DEBUG: Erreur lors de la création - {str(e)}")
        else:
            # Afficher les erreurs du formulaire
            messages.error(request, '❌ Veuillez corriger les erreurs dans le formulaire.')
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
            print(f"DEBUG: Erreurs du formulaire - {form.errors}")
    else:
        # Initialiser le formulaire
        if request.user.role == 'ENCADRANT':
            form = form_class(user=request.user)
        else:
            form = form_class(user_role=request.user.role, user=request.user)
    
    context = {
        'form': form,
        'title': 'Ajouter un stagiaire',
        'is_rh': request.user.role == 'RH'
    }
    return render(request, 'stagiaires/add_stagiaire.html', context)


@login_required
def stagiaire_detail_view(request, stagiaire_id):
    """Vue pour afficher les détails d'un stagiaire"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)
    
    # Vérifier les permissions (seuls les RH, ADMIN et l'encadrant du stagiaire peuvent voir les détails)
    if not hasattr(request.user, 'role') or (
        request.user.role not in ['RH', 'ADMIN'] and 
        request.user != stagiaire.encadrant and 
        request.user != stagiaire.cree_par
    ):
        messages.error(request, 'Accès non autorisé.')
        return redirect('stagiaires_list')
    
    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'stagiaire': stagiaire,
        'title': f'Détails du stagiaire {stagiaire.prenom} {stagiaire.nom}'
    }
    return render(request, 'stagiaires/stagiaire_detail.html', context)


@login_required
def edit_stagiaire_view(request, stagiaire_id):
    """Vue pour modifier un stagiaire"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)
    
    # Vérifier les permissions
    # RH et ADMIN peuvent modifier tous les stagiaires
    # ENCADRANT peut modifier les stagiaires de son service
    can_edit = False

    if hasattr(request.user, 'role'):
        if request.user.role in ['RH', 'ADMIN']:
            can_edit = True
        elif request.user.role == 'ENCADRANT':
            # Vérifier si le stagiaire appartient au service de l'encadrant
            from stagiaires.views import filter_stagiaires_by_user_role
            stagiaires_autorises = filter_stagiaires_by_user_role(request.user, Stagiaire.objects.all())
            can_edit = stagiaires_autorises.filter(id=stagiaire.id).exists()

    if not can_edit:
        messages.error(request, 'Vous n\'êtes pas autorisé à modifier ce stagiaire.')
        return redirect('stagiaires_list')
    
    # Utiliser le formulaire d'édition avec upload de rapport
    from .forms import StagiaireEditForm
    form_class = StagiaireEditForm
    
    if request.method == 'POST':
        form = form_class(request.POST, request.FILES, instance=stagiaire, user=request.user)
        if form.is_valid():
            # Sauvegarder le formulaire normalement
            stagiaire = form.save(commit=False)

            # Gérer spécifiquement l'upload de rapport
            if 'rapport_stage' in request.FILES:
                stagiaire.date_upload_rapport = timezone.now()
                stagiaire.rapport_uploade_par = request.user
                messages.success(request, f'Le rapport de stage a été uploadé avec succès.')

            # Sauvegarder toutes les modifications
            stagiaire.save()
            messages.success(request, f'Les informations du stagiaire {stagiaire.prenom} {stagiaire.nom} ont été mises à jour.')
            return redirect('stagiaire_detail', stagiaire_id=stagiaire.id)
        else:
            # Afficher les erreurs du formulaire
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'Erreur dans {field}: {error}')
    else:
        form = form_class(instance=stagiaire, user=request.user)
    
    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'form': form,
        'stagiaire': stagiaire,
        'title': f'Modifier le stagiaire {stagiaire.prenom} {stagiaire.nom}'
    }
    return render(request, 'stagiaires/edit_stagiaire.html', context)


@login_required
def user_management_view(request):
    """Vue pour la gestion des utilisateurs (admin uniquement)"""
    # Vérifier les permissions
    if not request.user.is_superuser:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    # Récupérer tous les utilisateurs
    users = CustomUser.objects.all().order_by('role', 'last_name', 'first_name')
    
    context = {
        'user': request.user,
        'role_display': request.user.get_role_display(),
        'users': users,
        'title': 'Gestion des utilisateurs'
    }
    return render(request, 'stagiaires/user_management.html', context)


@login_required
def edit_user_view(request, user_id):
    """Vue pour modifier un utilisateur (admin uniquement)"""
    # Vérifier les permissions
    if not request.user.is_superuser:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    # Récupérer l'utilisateur à modifier
    user_to_edit = get_object_or_404(CustomUser, id=user_id)

    if request.method == 'POST':
        form = CustomUserForm(request.POST, instance=user_to_edit)
        if form.is_valid():
            form.save()
            messages.success(request, f'L\'utilisateur {user_to_edit.get_full_name()} a été modifié avec succès.')
            return redirect('user_management')
    else:
        form = CustomUserForm(instance=user_to_edit)

    context = {
        'user': request.user,
        'role_display': request.user.get_role_display(),
        'form': form,
        'user_to_edit': user_to_edit,
        'title': f'Modifier l\'utilisateur {user_to_edit.get_full_name()}'
    }
    return render(request, 'stagiaires/edit_user.html', context)


@login_required
def add_user_admin_view(request):
    """Vue pour ajouter un utilisateur (admin uniquement)"""
    # Vérifier les permissions
    if not request.user.is_superuser:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    if request.method == 'POST':
        form = AdminUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, f'L\'utilisateur {user.get_full_name()} a été créé avec succès.')
            return redirect('user_management')
    else:
        form = AdminUserCreationForm()

    context = {
        'user': request.user,
        'role_display': request.user.get_role_display(),
        'form': form,
        'title': 'Ajouter un utilisateur'
    }
    return render(request, 'stagiaires/add_user_admin.html', context)


@login_required
def reports_view(request):
    """Vue pour afficher les rapports et statistiques"""
    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    # Récupérer les statistiques de base
    total_stagiaires = Stagiaire.objects.count()
    stagiaires_actifs = Stagiaire.objects.filter(statut='EN_COURS').count()
    stagiaires_termines = Stagiaire.objects.filter(statut='TERMINE').count()
    
    # Statistiques par département
    stats_departement = Stagiaire.objects.values('departement').annotate(
        total=models.Count('id')
    ).order_by('departement')
    
    # Statistiques par encadrant
    stats_encadrant = Stagiaire.objects.values(
        'encadrant__first_name', 'encadrant__last_name'
    ).annotate(
        total=models.Count('id')
    ).order_by('-total')
    
    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'total_stagiaires': total_stagiaires,
        'stagiaires_actifs': stagiaires_actifs,
        'stagiaires_termines': stagiaires_termines,
        'stats_departement': stats_departement,
        'stats_encadrant': stats_encadrant,
        'title': 'Rapports et Statistiques'
    }
    return render(request, 'stagiaires/reports.html', context)


@login_required
def export_view(request):
    """Vue pour la page d'export des données"""
    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'title': 'Export des Données'
    }
    return render(request, 'stagiaires/export.html', context)

#convention 
def convention_upload(request, stagiaire_id):
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)
    if request.method == 'POST':
        convention = request.FILES.get('convention')
        if convention:
            stagiaire.convention_stage = convention
            stagiaire.save()
            messages.success(request, "Convention téléversée avec succès.")
            return redirect('stagiaire_detail', stagiaire_id=stagiaire.id)
        else:
            messages.error(request, "Veuillez sélectionner un fichier à téléverser.")
    return render(request, 'stagiaires/convention_upload.html', {'stagiaire': stagiaire})
@login_required
def conventions_list_view(request):
    """Vue pour afficher la liste des conventions de stage"""
    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    # Récupérer toutes les conventions (stagiaires avec convention)
    stagiaires_avec_convention = Stagiaire.objects.exclude(convention_stage='').order_by('-date_debut')
    
    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'stagiaires': stagiaires_avec_convention,
        'title': 'Conventions de Stage'
    }
    return render(request, 'stagiaires/conventions_list.html', context)
#contrat 
@login_required
def contrat_create(request, stagiaire_id):
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)
    if request.method == 'POST':
        # Ajoute ici la logique pour créer le contrat
        # Par exemple, traiter un formulaire ou uploader un fichier
        messages.success(request, "Contrat créé avec succès.")
        return redirect('stagiaire_detail', stagiaire_id=stagiaire.id)
    return render(request, 'stagiaires/contrat_create.html', {'stagiaire': stagiaire})
@login_required
def contrat_detail(request, stagiaire_id):
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)
    # Ajoute ici la logique ou le contexte que tu veux afficher
    return render(request, 'stagiaires/contrat_detail.html', {'stagiaire': stagiaire})

@login_required
def convention_detail(request, stagiaire_id):
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)
    # Ajoute ici la logique ou le contexte que tu veux afficher
    return render(request, 'stagiaires/convention_detail.html', {'stagiaire': stagiaire})

@login_required
def attestations_list_view(request):
    """Vue pour afficher la liste des attestations de fin de stage"""
    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    # Récupérer toutes les attestations (stagiaires avec stage terminé)
    stagiaires_termines = Stagiaire.objects.filter(statut='TERMINE').order_by('-date_fin')
    
    context = {
    'user': request.user,
    'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
    'stagiaires_eligibles': stagiaires_termines,  # Ajoute cette ligne
    'title': 'Attestations de Fin de Stage'
}
    return render(request, 'stagiaires/attestations_list.html', context)



@login_required
def services_list_view(request):
    """Vue pour afficher la liste des services"""
    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['ADMIN', 'RH', 'ENCADRANT']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    # Filtrer les services selon le rôle de l'utilisateur
    if request.user.role == 'ADMIN' or request.user.role == 'RH':
        services = Service.objects.all().order_by('nom')
    elif request.user.role == 'ENCADRANT' and request.user.service:
        # Les encadrants ne voient que leur propre service
        services = Service.objects.filter(id=request.user.service.id)
    else:
        services = Service.objects.none()

    context = {
        'services': services,
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'can_add': request.user.role in ['ADMIN', 'RH']  # Les admins et RH peuvent ajouter des services
    }
    return render(request, 'stagiaires/services_list.html', context)


@login_required
def add_service_view(request):
    """Vue pour ajouter un service"""
    # Vérifier les permissions - les admins et RH peuvent ajouter des services
    if not hasattr(request.user, 'role') or request.user.role not in ['ADMIN', 'RH']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    if request.method == 'POST':
        form = ServiceForm(request.POST, user=request.user)
        if form.is_valid():
            service = form.save(commit=False)
            service.cree_par = request.user

            # Vérifier si un service avec le même nom existe déjà
            nom_existe = Service.objects.filter(nom__iexact=service.nom).exists()
            code_existe = Service.objects.filter(code_service__iexact=service.code_service).exists()

            if nom_existe:
                messages.error(request, f'Un service avec le nom "{service.nom}" existe déjà. Veuillez choisir un autre nom.')
                return render(request, 'stagiaires/add_service.html', {
                    'user': request.user,
                    'form': form,
                    'title': 'Ajouter un Service'
                })

            if code_existe:
                messages.error(request, f'Un service avec le code "{service.code_service}" existe déjà. Veuillez choisir un autre code.')
                return render(request, 'stagiaires/add_service.html', {
                    'user': request.user,
                    'form': form,
                    'title': 'Ajouter un Service'
                })

            try:
                service.save()
                messages.success(request, f'Le service "{service.nom}" a été ajouté avec succès!')
                return redirect('services_list')
            except Exception as e:
                error_message = str(e)

                # Gérer les erreurs d'intégrité spécifiques
                if 'UNIQUE constraint failed' in error_message or 'duplicate key' in error_message.lower():
                    if 'code_service' in error_message:
                        messages.error(request, f'Un service avec le code "{service.code_service}" existe déjà. Veuillez choisir un autre code.')
                    elif 'nom' in error_message:
                        messages.error(request, f'Un service avec le nom "{service.nom}" existe déjà. Veuillez choisir un autre nom.')
                    else:
                        messages.error(request, 'Ce service existe déjà. Veuillez vérifier le nom et le code.')
                else:
                    messages.error(request, f'Erreur lors de la création du service: {error_message}')

                return render(request, 'stagiaires/add_service.html', {
                    'user': request.user,
                    'form': form,
                    'title': 'Ajouter un Service'
                })
    else:
        form = ServiceForm(user=request.user)

    context = {
        'user': request.user,
        'form': form,
        'title': 'Ajouter un Service'
    }
    return render(request, 'stagiaires/add_service.html', context)


@login_required
def calendrier_encadrant_view(request):
    """Vue pour le calendrier des encadrants"""
    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['ADMIN', 'ENCADRANT', 'RH']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    from datetime import datetime, timedelta
    import calendar

    # Récupérer l'année et le mois depuis les paramètres GET
    today = datetime.now()
    year = int(request.GET.get('year', today.year))
    month = int(request.GET.get('month', today.month))

    # Calculer les dates de début et fin pour l'affichage
    start_date = datetime(year, month, 1)

    # Calculer le nombre de jours dans le mois
    days_in_month = calendar.monthrange(year, month)[1]
    end_date = datetime(year, month, days_in_month)

    # Récupérer les stagiaires selon le rôle
    stagiaires_queryset = Stagiaire.objects.filter(
        date_debut__lte=end_date,
        date_fin__gte=start_date
    )

    # Appliquer le filtrage par rôle (département pour les encadrants)
    stagiaires = filter_stagiaires_by_user_role(request.user, stagiaires_queryset).order_by('nom', 'prenom')

    # Générer les semaines du mois
    cal = calendar.Calendar(firstweekday=0)  # Lundi = 0
    month_days = cal.monthdayscalendar(year, month)

    # Créer la structure des semaines avec les dates
    semaines = []
    for week in month_days:
        semaine_data = []
        for day in week:
            if day == 0:
                semaine_data.append(None)
            else:
                date_obj = datetime(year, month, day)
                semaine_data.append({
                    'day': day,
                    'date': date_obj,
                    'is_weekend': date_obj.weekday() >= 5,
                    'is_today': date_obj.date() == today.date()
                })
        semaines.append(semaine_data)

    # Préparer les données des stagiaires avec leurs périodes
    stagiaires_data = []
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']

    for i, stagiaire in enumerate(stagiaires):
        color = colors[i % len(colors)]

        # Calculer la durée en semaines
        duree_jours = (stagiaire.date_fin - stagiaire.date_debut).days + 1
        duree_semaines = (duree_jours + 6) // 7  # Arrondir vers le haut

        # Calculer les positions dans le calendrier
        periodes = []
        current_date = max(stagiaire.date_debut, start_date.date())
        end_stage = min(stagiaire.date_fin, end_date.date())

        while current_date <= end_stage:
            # Trouver la semaine et le jour
            for week_idx, week in enumerate(semaines):
                for day_idx, day_data in enumerate(week):
                    if day_data and day_data['date'].date() == current_date:
                        periodes.append({
                            'week': week_idx,
                            'day': day_idx,
                            'date': current_date
                        })
                        break
            current_date += timedelta(days=1)

        stagiaires_data.append({
            'stagiaire': stagiaire,
            'color': color,
            'duree_semaines': duree_semaines,
            'duree_jours': duree_jours,
            'periodes': periodes,
            'debut_dans_mois': stagiaire.date_debut >= start_date.date(),
            'fin_dans_mois': stagiaire.date_fin <= end_date.date()
        })

    # Navigation mois précédent/suivant
    prev_month = month - 1 if month > 1 else 12
    prev_year = year if month > 1 else year - 1
    next_month = month + 1 if month < 12 else 1
    next_year = year if month < 12 else year + 1

    context = {
        'user': request.user,
        'year': year,
        'month': month,
        'month_name': calendar.month_name[month],
        'semaines': semaines,
        'stagiaires_data': stagiaires_data,
        'prev_month': prev_month,
        'prev_year': prev_year,
        'next_month': next_month,
        'next_year': next_year,
        'title': f'Calendrier des Stages - {calendar.month_name[month]} {year}'
    }

    return render(request, 'stagiaires/calendrier_encadrant.html', context)


@login_required
def calendrier_simple_view(request):
    """Vue pour le calendrier simple des encadrants"""
    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['ADMIN', 'ENCADRANT', 'RH']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    from datetime import datetime, timedelta
    import calendar

    # Récupérer l'année et le mois depuis les paramètres GET
    today = datetime.now()
    year = int(request.GET.get('year', today.year))
    month = int(request.GET.get('month', today.month))

    # Calculer les dates de début et fin pour l'affichage
    start_date = datetime(year, month, 1)

    # Calculer le nombre de jours dans le mois
    days_in_month = calendar.monthrange(year, month)[1]
    end_date = datetime(year, month, days_in_month)

    # Récupérer les stagiaires selon le rôle
    stagiaires_queryset = Stagiaire.objects.filter(
        date_debut__lte=end_date,
        date_fin__gte=start_date
    )

    # Appliquer le filtrage par rôle (département pour les encadrants)
    stagiaires = filter_stagiaires_by_user_role(request.user, stagiaires_queryset).order_by('nom', 'prenom')

    # Générer les semaines du mois
    cal = calendar.Calendar(firstweekday=0)  # Lundi = 0
    month_days = cal.monthdayscalendar(year, month)

    # Créer la structure des semaines avec les dates
    semaines = []
    for week in month_days:
        semaine_data = []
        for day in week:
            if day == 0:
                semaine_data.append(None)
            else:
                date_obj = datetime(year, month, day)
                semaine_data.append({
                    'day': day,
                    'date': date_obj,
                    'is_weekend': date_obj.weekday() >= 5,
                    'is_today': date_obj.date() == today.date()
                })
        semaines.append(semaine_data)

    # Préparer les données des stagiaires avec leurs périodes
    stagiaires_data = []
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#FF8C94', '#A8E6CF']

    for i, stagiaire in enumerate(stagiaires):
        color = colors[i % len(colors)]

        # Calculer la durée en semaines
        duree_jours = (stagiaire.date_fin - stagiaire.date_debut).days + 1
        duree_semaines = (duree_jours + 6) // 7  # Arrondir vers le haut

        # Calculer les positions dans le calendrier
        periodes = []
        current_date = max(stagiaire.date_debut, start_date.date())
        end_stage = min(stagiaire.date_fin, end_date.date())

        while current_date <= end_stage:
            # Trouver la semaine et le jour
            for week_idx, week in enumerate(semaines):
                for day_idx, day_data in enumerate(week):
                    if day_data and day_data['date'].date() == current_date:
                        periodes.append({
                            'week': week_idx,
                            'day': day_idx,
                            'date': current_date
                        })
                        break
            current_date += timedelta(days=1)

        stagiaires_data.append({
            'stagiaire': stagiaire,
            'color': color,
            'duree_semaines': duree_semaines,
            'duree_jours': duree_jours,
            'periodes': periodes,
            'debut_dans_mois': stagiaire.date_debut >= start_date.date(),
            'fin_dans_mois': stagiaire.date_fin <= end_date.date()
        })

    # Navigation mois précédent/suivant
    prev_month = month - 1 if month > 1 else 12
    prev_year = year if month > 1 else year - 1
    next_month = month + 1 if month < 12 else 1
    next_year = year if month < 12 else year + 1

    context = {
        'user': request.user,
        'year': year,
        'month': month,
        'month_name': calendar.month_name[month],
        'semaines': semaines,
        'stagiaires_data': stagiaires_data,
        'prev_month': prev_month,
        'prev_year': prev_year,
        'next_month': next_month,
        'next_year': next_year,
        'title': f'Calendrier Simple - {calendar.month_name[month]} {year}'
    }

    return render(request, 'stagiaires/calendrier_simple.html', context)


@login_required
def edit_service_view(request, service_id):
    """Vue pour modifier un service"""
    try:
        service = Service.objects.get(id=service_id)
    except Service.DoesNotExist:
        messages.error(request, 'Service non trouvé.')
        return redirect('services_list')
    
    # Vérifier les permissions
    if request.user.role == 'ADMIN':
        # Les admins peuvent modifier tous les services
        pass
    elif request.user.role == 'ENCADRANT' and request.user.service and request.user.service.id == service.id:
        # Les encadrants ne peuvent modifier que leur propre service
        pass
    else:
        messages.error(request, 'Vous n\'avez pas les permissions pour modifier ce service.')
        return redirect('services_list')
    
    if request.method == 'POST':
        form = ServiceForm(request.POST, instance=service, user=request.user)
        if form.is_valid():
            service = form.save(commit=False)
            service.modifie_par = request.user
            service.save()
            messages.success(request, f'Le service {service.nom} a été modifié avec succès!')
            return redirect('services_list')
    else:
        form = ServiceForm(instance=service, user=request.user)
    
    context = {
        'user': request.user,
        'form': form,
        'service': service,
        'title': 'Modifier un Service'
    }
    return render(request, 'stagiaires/edit_service.html', context)



@login_required
def sujets_list_view(request):
    print("User role:", request.user.role)  # Debug

    if request.user.role == 'ENCADRANT':
        # Filtrage strict : seulement les sujets du service de l'encadrant
        if hasattr(request.user, 'service') and request.user.service:
            sujets = Sujet.objects.filter(service=request.user.service).order_by('-date_creation')
        else:
            # Si l'encadrant n'a pas de service, montrer seulement ses sujets directs
            sujets = Sujet.objects.filter(encadrant=request.user).order_by('-date_creation')
    else:
        # Admin et RH voient tous les sujets
        sujets = Sujet.objects.all().order_by('-date_creation')

    print("Sujets trouvés:", sujets.count())  # Debug
    return render(request, 'stagiaires/sujets_list.html', {'sujets': sujets})


@login_required
def add_sujet_view(request):
    """Vue pour ajouter un sujet de stage"""
    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['ADMIN', 'ENCADRANT', 'RH']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')

    if request.method == 'POST':
        print(f"DEBUG: POST data: {request.POST}")  # Debug
        form = SujetForm(request.POST, user=request.user)
        print(f"DEBUG: Form is_valid: {form.is_valid()}")  # Debug

        if form.is_valid():
            sujet = form.save(commit=False)
            sujet.cree_par = request.user

            # Pour les encadrants, toujours assigner leur service
            if request.user.role == 'ENCADRANT' and hasattr(request.user, 'service') and request.user.service:
                sujet.service = request.user.service
                print(f"DEBUG: Service assigné automatiquement: {sujet.service}")  # Debug

            # Si pas de service défini et que c'est requis, utiliser un service par défaut ou générer une erreur
            if not sujet.service and request.user.role == 'ENCADRANT':
                messages.error(request, 'Vous devez avoir un service assigné pour créer un sujet.')
                return render(request, 'stagiaires/add_sujet.html', {
                    'user': request.user,
                    'form': form,
                    'title': 'Ajouter un Sujet de Stage'
                })

            try:
                sujet.save()
                print(f"DEBUG: Sujet sauvegardé avec succès: {sujet.id}")  # Debug
                messages.success(request, f'Le sujet "{sujet.titre}" a été ajouté avec succès!')
                return redirect('sujets_list')
            except Exception as e:
                print(f"DEBUG: Erreur lors de la sauvegarde: {e}")  # Debug
                messages.error(request, f'Erreur lors de la sauvegarde: {e}')
        else:
            print(f"DEBUG: Erreurs du formulaire: {form.errors}")  # Debug
            messages.error(request, 'Veuillez corriger les erreurs dans le formulaire.')
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = SujetForm(user=request.user)

    context = {
        'user': request.user,
        'form': form,
        'title': 'Ajouter un Sujet de Stage'
    }
    return render(request, 'stagiaires/add_sujet.html', context)


@login_required
def edit_thematique_view(request, thematique_id):
    """Vue pour modifier une thématique de stage"""
    # Récupérer la thématique ou renvoyer une erreur 404
    thematique = get_object_or_404(Thematique, id=thematique_id)
    
    # Vérifier que l'utilisateur a le droit de modifier cette thématique
    # (admin, RH ou créateur de la thématique)
    if not (request.user.role in ['ADMIN', 'RH'] or request.user == thematique.cree_par):
        messages.error(request, "Vous n'avez pas les droits pour modifier cette thématique.")
        return redirect('thematiques_list')
    
    if request.method == 'POST':
        form = ThematiqueForm(request.POST, instance=thematique)
        if form.is_valid():
            form.save()
            messages.success(request, f'La thématique "{thematique.titre}" a été modifiée avec succès.')
            return redirect('thematiques_list')
    else:
        form = ThematiqueForm(instance=thematique)
    
    context = {
        'form': form,
        'thematique': thematique,
        'title': f'Modifier la thématique: {thematique.titre}'
    }
    return render(request, 'stagiaires/edit_thematique.html', context)

@login_required
def delete_thematique_view(request, thematique_id):
    """Vue pour supprimer une thématique"""
    thematique = get_object_or_404(Thematique, id=thematique_id)
    
    # Vérifier les permissions (ADMIN, RH, ENCADRANT ou créateur)
    if not (request.user.role in ['ADMIN', 'RH', 'ENCADRANT'] or request.user == thematique.cree_par):
        messages.error(request, "Vous n'avez pas les droits pour supprimer cette thématique.")
        return redirect('thematiques_list')
    
    # Vérifier si la thématique est utilisée par des sujets
    sujets_associes = Sujet.objects.filter(thematique=thematique).count()
    if sujets_associes > 0:
        messages.error(
            request, 
            f"Impossible de supprimer cette thématique car elle est utilisée par {sujets_associes} sujet(s)."
        )
        return redirect('thematiques_list')
    
    # Supprimer la thématique
    nom_thematique = thematique.titre
    thematique.delete()
    messages.success(request, f'La thématique "{nom_thematique}" a été supprimée avec succès.')
    
    return redirect('thematiques_list')

@login_required
def edit_sujet_view(request, sujet_id):
    """Vue pour modifier un sujet"""
    sujet = get_object_or_404(Sujet, id=sujet_id)
    
    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH', 'ENCADRANT']:
        messages.error(request, "Vous n'avez pas les droits pour modifier ce sujet.")
        return redirect('sujets_list')
    
    if request.method == 'POST':
        form = SujetForm(request.POST, instance=sujet)
        if form.is_valid():
            form.save()
            messages.success(request, f'Le sujet "{sujet.titre}" a été modifié avec succès.')
            return redirect('sujets_list')
    else:
        form = SujetForm(instance=sujet)
    
    context = {
        'form': form,
        'sujet': sujet,
        'title': f'Modifier le sujet: {sujet.titre}'
    }
    return render(request, 'stagiaires/edit_sujet.html', context)

@login_required
def delete_service_view(request, service_id):
    service = get_object_or_404(Service, id=service_id)
    
    # Vérification des permissions
    if not request.user.is_superuser:
        messages.error(request, "Vous n'avez pas les droits pour supprimer un service")
        return redirect('services_list')
    
    if request.method == 'POST':
        service.delete()
        messages.success(request, f"Le service {service.nom} a été supprimé avec succès")
        return redirect('services_list')
    
    # Si méthode GET, afficher la confirmation
    return render(request, 'stagiaires/confirm_delete.html', {
        'object': service,
        'object_type': 'service',
        'object_name': service.nom,
        'cancel_url': 'services_list'
    })

@login_required
def delete_sujet_view(request, sujet_id):
    """Vue pour supprimer un sujet"""
    sujet = get_object_or_404(Sujet, id=sujet_id)
    
    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH'] and sujet.cree_par != request.user:
        messages.error(request, "Vous n'avez pas les droits pour supprimer ce sujet.")
        return redirect('sujets_list')
    
    # Vérifier si le sujet est utilisé par des stagiaires
    stagiaires_associes = Stagiaire.objects.filter(sujet=sujet).count()
    if stagiaires_associes > 0:
        messages.error(
            request, 
            f"Impossible de supprimer ce sujet car il est utilisé par {stagiaires_associes} stagiaire(s)."
        )
        return redirect('sujets_list')
    
    # Supprimer le sujet
    nom_sujet = sujet.titre
    sujet.delete()
    messages.success(request, f'Le sujet "{nom_sujet}" a été supprimé avec succès.')
    
    return redirect('sujets_list')
@login_required
@require_POST
def update_tache_status(request, tache_id):
    """Vue AJAX pour mettre à jour le statut d'une tâche"""
    tache = get_object_or_404(TacheStage, id=tache_id)
    stagiaire = tache.stagiaire
    
    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH'] and request.user != stagiaire.encadrant:
        return JsonResponse({'success': False, 'error': "Vous n'avez pas les droits pour modifier cette tâche."}, status=403)
    
    # Récupérer le nouveau statut
    nouveau_statut = request.POST.get('statut')
    if nouveau_statut not in dict(TacheStage.STATUT_CHOICES):
        return JsonResponse({'success': False, 'error': "Statut invalide."}, status=400)
    
    # Mettre à jour le statut et les dates si nécessaire
    tache.statut = nouveau_statut
    
    # Si la tâche passe à "En cours", définir la date de début réelle
    if nouveau_statut == 'EN_COURS' and not tache.date_debut_reelle:
        tache.date_debut_reelle = timezone.now().date()
    
    # Si la tâche passe à "Terminée", définir la date de fin réelle
    if nouveau_statut == 'TERMINEE' and not tache.date_fin_reelle:
        tache.date_fin_reelle = timezone.now().date()
    
    tache.save()
    
    return JsonResponse({
        'success': True, 
        'message': f"Statut mis à jour avec succès : {tache.get_statut_display()}",
        'nouveau_statut': tache.get_statut_display(),
        'date_debut_reelle': tache.date_debut_reelle.strftime('%d/%m/%Y') if tache.date_debut_reelle else None,
        'date_fin_reelle': tache.date_fin_reelle.strftime('%d/%m/%Y') if tache.date_fin_reelle else None
    })


@login_required
def toggle_thematique_view(request, thematique_id):
    """Vue pour activer/désactiver une thématique"""
    thematique = get_object_or_404(Thematique, id=thematique_id)
    
    # Vérifier les permissions (ADMIN, RH, ENCADRANT ou créateur)
    if not (request.user.role in ['ADMIN', 'RH', 'ENCADRANT'] or request.user == thematique.cree_par):
        messages.error(request, "Vous n'avez pas les droits pour modifier cette thématique.")
        return redirect('thematiques_list')
    
    # Inverser le statut actif
    thematique.active = not thematique.active
    thematique.save()
    
    status = "activée" if thematique.active else "désactivée"
    messages.success(request, f'La thématique "{thematique.titre}" a été {status} avec succès.')
    
    return redirect('thematiques_list')


@login_required
def toggle_sujet_view(request, sujet_id):
    """Vue pour activer/désactiver un sujet"""
    sujet = get_object_or_404(Sujet, id=sujet_id)
    
    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH'] and sujet.cree_par != request.user:
        messages.error(request, "Vous n'avez pas les droits pour modifier ce sujet.")
        return redirect('sujets_list')
    
    # Inverser le statut actif
    sujet.actif = not sujet.actif
    sujet.save()
    
    status = "activé" if sujet.actif else "désactivé"
    messages.success(request, f'Le sujet "{sujet.titre}" a été {status} avec succès.')
    
    return redirect('sujets_list')


@login_required
def delete_thematique_view(request, thematique_id):
    """Vue pour supprimer une thématique"""
    thematique = get_object_or_404(Thematique, id=thematique_id)
    
    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH']:
        messages.error(request, "Vous n'avez pas les droits pour supprimer cette thématique.")
        return redirect('thematiques_list')
    
    # Vérifier si la thématique est utilisée par des sujets
    sujets_associes = Sujet.objects.filter(thematique=thematique).count()
    if sujets_associes > 0:
        messages.error(
            request, 
            f"Impossible de supprimer cette thématique car elle est utilisée par {sujets_associes} sujet(s)."
        )
        return redirect('thematiques_list')
    
    # Supprimer la thématique
    nom_thematique = thematique.titre
    thematique.delete()
    messages.success(request, f'La thématique "{nom_thematique}" a été supprimée avec succès.')
    
    return redirect('thematiques_list')


@login_required
def delete_sujet_view(request, sujet_id):
    """Vue pour supprimer un sujet"""
    sujet = get_object_or_404(Sujet, id=sujet_id)
    
    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH'] and sujet.cree_par != request.user:
        messages.error(request, "Vous n'avez pas les droits pour supprimer ce sujet.")
        return redirect('sujets_list')
    
    # Vérifier si le sujet est utilisé par des stagiaires
    stagiaires_associes = Stagiaire.objects.filter(sujet=sujet).count()
    if stagiaires_associes > 0:
        messages.error(
            request, 
            f"Impossible de supprimer ce sujet car il est utilisé par {stagiaires_associes} stagiaire(s)."
        )
        return redirect('sujets_list')
    
    # Supprimer le sujet
    nom_sujet = sujet.titre
    sujet.delete()
    messages.success(request, f'Le sujet "{nom_sujet}" a été supprimé avec succès.')
    
    return redirect('sujets_list')



@login_required
def evaluation_stagiaire_view(request, stagiaire_id):
    """Vue pour évaluer un stagiaire"""
    # Vérifier que l'utilisateur est un encadrant ou un admin
    if request.user.role not in ['ADMIN', 'ENCADRANT', 'RH']:
        messages.error(request, "Vous n'avez pas les droits pour évaluer un stagiaire.")
        return redirect('stagiaires_list')
    
    # Récupérer le stagiaire
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)
    
    # Vérifier que l'utilisateur est l'encadrant du stagiaire ou un admin/RH
    if request.user.role not in ['ADMIN', 'RH'] and request.user != stagiaire.encadrant:
        messages.error(request, "Vous ne pouvez évaluer que les stagiaires que vous encadrez.")
        return redirect('stagiaires_list')
    
    if request.method == 'POST':
        # Logique pour traiter le formulaire d'évaluation
        # Pour l'instant, nous allons simplement rediriger avec un message de succès
        messages.success(request, f"L'évaluation du stagiaire {stagiaire.prenom} {stagiaire.nom} a été enregistrée.")
        return redirect('stagiaires_list')
    
    context = {
        'stagiaire': stagiaire,
        'title': f'Évaluation de {stagiaire.prenom} {stagiaire.nom}'
    }
    return render(request, 'stagiaires/evaluation_stagiaire.html', context)


@login_required
def calendrier_stagiaires_view(request):
    """Vue pour afficher le calendrier des stagiaires"""
    if not hasattr(request.user, 'role') or request.user.role not in ['RH', 'ADMIN', 'ENCADRANT']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    # Récupérer les paramètres de filtrage
    vue = request.GET.get('vue', 'tous')
    service_id = request.GET.get('service', '')
    encadrant_id = request.GET.get('encadrant', '')
    annee = request.GET.get('annee', str(timezone.now().year))
    mois = request.GET.get('mois', str(timezone.now().month))
    
    # Filtrer les stagiaires
    stagiaires = Stagiaire.objects.select_related('encadrant', 'service').all()
    
    # Filtrer par année
    try:
        annee = int(annee)
        mois = int(mois)
        
        # Créer les dates de début et fin du mois sélectionné
        premier_jour = date(annee, mois, 1)
        
        # Calculer le dernier jour du mois sans utiliser calendar
        if mois == 12:
            dernier_jour = date(annee + 1, 1, 1) - timedelta(days=1)
        else:
            dernier_jour = date(annee, mois + 1, 1) - timedelta(days=1)
        
        # Filtrer les stagiaires dont la période chevauche le mois sélectionné
        stagiaires = stagiaires.filter(
            Q(date_debut__lte=dernier_jour) & Q(date_fin__gte=premier_jour)
        )
    except ValueError:
        annee = timezone.now().year
        mois = timezone.now().month
    
    # Filtrer par service si spécifié
    if service_id:
        try:
            stagiaires = stagiaires.filter(service_id=int(service_id))
        except ValueError:
            pass
    
    # Filtrer par encadrant si spécifié
    if encadrant_id:
        try:
            stagiaires = stagiaires.filter(encadrant_id=int(encadrant_id))
        except ValueError:
            pass
    
    # Appliquer le filtrage par rôle utilisateur
    stagiaires = filter_stagiaires_by_user_role(request.user, stagiaires)
    
    # Récupérer tous les services et encadrants pour les filtres
    services = Service.objects.filter(actif=True)
    encadrants = CustomUser.objects.filter(role='ENCADRANT')
    
    # Générer la liste des années (année actuelle +/- 2 ans)
    current_year = timezone.now().year
    annees = list(range(current_year - 2, current_year + 3))
    
    # Générer la liste des mois
    mois_liste = [
        {'numero': 1, 'nom': 'Janvier'},
        {'numero': 2, 'nom': 'Février'},
        {'numero': 3, 'nom': 'Mars'},
        {'numero': 4, 'nom': 'Avril'},
        {'numero': 5, 'nom': 'Mai'},
        {'numero': 6, 'nom': 'Juin'},
        {'numero': 7, 'nom': 'Juillet'},
        {'numero': 8, 'nom': 'Août'},
        {'numero': 9, 'nom': 'Septembre'},
        {'numero': 10, 'nom': 'Octobre'},
        {'numero': 11, 'nom': 'Novembre'},
        {'numero': 12, 'nom': 'Décembre'}
    ]
    
    # Organiser les stagiaires par service ou encadrant si nécessaire
    stagiaires_par_service = {}
    stagiaires_par_encadrant = {}
    
    if vue == 'service':
        for stagiaire in stagiaires:
            service_nom = stagiaire.service.nom if stagiaire.service else stagiaire.get_departement_display()
            if service_nom not in stagiaires_par_service:
                stagiaires_par_service[service_nom] = []
            stagiaires_par_service[service_nom].append(stagiaire)
    elif vue == 'encadrant':
        for stagiaire in stagiaires:
            encadrant_nom = stagiaire.encadrant.get_full_name() if stagiaire.encadrant else "Non assigné"
            if encadrant_nom not in stagiaires_par_encadrant:
                stagiaires_par_encadrant[encadrant_nom] = []
            stagiaires_par_encadrant[encadrant_nom].append(stagiaire)
    
    # Nom du mois et année pour l'affichage
    mois_noms = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre']
    current_month_year = f"{mois_noms[mois-1]} {annee}"

    # Générer des couleurs distinctes pour chaque stagiaire
    couleurs_disponibles = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
        '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
        '#A3E4D7', '#F9E79F', '#D5A6BD', '#AED6F1', '#A9DFBF'
    ]

    # Ajouter la couleur directement à chaque stagiaire
    stagiaires_avec_couleurs = []
    for i, stagiaire in enumerate(stagiaires):
        couleur_index = i % len(couleurs_disponibles)
        stagiaire.couleur = couleurs_disponibles[couleur_index]
        stagiaires_avec_couleurs.append(stagiaire)

    # Créer le calendrier jour par jour pour le mois sélectionné
    import calendar
    cal = calendar.monthcalendar(annee, mois)

    # Créer la structure du calendrier avec les stagiaires par jour
    calendrier_jours = []
    for semaine in cal:
        semaine_jours = []
        for jour in semaine:
            if jour == 0:
                semaine_jours.append(None)  # Jour vide
            else:
                date_jour = date(annee, mois, jour)
                stagiaires_du_jour = []

                for stagiaire in stagiaires_avec_couleurs:
                    if stagiaire.date_debut <= date_jour <= stagiaire.date_fin:
                        stagiaires_du_jour.append(stagiaire)

                semaine_jours.append({
                    'jour': jour,
                    'date': date_jour,
                    'stagiaires': stagiaires_du_jour,
                    'est_aujourd_hui': date_jour == date.today()
                })
        calendrier_jours.append(semaine_jours)

    context = {
        'stagiaires': stagiaires_avec_couleurs,
        'stagiaires_par_service': stagiaires_par_service,
        'stagiaires_par_encadrant': stagiaires_par_encadrant,
        'services': services,
        'encadrants': encadrants,
        'vue': vue,
        'selected_service': int(service_id) if service_id.isdigit() else '',
        'selected_encadrant': int(encadrant_id) if encadrant_id.isdigit() else '',
        'selected_annee': annee,
        'selected_mois': mois,
        'annees': annees,
        'mois_liste': mois_liste,
        'current_month_year': current_month_year,
        'calendrier_jours': calendrier_jours,
    }
    
    return render(request, 'stagiaires/calendrier_stagiaires.html', context)


@login_required
def niveaux_ecole_list_view(request):
    """Vue pour afficher et gérer les niveaux d'études"""
    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['ADMIN', 'RH', 'ENCADRANT']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    # Récupérer tous les niveaux d'études avec le nombre de stagiaires
    niveaux = Stagiaire.objects.values('niveau_etude').annotate(
        stagiaires_count=models.Count('id')
    ).order_by('niveau_etude')
    
    # Filtrer les valeurs vides
    niveaux = [niveau for niveau in niveaux if niveau['niveau_etude']]
    
    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'niveaux': niveaux,
        'title': 'Niveaux d\'études'
    }
    return render(request, 'stagiaires/niveaux_ecole_list.html', context)

@login_required
def ecoles_list_view(request):
    """Vue pour afficher et gérer les établissements"""
    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role not in ['ADMIN', 'RH', 'ENCADRANT']:
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    # Récupérer tous les établissements avec le nombre de stagiaires
    ecoles = Stagiaire.objects.values('etablissement').annotate(
        stagiaires_count=models.Count('id')
    ).order_by('etablissement')
    
    # Filtrer les valeurs vides
    ecoles = [ecole for ecole in ecoles if ecole['etablissement']]
    
    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'ecoles': ecoles,
        'title': 'Établissements'
    }
    return render(request, 'stagiaires/ecoles_list.html', context)

@login_required
def add_stagiaire_encadrant_view(request):
    """Vue pour les encadrants qui ajoutent un stagiaire"""
    if not hasattr(request.user, 'role') or request.user.role != 'ENCADRANT':
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    if request.method == 'POST':
        form = StagiaireFormEncadrant(request.POST, request.FILES, user=request.user)
        if form.is_valid():
            stagiaire = form.save(commit=False)
            stagiaire.cree_par = request.user
            stagiaire.encadrant = request.user
            
            # Définir le service du stagiaire comme celui de l'encadrant
            if hasattr(request.user, 'service') and request.user.service:
                stagiaire.service = request.user.service
            
            stagiaire.save()
            messages.success(request, f'Le stagiaire {stagiaire.prenom} {stagiaire.nom} a été ajouté avec succès.')
            return redirect('stagiaires_list')
    else:
        form = StagiaireFormEncadrant(user=request.user)
    
    context = {
        'form': form,
        'title': 'Ajouter un stagiaire',
        'user': request.user
    }
    return render(request, 'stagiaires/add_stagiaire_encadrant.html', context)


@login_required
@require_POST
def delete_stagiaire_view(request, stagiaire_id):
    """Vue pour supprimer un stagiaire"""
    # Vérifier que l'utilisateur est un administrateur
    if not hasattr(request.user, 'role') or request.user.role != 'ADMIN':
        messages.error(request, 'Accès non autorisé. Seuls les administrateurs peuvent supprimer des stagiaires.')
        return redirect('stagiaires_list')
    
    try:
        stagiaire = Stagiaire.objects.get(id=stagiaire_id)
        nom_complet = f"{stagiaire.prenom} {stagiaire.nom}"
        
        # Supprimer le stagiaire
        stagiaire.delete()
        
        messages.success(request, f'Le stagiaire {nom_complet} a été supprimé avec succès.')
    except Stagiaire.DoesNotExist:
        messages.error(request, 'Stagiaire non trouvé.')
    except Exception as e:
        messages.error(request, f'Erreur lors de la suppression: {str(e)}')
    
    return redirect('stagiaires_list')

@login_required
@require_POST
def add_niveau_view(request):
    """Vue pour ajouter un niveau d'étude"""
    if not hasattr(request.user, 'role') or request.user.role != 'ADMIN':
        messages.error(request, 'Accès non autorisé.')
        return redirect('niveaux_ecole_list')
    
    niveau_etude = request.POST.get('niveau_etude', '').strip()
    if niveau_etude:
        # Vérifier si le niveau existe déjà
        if Stagiaire.objects.filter(niveau_etude=niveau_etude).exists():
            messages.warning(request, f'Le niveau "{niveau_etude}" existe déjà.')
        else:
            # Créer un stagiaire temporaire pour ajouter le niveau (sera supprimé ensuite)
            temp_stagiaire = Stagiaire.objects.create(
                nom="Temporaire",
                prenom="Temporaire",
                email="<EMAIL>",
                date_naissance=timezone.now().date(),
                date_debut=timezone.now().date(),
                date_fin=timezone.now().date() + timedelta(days=1),
                niveau_etude=niveau_etude,
                departement="INFORMATIQUE",
                cree_par=request.user
            )
            temp_stagiaire.delete()
            messages.success(request, f'Le niveau "{niveau_etude}" a été ajouté avec succès.')
    else:
        messages.error(request, 'Veuillez saisir un niveau d\'étude.')
    
    return redirect('niveaux_ecole_list')

@login_required
@require_POST
def delete_niveau_view(request):
    """Vue pour supprimer un niveau d'étude"""
    if not hasattr(request.user, 'role') or request.user.role != 'ADMIN':
        messages.error(request, 'Accès non autorisé.')
        return redirect('niveaux_ecole_list')
    
    niveau_etude = request.POST.get('niveau_etude', '')
    if niveau_etude:
        # Vérifier si le niveau est utilisé par des stagiaires
        stagiaires_count = Stagiaire.objects.filter(niveau_etude=niveau_etude).count()
        if stagiaires_count > 0:
            messages.error(
                request, 
                f'Impossible de supprimer ce niveau car il est utilisé par {stagiaires_count} stagiaire(s).'
            )
        else:
            messages.success(request, f'Le niveau "{niveau_etude}" a été supprimé avec succès.')
    else:
        messages.error(request, 'Niveau d\'étude non spécifié.')
    
    return redirect('niveaux_ecole_list')

@login_required
@require_POST
def add_ecole_view(request):
    """Vue pour ajouter un établissement"""
    if not hasattr(request.user, 'role') or request.user.role != 'ADMIN':
        messages.error(request, 'Accès non autorisé.')
        return redirect('ecoles_list')
    
    etablissement = request.POST.get('etablissement', '').strip()
    if etablissement:
        # Vérifier si l'établissement existe déjà
        if Stagiaire.objects.filter(etablissement=etablissement).exists():
            messages.warning(request, f'L\'établissement "{etablissement}" existe déjà.')
        else:
            # Créer un stagiaire temporaire pour ajouter l'établissement (sera supprimé ensuite)
            temp_stagiaire = Stagiaire.objects.create(
                nom="Temporaire",
                prenom="Temporaire",
                email="<EMAIL>",
                date_naissance=timezone.now().date(),
                date_debut=timezone.now().date(),
                date_fin=timezone.now().date() + timedelta(days=1),
                etablissement=etablissement,
                niveau_etude="Temporaire",
                departement="INFORMATIQUE",
                cree_par=request.user
            )
            temp_stagiaire.delete()
            messages.success(request, f'L\'établissement "{etablissement}" a été ajouté avec succès.')
    else:
        messages.error(request, 'Veuillez saisir un nom d\'établissement.')
    
    return redirect('ecoles_list')

@login_required
@require_POST
def delete_ecole_view(request):
    """Vue pour supprimer un établissement"""
    if not hasattr(request.user, 'role') or request.user.role != 'ADMIN':
        messages.error(request, 'Accès non autorisé.')
        return redirect('ecoles_list')
    
    etablissement = request.POST.get('etablissement', '')
    if etablissement:
        # Vérifier si l'établissement est utilisé par des stagiaires
        stagiaires_count = Stagiaire.objects.filter(etablissement=etablissement).count()
        if stagiaires_count > 0:
            messages.error(
                request, 
                f'Impossible de supprimer cet établissement car il est utilisé par {stagiaires_count} stagiaire(s).'
            )
        else:
            messages.success(request, f'L\'établissement "{etablissement}" a été supprimé avec succès.')
    else:
        messages.error(request, 'Établissement non spécifié.')
    
    return redirect('ecoles_list')

@login_required
@require_POST
def delete_duree_estimee_view(request, duree_id):
    """Vue pour supprimer une durée estimée"""
    # Vérifier les permissions (ADMIN uniquement)
    if not hasattr(request.user, 'role') or request.user.role != 'ADMIN':
        messages.error(request, 'Accès non autorisé. Seuls les administrateurs peuvent supprimer des durées estimées.')
        return redirect('durees_estimees')
    
    try:
        duree = DureeEstimee.objects.get(id=duree_id)
        
        # Vérifier si la durée est utilisée par des stagiaires
        stagiaires_count = Stagiaire.objects.filter(duree_estimee=duree.duree).count()
        if stagiaires_count > 0:
            messages.error(
                request, 
                f'Impossible de supprimer cette durée car elle est utilisée par {stagiaires_count} stagiaire(s).'
            )
        else:
            # Supprimer la durée estimée
            duree_jours = duree.duree
            duree.delete()
            messages.success(request, f'La durée estimée de {duree_jours} jours a été supprimée avec succès.')
    except DureeEstimee.DoesNotExist:
        messages.error(request, 'Durée estimée non trouvée.')
    except Exception as e:
        messages.error(request, f'Erreur lors de la suppression: {str(e)}')
    
    return redirect('durees_estimees')

@login_required
def parametrage_view(request):
    """Vue pour la page de paramétrage (admin uniquement)"""
    # Vérifier les permissions
    if not hasattr(request.user, 'role') or request.user.role != 'ADMIN':
        messages.error(request, 'Accès non autorisé.')
        return redirect('dashboard')
    
    context = {
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'title': 'Paramétrage du système'
    }
    return render(request, 'stagiaires/parametrage.html', context)

@login_required
def taches_stagiaire_view(request, stagiaire_id):
    """Vue pour afficher et gérer les tâches d'un stagiaire"""
    try:
        stagiaire = Stagiaire.objects.get(id=stagiaire_id)
    except Stagiaire.DoesNotExist:
        messages.error(request, "Ce stagiaire n'existe pas.")
        return redirect('stagiaires_list')
    
    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH', 'ENCADRANT']:
        messages.error(request, "Vous n'avez pas les permissions nécessaires.")
        return redirect('dashboard')
    
    # Si l'utilisateur est un encadrant, vérifier qu'il est bien l'encadrant du stagiaire
    if request.user.role == 'ENCADRANT' and stagiaire.encadrant != request.user:
        if not (stagiaire.service and request.user.service and stagiaire.service.id == request.user.service.id):
            messages.error(request, "Vous n'êtes pas l'encadrant de ce stagiaire.")
            return redirect('stagiaires_list')
    
    # Récupérer les tâches du stagiaire
    taches = Tache.objects.filter(stagiaire=stagiaire).order_by('-date_creation')
    
    # Formulaire pour ajouter une tâche
    if request.method == 'POST':
        form = TacheForm(request.POST)
        if form.is_valid():
            tache = form.save(commit=False)
            tache.stagiaire = stagiaire
            tache.creee_par = request.user
            tache.save()
            messages.success(request, "La tâche a été ajoutée avec succès.")
            return redirect('taches_stagiaire', stagiaire_id=stagiaire.id)
    else:
        form = TacheForm()
    
    context = {
        'stagiaire': stagiaire,
        'taches': taches,
        'form': form,
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur'
    }
    return render(request, 'stagiaires/taches_stagiaire.html', context)

@login_required
def demarrer_tache_view(request, tache_id):
    """Vue pour démarrer une tâche"""
    try:
        tache = Tache.objects.get(id=tache_id)
    except Tache.DoesNotExist:
        messages.error(request, "Cette tâche n'existe pas.")
        return redirect('stagiaires_list')
    
    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH', 'ENCADRANT']:
        messages.error(request, "Vous n'avez pas les permissions nécessaires.")
        return redirect('dashboard')
    
    # Si l'utilisateur est un encadrant, vérifier qu'il est bien l'encadrant du stagiaire
    if request.user.role == 'ENCADRANT' and tache.stagiaire.encadrant != request.user:
        if not (tache.stagiaire.service and request.user.service and tache.stagiaire.service.id == request.user.service.id):
            messages.error(request, "Vous n'êtes pas l'encadrant de ce stagiaire.")
            return redirect('stagiaires_list')
    
    # Démarrer la tâche
    if tache.demarrer():
        messages.success(request, f"La tâche '{tache.titre}' a été démarrée.")
    else:
        messages.error(request, f"Impossible de démarrer la tâche '{tache.titre}'.")
    
    return redirect('taches_stagiaire', stagiaire_id=tache.stagiaire.id)


@login_required
def terminer_tache_view(request, tache_id):
    """Vue pour terminer une tâche"""
    try:
        tache = Tache.objects.get(id=tache_id)
    except Tache.DoesNotExist:
        messages.error(request, "Cette tâche n'existe pas.")
        return redirect('stagiaires_list')
    
    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH', 'ENCADRANT']:
        messages.error(request, "Vous n'avez pas les permissions nécessaires.")
        return redirect('dashboard')
    
    # Si l'utilisateur est un encadrant, vérifier qu'il est bien l'encadrant du stagiaire
    if request.user.role == 'ENCADRANT' and tache.stagiaire.encadrant != request.user:
        if not (tache.stagiaire.service and request.user.service and tache.stagiaire.service.id == request.user.service.id):
            messages.error(request, "Vous n'êtes pas l'encadrant de ce stagiaire.")
            return redirect('stagiaires_list')
    
    # Terminer la tâche
    if tache.terminer():
        messages.success(request, f"La tâche '{tache.titre}' a été marquée comme terminée.")
    else:
        messages.error(request, f"Impossible de terminer la tâche '{tache.titre}'.")
    
    return redirect('taches_stagiaire', stagiaire_id=tache.stagiaire.id)


@login_required
def annuler_tache_view(request, tache_id):
    """Vue pour annuler une tâche"""
    try:
        tache = Tache.objects.get(id=tache_id)
    except Tache.DoesNotExist:
        messages.error(request, "Cette tâche n'existe pas.")
        return redirect('stagiaires_list')
    
    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH', 'ENCADRANT']:
        messages.error(request, "Vous n'avez pas les permissions nécessaires.")
        return redirect('dashboard')
    
    # Si l'utilisateur est un encadrant, vérifier qu'il est bien l'encadrant du stagiaire
    if request.user.role == 'ENCADRANT' and tache.stagiaire.encadrant != request.user:
        if not (tache.stagiaire.service and request.user.service and tache.stagiaire.service.id == request.user.service.id):
            messages.error(request, "Vous n'êtes pas l'encadrant de ce stagiaire.")
            return redirect('stagiaires_list')
    
    # Annuler la tâche
    if tache.annuler():
        messages.success(request, f"La tâche '{tache.titre}' a été annulée.")
    else:
        messages.error(request, f"Impossible d'annuler la tâche '{tache.titre}'.")
    
    return redirect('taches_stagiaire', stagiaire_id=tache.stagiaire.id)

@login_required
def add_tache_view(request, stagiaire_id):
    """Vue pour ajouter une tâche à un stagiaire"""
    stagiaire = get_object_or_404(Stagiaire, id=stagiaire_id)
    
    # Vérifier les permissions
    if not request.user.is_superuser and request.user != stagiaire.encadrant:
        messages.error(request, "Vous n'avez pas la permission d'ajouter des tâches à ce stagiaire.")
        return redirect('stagiaire_detail', stagiaire_id=stagiaire_id)
    
    if request.method == 'POST':
        form = TacheStageForm(request.POST)
        if form.is_valid():
            tache = form.save(commit=False)
            tache.stagiaire = stagiaire
            tache.creee_par = request.user
            tache.save()
            messages.success(request, "La tâche a été ajoutée avec succès.")
            return redirect('stagiaire_detail', stagiaire_id=stagiaire_id)
    else:
        form = TacheStageForm()
    
    context = {
        'form': form,
        'stagiaire': stagiaire,
        'title': f"Ajouter une tâche pour {stagiaire.prenom} {stagiaire.nom}"
    }
    return render(request, 'stagiaires/add_tache.html', context)


@login_required
def rencontre_stagiaire_view(request, stagiaire_id):
    """Vue pour la rencontre avec un stagiaire - ajout de tâches et envoi par email"""
    try:
        stagiaire = Stagiaire.objects.get(id=stagiaire_id)
    except Stagiaire.DoesNotExist:
        messages.error(request, "Ce stagiaire n'existe pas.")
        return redirect('stagiaires_list')

    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH', 'ENCADRANT']:
        messages.error(request, "Vous n'avez pas les permissions nécessaires.")
        return redirect('dashboard')

    # Si l'utilisateur est un encadrant, vérifier qu'il peut accéder à ce stagiaire
    if request.user.role == 'ENCADRANT':
        # Vérifier si c'est son stagiaire ou un stagiaire de son service
        if not (stagiaire.encadrant == request.user or
                (stagiaire.service and request.user.service and
                 stagiaire.service.id == request.user.service.id)):
            messages.error(request, "Vous n'avez pas accès à ce stagiaire.")
            return redirect('stagiaires_list')

    # Récupérer les tâches du stagiaire
    taches = Tache.objects.filter(stagiaire=stagiaire).order_by('-date_creation')

    if request.method == 'POST':
        if 'add_tache' in request.POST:
            # Ajouter une nouvelle tâche
            form = TacheForm(request.POST)
            if form.is_valid():
                tache = form.save(commit=False)
                tache.stagiaire = stagiaire
                tache.creee_par = request.user
                tache.save()
                messages.success(request, "La tâche a été ajoutée avec succès.")
                return redirect('rencontre_stagiaire', stagiaire_id=stagiaire.id)
        elif 'send_email' in request.POST:
            # Envoyer les tâches par email
            try:
                # Préparer le contenu de l'email
                taches_list = Tache.objects.filter(stagiaire=stagiaire).order_by('-date_creation')

                if taches_list.exists():
                    # Construire le message
                    message = f"Bonjour {stagiaire.prenom},\n\n"
                    message += "Voici les tâches qui vous ont été assignées lors de notre rencontre :\n\n"

                    for i, tache in enumerate(taches_list, 1):
                        message += f"{i}. {tache.titre}\n"
                        if tache.description:
                            message += f"   Description : {tache.description}\n"
                        if tache.date_fin_prevue:
                            message += f"   Date limite : {tache.date_fin_prevue.strftime('%d/%m/%Y')}\n"
                        message += f"   Priorité : {tache.get_priorite_display()}\n\n"

                    message += "Cordialement,\n"
                    message += f"{request.user.get_full_name() or request.user.username}\n"
                    message += "Encadrant de stage"

                    # Envoyer l'email
                    send_mail(
                        subject=f"Tâches assignées - Stage {stagiaire.nom_complet}",
                        message=message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[stagiaire.email],
                        fail_silently=False,
                    )

                    messages.success(request, f"Les tâches ont été envoyées par email à {stagiaire.email}")
                else:
                    messages.warning(request, "Aucune tâche à envoyer.")

            except Exception as e:
                messages.error(request, f"Erreur lors de l'envoi de l'email : {str(e)}")

            return redirect('rencontre_stagiaire', stagiaire_id=stagiaire.id)
    else:
        form = TacheForm()

    context = {
        'stagiaire': stagiaire,
        'taches': taches,
        'form': form,
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'title': f'Rencontre avec {stagiaire.nom_complet}'
    }
    return render(request, 'stagiaires/rencontre_stagiaire.html', context)


@login_required
def consulter_cv_view(request, stagiaire_id):
    """Vue pour consulter le CV d'un stagiaire"""
    try:
        stagiaire = Stagiaire.objects.get(id=stagiaire_id)
    except Stagiaire.DoesNotExist:
        messages.error(request, "Ce stagiaire n'existe pas.")
        return redirect('stagiaires_list')

    # Vérifier les permissions
    if request.user.role not in ['ADMIN', 'RH', 'ENCADRANT']:
        messages.error(request, "Vous n'avez pas les permissions nécessaires.")
        return redirect('dashboard')

    # Si l'utilisateur est un encadrant, vérifier qu'il peut accéder à ce stagiaire
    if request.user.role == 'ENCADRANT':
        # Vérifier si c'est son stagiaire ou un stagiaire de son service
        if not (stagiaire.encadrant == request.user or
                (stagiaire.service and request.user.service and
                 stagiaire.service.id == request.user.service.id)):
            messages.error(request, "Vous n'avez pas accès au CV de ce stagiaire.")
            return redirect('stagiaires_list')

    # Vérifier que le stagiaire a un CV
    if not stagiaire.cv:
        messages.warning(request, f"Aucun CV disponible pour {stagiaire.nom_complet}.")
        return redirect('stagiaire_detail', stagiaire_id=stagiaire.id)

    context = {
        'stagiaire': stagiaire,
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'title': f'CV de {stagiaire.nom_complet}'
    }
    return render(request, 'stagiaires/consulter_cv.html', context)


@login_required
def calendrier_encadrant_view(request):
    """Vue pour le calendrier de l'encadrant"""
    if request.user.role not in ['ADMIN', 'RH', 'ENCADRANT']:
        messages.error(request, "Vous n'avez pas les permissions nécessaires.")
        return redirect('dashboard')

    # Récupérer le mois et l'année depuis les paramètres GET
    from datetime import datetime, timedelta
    import calendar

    today = datetime.now()
    year = int(request.GET.get('year', today.year))
    month = int(request.GET.get('month', today.month))

    # Créer la date du premier jour du mois
    first_day = datetime(year, month, 1)

    # Calculer le mois précédent et suivant
    if month == 1:
        prev_month = 12
        prev_year = year - 1
    else:
        prev_month = month - 1
        prev_year = year

    if month == 12:
        next_month = 1
        next_year = year + 1
    else:
        next_month = month + 1
        next_year = year

    # Récupérer les stagiaires de l'encadrant
    if request.user.role == 'ENCADRANT':
        stagiaires = Stagiaire.objects.filter(
            service=request.user.service,
            statut='EN_COURS'
        ).select_related('encadrant', 'service')
    else:
        # Pour admin et RH, afficher tous les stagiaires
        stagiaires = Stagiaire.objects.filter(
            statut='EN_COURS'
        ).select_related('encadrant', 'service')

    # Récupérer les tâches du mois pour ces stagiaires
    start_date = first_day
    end_date = datetime(year, month, calendar.monthrange(year, month)[1])

    taches_mois = Tache.objects.filter(
        stagiaire__in=stagiaires,
        date_fin_prevue__range=[start_date.date(), end_date.date()]
    ).select_related('stagiaire', 'creee_par')

    # Organiser les tâches par date
    taches_par_date = {}
    for tache in taches_mois:
        date_str = tache.date_fin_prevue.strftime('%Y-%m-%d')
        if date_str not in taches_par_date:
            taches_par_date[date_str] = []
        taches_par_date[date_str].append(tache)

    # Ajouter quelques événements de démonstration pour le style
    demo_events = [
        {
            'titre': 'Long Event',
            'date': (today + timedelta(days=2)).strftime('%Y-%m-%d'),
            'priorite': 'HAUTE'
        },
        {
            'titre': 'Conference',
            'date': (today + timedelta(days=5)).strftime('%Y-%m-%d'),
            'priorite': 'MOYENNE'
        },
        {
            'titre': '4p Repeating Event',
            'date': (today + timedelta(days=7)).strftime('%Y-%m-%d'),
            'priorite': 'BASSE'
        },
        {
            'titre': 'Birthday Party',
            'date': (today + timedelta(days=10)).strftime('%Y-%m-%d'),
            'priorite': 'HAUTE'
        },
        {
            'titre': 'Click for Google',
            'date': (today + timedelta(days=15)).strftime('%Y-%m-%d'),
            'priorite': 'MOYENNE'
        },
    ]

    # Ajouter les événements de démonstration
    for event in demo_events:
        if event['date'] not in taches_par_date:
            taches_par_date[event['date']] = []
        # Créer un objet similaire à une tâche pour la démonstration
        class DemoEvent:
            def __init__(self, titre, priorite):
                self.titre = titre
                self.priorite = priorite
                self.statut = 'EN_COURS'
                self.stagiaire = type('obj', (object,), {'nom': 'Demo', 'prenom': 'User'})()

        taches_par_date[event['date']].append(DemoEvent(event['titre'], event['priorite']))

    # Générer le calendrier
    cal = calendar.monthcalendar(year, month)

    # Noms des mois en français
    mois_francais = [
        '', 'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ]

    context = {
        'year': year,
        'month': month,
        'month_name': mois_francais[month],
        'calendar': cal,
        'today': today,
        'prev_year': prev_year,
        'prev_month': prev_month,
        'next_year': next_year,
        'next_month': next_month,
        'stagiaires': stagiaires,
        'taches_par_date': taches_par_date,
        'user': request.user,
        'role_display': request.user.get_role_display() if hasattr(request.user, 'role') else 'Utilisateur',
        'title': f'Calendrier {mois_francais[month]} {year}'
    }

    return render(request, 'stagiaires/calendrier_encadrant.html', context)
