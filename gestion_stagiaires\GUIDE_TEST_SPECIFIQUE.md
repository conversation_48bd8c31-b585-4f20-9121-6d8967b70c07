# Guide de test spécifique pour votre système

## 🔐 Informations de connexion

**Administrateurs disponibles :**
- Username: `admin` (Email: <EMAIL>)
- Username: `salim@48` (Email: <EMAIL>)

## 📋 Test d'ajout de stagiaire - Étapes détaillées

### Étape 1: Connexion
1. Allez sur http://127.0.0.1:8000/admin/
2. Connectez-vous avec `admin` et votre mot de passe

### Étape 2: Accès au formulaire
1. Cliquez sur "Stagiaires"
2. Cliquez sur "Ajouter" (bouton en haut à droite)

### Étape 3: Remplir le formulaire avec ces données EXACTES

**⚠️ IMPORTANT: Utilisez un email unique à chaque test !**

```
📝 DONNÉES À SAISIR:
═══════════════════════════════════════════════════════════

Informations personnelles:
┌─────────────────────────────────────────────────────────┐
│ Nom: TestManuel2025                                     │
│ Prénom: Stagiaire                                       │
│ Email: <EMAIL>                   │
│        (remplacez HHMMSS par l'heure actuelle)          │
│ Téléphone: 0123456789                                   │
│ Date de naissance: 01/01/2000                           │
└─────────────────────────────────────────────────────────┘

Informations du stage:
┌─────────────────────────────────────────────────────────┐
│ Département: IT                                         │
│ Service: informatique (si disponible)                   │
│ Encadrant: aya souya (recommandé car a un service)      │
│ Date de début: (date d'aujourd'hui)                     │
│ Date de fin: (date dans 3 mois)                         │
│ Statut: En cours                                        │
└─────────────────────────────────────────────────────────┘

Informations académiques:
┌─────────────────────────────────────────────────────────┐
│ Établissement: Université Test Manuel                   │
│ Niveau d'étude: Master                                  │
│ Spécialité: Informatique                                │
└─────────────────────────────────────────────────────────┘

Thématique et sujet:
┌─────────────────────────────────────────────────────────┐
│ Thématique: (laisser vide)                              │
│ Sujet: (laisser vide)                                   │
│ Durée estimée: 0                                        │
└─────────────────────────────────────────────────────────┘

Technologies et compétences:
┌─────────────────────────────────────────────────────────┐
│ Technologies: (laisser vide)                            │
└─────────────────────────────────────────────────────────┘

TOUS LES AUTRES CHAMPS: LAISSER VIDES
```

### Étape 4: Vérifications avant de sauvegarder
- [ ] L'email est unique (contient l'heure actuelle)
- [ ] Tous les champs obligatoires sont remplis
- [ ] Un encadrant est sélectionné
- [ ] Les dates sont cohérentes (fin > début)

### Étape 5: Sauvegarder
1. Cliquez sur "Enregistrer" (en bas du formulaire)
2. **OBSERVEZ ATTENTIVEMENT ce qui se passe**

## 🔍 Que vérifier après avoir cliqué "Enregistrer"

### ✅ Si ça marche:
- Vous êtes redirigé vers la liste des stagiaires
- Un message de succès apparaît en vert
- Le nouveau stagiaire apparaît dans la liste

### ❌ Si ça ne marche pas:
1. **Erreurs en rouge** - Notez EXACTEMENT le texte
2. **Page qui se recharge** - Le formulaire revient avec vos données
3. **Aucun message** - Problème silencieux

## 🛠️ Diagnostic en cas de problème

### 1. Vérifier la console JavaScript
1. Appuyez sur **F12**
2. Allez dans l'onglet **Console**
3. Cherchez des erreurs en rouge
4. Notez le texte exact des erreurs

### 2. Vérifier les erreurs de formulaire
- Regardez s'il y a du texte en rouge près des champs
- Vérifiez que l'email est vraiment unique
- Assurez-vous que toutes les dates sont au bon format

### 3. Test avec données minimales
Si le test complet échoue, essayez avec SEULEMENT:
```
Nom: Test
Prénom: Simple
Email: <EMAIL>
Date de naissance: 01/01/2000
Département: IT
Date de début: (aujourd'hui)
Date de fin: (dans 1 mois)
Établissement: Test
Niveau d'étude: Master
Spécialité: Info
```

## 📊 Vérification finale

Après le test, vérifiez en base de données:

1. Allez dans le terminal
2. Tapez: `python manage.py shell`
3. Exécutez:
```python
from stagiaires.models import Stagiaire
stagiaires = Stagiaire.objects.filter(email__contains='test.manuel')
for s in stagiaires:
    print(f"{s.nom_complet} - {s.email} - Créé le: {s.date_creation}")
```

## 🎯 Résultats attendus

**Si tout fonctionne correctement:**
- Le stagiaire apparaît dans la liste de l'admin
- Il est trouvé en base de données
- Tous les champs sont correctement remplis

**Si il y a encore un problème:**
- Notez EXACTEMENT les erreurs affichées
- Vérifiez la console JavaScript
- Essayez avec un navigateur différent (Chrome, Firefox, Edge)
- Désactivez les extensions de navigateur

## 📞 Informations pour le support

Si le problème persiste, fournissez:
1. Le texte exact des erreurs
2. Les erreurs de la console JavaScript (F12)
3. Le navigateur utilisé
4. Les données exactes saisies dans le formulaire
