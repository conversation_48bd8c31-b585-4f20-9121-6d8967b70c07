{% extends 'stagiaires/base.html' %}

{% block title %}Validation Convention - {{ stagiaire.nom_complet }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        Validation de Convention de Stage
                    </h4>
                    <small>{{ stagiaire.nom_complet }} - {{ stagiaire.get_departement_display }}</small>
                </div>
                <div class="card-body">
                    <!-- Informations du stagiaire -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Informations du stagiaire</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Nom :</strong> {{ stagiaire.nom_complet }}</p>
                                    <p><strong>Email :</strong> {{ stagiaire.email }}</p>
                                    <p><strong>Établissement :</strong> {{ stagiaire.etablissement }}</p>
                                    <p><strong>Niveau :</strong> {{ stagiaire.niveau_etude }}</p>
                                    <p><strong>Spécialité :</strong> {{ stagiaire.specialite }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-briefcase me-2"></i>Détails du stage</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Département :</strong> {{ stagiaire.get_departement_display }}</p>
                                    <p><strong>Encadrant :</strong> {{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}</p>
                                    <p><strong>Période :</strong> {{ stagiaire.date_debut }} - {{ stagiaire.date_fin }}</p>
                                    <p><strong>Durée :</strong> {{ stagiaire.duree_stage }} jours</p>
                                    <p><strong>Statut :</strong> 
                                        <span class="badge bg-{% if stagiaire.statut == 'EN_COURS' %}primary{% elif stagiaire.statut == 'TERMINE' %}success{% else %}secondary{% endif %}">
                                            {{ stagiaire.get_statut_display }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Convention à valider -->
                    <div class="card border-warning mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-file-contract me-2"></i>Convention à valider</h6>
                        </div>
                        <div class="card-body">
                            {% if stagiaire.convention_stage %}
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="mb-1"><strong>Fichier :</strong> {{ stagiaire.convention_stage.name|default:"Convention de stage" }}</p>
                                        <p class="mb-1"><strong>Statut actuel :</strong> 
                                            <span class="badge bg-{% if stagiaire.statut_convention == 'VALIDEE' %}success{% elif stagiaire.statut_convention == 'REJETEE' %}danger{% elif stagiaire.statut_convention == 'MODIFIEE' %}warning{% else %}secondary{% endif %}">
                                                {{ stagiaire.get_statut_convention_display }}
                                            </span>
                                        </p>
                                        {% if stagiaire.date_validation_convention %}
                                            <p class="mb-1"><strong>Validée le :</strong> {{ stagiaire.date_validation_convention|date:"d/m/Y à H:i" }}</p>
                                            <p class="mb-1"><strong>Validée par :</strong> {{ stagiaire.validee_par.get_full_name }}</p>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <a href="{{ stagiaire.convention_stage.url }}" target="_blank" class="btn btn-primary">
                                            <i class="fas fa-eye me-1"></i>Voir la convention
                                        </a>
                                        <a href="{{ stagiaire.convention_stage.url }}" download class="btn btn-outline-primary ms-2">
                                            <i class="fas fa-download me-1"></i>Télécharger
                                        </a>
                                    </div>
                                </div>
                                
                                {% if stagiaire.commentaire_convention %}
                                <div class="mt-3">
                                    <strong>Commentaires précédents :</strong>
                                    <div class="alert alert-light">{{ stagiaire.commentaire_convention }}</div>
                                </div>
                                {% endif %}
                            {% else %}
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Aucune convention uploadée pour ce stagiaire.
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Section de signature électronique -->
                    {% if stagiaire.convention_stage and stagiaire.statut_convention == 'EN_ATTENTE' %}
                    <div class="card border-success mb-4">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-signature me-2"></i>Signature électronique RH</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <p class="mb-2"><strong>Responsable RH :</strong> {{ user.get_full_name }}</p>
                                    <p class="mb-2"><strong>Date de validation :</strong> <span id="currentDate"></span></p>
                                    <p class="mb-2"><strong>Statut :</strong> <span class="badge bg-warning">En cours de validation</span></p>

                                    <div class="alert alert-info mt-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Information :</strong> En validant cette convention, vous confirmez que :
                                        <ul class="mb-0 mt-2">
                                            <li>Tous les documents requis sont présents et conformes</li>
                                            <li>Les informations du stagiaire sont correctes</li>
                                            <li>Le stagiaire est officiellement accepté pour le stage</li>
                                            <li>La convention est signée électroniquement par vos soins</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="signature-preview p-3 border rounded bg-light">
                                        <i class="fas fa-user-tie fa-3x text-muted mb-2"></i>
                                        <p class="mb-1"><strong>{{ user.get_full_name }}</strong></p>
                                        <p class="small text-muted mb-1">Responsable RH</p>
                                        <p class="small text-muted">{{ user.email }}</p>
                                        <div class="signature-line mt-3 pt-2 border-top">
                                            <small class="text-muted">Signature électronique</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Formulaire de validation -->
                    {% if stagiaire.convention_stage %}
                    <form method="post" id="validationForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.statut_convention.id_for_label }}" class="form-label">
                                    <i class="fas fa-clipboard-check me-1"></i>Décision de validation
                                </label>
                                {{ form.statut_convention }}
                                {% if form.statut_convention.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.statut_convention.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.commentaire_convention.id_for_label }}" class="form-label">
                                <i class="fas fa-comment me-1"></i>Commentaires de validation
                            </label>
                            {{ form.commentaire_convention }}
                            {% if form.commentaire_convention.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.commentaire_convention.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Ajoutez vos commentaires sur la validation (obligatoire en cas de rejet ou modification demandée).
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <a href="{% url 'conventions_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Retour à la liste
                            </a>

                            <div class="d-flex gap-2">
                                {% if stagiaire.statut_convention == 'EN_ATTENTE' %}
                                    <button type="submit" class="btn btn-success btn-lg" id="validateBtn">
                                        <i class="fas fa-signature me-2"></i>Signer et Valider la Convention
                                    </button>
                                {% else %}
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-edit me-1"></i>Modifier la décision
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card-header h6 {
    margin-bottom: 0;
}
.card-body p {
    margin-bottom: 8px;
}

/* Signature preview styling */
.signature-preview {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #6c757d;
    transition: all 0.3s ease;
}

.signature-preview:hover {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.signature-line {
    border-top: 2px solid #6c757d !important;
    position: relative;
}

.signature-line::before {
    content: "✓";
    position: absolute;
    right: 10px;
    top: -15px;
    color: #28a745;
    font-weight: bold;
    font-size: 18px;
}

/* Animation pour le bouton de validation */
#validateBtn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

#validateBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

#validateBtn.signing {
    background: linear-gradient(45deg, #28a745, #20c997);
    animation: signing 2s infinite;
}

@keyframes signing {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Alerte de confirmation */
.confirmation-alert {
    border-left: 4px solid #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

/* Style pour les statuts */
.status-badge {
    font-size: 0.9em;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Afficher la date actuelle
    const currentDateElement = document.getElementById('currentDate');
    if (currentDateElement) {
        const now = new Date();
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        currentDateElement.textContent = now.toLocaleDateString('fr-FR', options);
    }

    // Gestion du formulaire de validation
    const form = document.getElementById('validationForm');
    const validateBtn = document.getElementById('validateBtn');
    const statutSelect = document.getElementById('{{ form.statut_convention.id_for_label }}');

    if (form && validateBtn) {
        form.addEventListener('submit', function(e) {
            const statut = statutSelect.value;
            const stagiaireName = '{{ stagiaire.nom_complet }}';

            let confirmMessage = '';
            let buttonText = '';

            switch(statut) {
                case 'VALIDEE':
                    confirmMessage = `🎉 CONFIRMATION DE SIGNATURE ÉLECTRONIQUE\n\n` +
                                   `Vous êtes sur le point de VALIDER et SIGNER électroniquement la convention de stage de :\n` +
                                   `👤 ${stagiaireName}\n\n` +
                                   `Cette action aura pour effet :\n` +
                                   `✅ Acceptation officielle du stagiaire\n` +
                                   `✅ Signature électronique de la convention\n` +
                                   `✅ Autorisation de début de stage\n\n` +
                                   `Confirmez-vous cette validation ?`;
                    buttonText = '<i class="fas fa-spinner fa-spin me-2"></i>Signature en cours...';
                    break;
                case 'REJETEE':
                    confirmMessage = `❌ CONFIRMATION DE REJET\n\n` +
                                   `Vous êtes sur le point de REJETER la convention de :\n` +
                                   `👤 ${stagiaireName}\n\n` +
                                   `Le stagiaire ne sera PAS accepté pour le stage.\n` +
                                   `Confirmez-vous ce rejet ?`;
                    buttonText = '<i class="fas fa-spinner fa-spin me-2"></i>Rejet en cours...';
                    break;
                case 'MODIFIEE':
                    confirmMessage = `⚠️ DEMANDE DE MODIFICATION\n\n` +
                                   `Vous demandez une modification de la convention de :\n` +
                                   `👤 ${stagiaireName}\n\n` +
                                   `La convention sera renvoyée pour correction.\n` +
                                   `Confirmez-vous cette demande ?`;
                    buttonText = '<i class="fas fa-spinner fa-spin me-2"></i>Traitement en cours...';
                    break;
                default:
                    confirmMessage = `Veuillez sélectionner un statut de validation.`;
                    e.preventDefault();
                    return;
            }

            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return;
            }

            // Animation du bouton
            validateBtn.innerHTML = buttonText;
            validateBtn.disabled = true;
            validateBtn.classList.add('signing');

            // Afficher un message de progression
            if (statut === 'VALIDEE') {
                setTimeout(() => {
                    const progressDiv = document.createElement('div');
                    progressDiv.className = 'alert alert-success confirmation-alert mt-3';
                    progressDiv.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Signature électronique en cours...</strong><br>
                        <small>Validation et signature de la convention de ${stagiaireName}</small>
                    `;
                    form.appendChild(progressDiv);
                }, 500);
            }
        });

        // Mise à jour dynamique du bouton selon le statut sélectionné
        statutSelect.addEventListener('change', function() {
            const statut = this.value;
            const btn = validateBtn;

            switch(statut) {
                case 'VALIDEE':
                    btn.className = 'btn btn-success btn-lg';
                    btn.innerHTML = '<i class="fas fa-signature me-2"></i>Signer et Valider la Convention';
                    break;
                case 'REJETEE':
                    btn.className = 'btn btn-danger btn-lg';
                    btn.innerHTML = '<i class="fas fa-times me-2"></i>Rejeter la Convention';
                    break;
                case 'MODIFIEE':
                    btn.className = 'btn btn-warning btn-lg';
                    btn.innerHTML = '<i class="fas fa-edit me-2"></i>Demander une Modification';
                    break;
                default:
                    btn.className = 'btn btn-secondary btn-lg';
                    btn.innerHTML = '<i class="fas fa-question me-2"></i>Sélectionner une action';
            }
        });
    }
});
</script>
{% endblock %}
