#!/usr/bin/env python
"""
Test détaillé de l'interface d'administration pour l'ajout de stagiaires
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service
from datetime import date, timedelta
import re

User = get_user_model()

def test_admin_form_detailed():
    """Test détaillé du formulaire d'administration"""
    
    print("=== Test détaillé du formulaire d'administration ===")
    
    # Configuration
    client = Client()
    admin_user = User.objects.filter(is_superuser=True).first()
    
    if not admin_user:
        print("❌ Aucun administrateur trouvé")
        return False
    
    print(f"✅ Admin: {admin_user.username}")
    
    # Se connecter
    client.force_login(admin_user)
    
    # Récupérer un encadrant
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    if encadrant:
        print(f"✅ Encadrant: {encadrant.username}")
        encadrant_id = str(encadrant.id)
        service_id = str(encadrant.service.id) if encadrant.service else ''
    else:
        print("⚠️ Aucun encadrant trouvé")
        encadrant_id = ''
        service_id = ''
    
    # Email unique
    test_email = f"test.detailed.{date.today().strftime('%Y%m%d%H%M%S')}@example.com"
    
    print(f"\n📋 Test avec email: {test_email}")
    
    # Supprimer le stagiaire de test s'il existe
    Stagiaire.objects.filter(email=test_email).delete()
    
    # Étape 1: Récupérer la page d'ajout
    print("\n1️⃣ Récupération de la page d'ajout...")
    try:
        response = client.get('/admin/stagiaires/stagiaire/add/')
        if response.status_code != 200:
            print(f"❌ Erreur d'accès à la page: {response.status_code}")
            return False
        
        print("✅ Page d'ajout accessible")
        
        # Extraire le token CSRF
        content = response.content.decode('utf-8')
        csrf_match = re.search(r'name=[\'"]csrfmiddlewaretoken[\'"] value=[\'"]([^\'"]+)[\'"]', content)
        if csrf_match:
            csrf_token = csrf_match.group(1)
            print("✅ Token CSRF récupéré")
        else:
            print("❌ Token CSRF non trouvé")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    
    # Étape 2: Préparer les données du formulaire
    print("\n2️⃣ Préparation des données du formulaire...")
    
    form_data = {
        'csrfmiddlewaretoken': csrf_token,
        'nom': 'TestDetailed',
        'prenom': 'Stagiaire',
        'email': test_email,
        'telephone': '0123456789',
        'date_naissance': '2000-01-01',
        'departement': 'IT',
        'service': service_id,
        'encadrant': encadrant_id,
        'date_debut': date.today().strftime('%Y-%m-%d'),
        'date_fin': (date.today() + timedelta(days=90)).strftime('%Y-%m-%d'),
        'statut': 'EN_COURS',
        'etablissement': 'Université Test Detailed',
        'niveau_etude': 'Master',
        'specialite': 'Informatique',
        'technologies': '',
        'thematique': '',
        'sujet': '',
        'duree_estimee': '0',
        'description_taches': '',
        'statut_taches': 'NON_COMMENCEES',
        'statut_convention': 'EN_ATTENTE',
        'commentaire_convention': '',
        'evaluation_encadrant': '',
        'note_finale': '',
        'cv': '',
        'assurance': '',
        'convention_stage': '',
        'date_validation_convention': '',
        'validee_par': '',
        'attestation_fin_stage': '',
        'date_generation_attestation': '',
        'attestation_generee_par': '',
        # Inlines
        'taches-TOTAL_FORMS': '0',
        'taches-INITIAL_FORMS': '0',
        'taches-MIN_NUM_FORMS': '0',
        'taches-MAX_NUM_FORMS': '1000',
        'missions-TOTAL_FORMS': '0',
        'missions-INITIAL_FORMS': '0',
        'missions-MIN_NUM_FORMS': '0',
        'missions-MAX_NUM_FORMS': '1000',
        'rapports-TOTAL_FORMS': '0',
        'rapports-INITIAL_FORMS': '0',
        'rapports-MIN_NUM_FORMS': '0',
        'rapports-MAX_NUM_FORMS': '1000',
        '_save': 'Enregistrer',
    }
    
    print(f"✅ Données préparées ({len(form_data)} champs)")
    
    # Étape 3: Soumettre le formulaire
    print("\n3️⃣ Soumission du formulaire...")
    
    try:
        response = client.post('/admin/stagiaires/stagiaire/add/', data=form_data, follow=True)
        
        print(f"Status: {response.status_code}")
        print(f"URL finale: {response.wsgi_request.path}")
        
        # Analyser la réponse
        content = response.content.decode('utf-8')
        
        # Vérifier si on est redirigé vers la liste (succès)
        if '/admin/stagiaires/stagiaire/' in response.wsgi_request.path and 'add' not in response.wsgi_request.path:
            print("✅ Redirection vers la liste des stagiaires")
            
            # Vérifier en base de données
            stagiaire_cree = Stagiaire.objects.filter(email=test_email).first()
            if stagiaire_cree:
                print(f"✅ SUCCÈS! Stagiaire créé: {stagiaire_cree.nom_complet}")
                print(f"   ID: {stagiaire_cree.id}")
                print(f"   Email: {stagiaire_cree.email}")
                print(f"   Créé par: {stagiaire_cree.cree_par}")
                print(f"   Date création: {stagiaire_cree.date_creation}")
                
                # Nettoyer
                stagiaire_cree.delete()
                print("🧹 Stagiaire de test supprimé")
                return True
            else:
                print("❌ Stagiaire non trouvé en base malgré la redirection")
                return False
        
        # Vérifier s'il y a des erreurs
        elif 'errorlist' in content:
            print("❌ Erreurs dans le formulaire:")
            
            # Extraire toutes les erreurs
            errors = re.findall(r'<ul class="errorlist[^>]*">(.*?)</ul>', content, re.DOTALL)
            for i, error in enumerate(errors):
                clean_error = re.sub(r'<[^>]+>', '', error).strip()
                if clean_error:
                    print(f"   {i+1}. {clean_error}")
            
            # Extraire les champs avec erreurs
            field_errors = re.findall(r'<div class="form-row[^>]*errors[^>]*">.*?<label[^>]*for="[^"]*">([^<]+)</label>', content, re.DOTALL)
            if field_errors:
                print("   Champs avec erreurs:")
                for field in set(field_errors):  # Éviter les doublons
                    print(f"      • {field.strip()}")
            
            return False
        
        else:
            print("❓ Réponse inattendue")
            print(f"   Contenu (premiers 500 caractères): {content[:500]}")
            return False
    
    except Exception as e:
        print(f"❌ Erreur lors de la soumission: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("Démarrage du test détaillé...")
    
    success = test_admin_form_detailed()
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 TEST RÉUSSI! L'ajout de stagiaires fonctionne dans l'admin.")
    else:
        print("❌ TEST ÉCHOUÉ! Il y a encore un problème avec l'ajout de stagiaires.")
        print("\n💡 Actions recommandées:")
        print("   1. Vérifiez les erreurs spécifiques affichées ci-dessus")
        print("   2. Testez manuellement avec le guide GUIDE_TEST_ADMIN.md")
        print("   3. Vérifiez les logs du serveur Django")
        print("   4. Assurez-vous que tous les champs obligatoires sont corrects")
    
    print(f"{'='*50}")

if __name__ == '__main__':
    main()
