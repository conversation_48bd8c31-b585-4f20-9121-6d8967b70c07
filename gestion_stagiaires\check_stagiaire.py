#!/usr/bin/env python
"""
Script pour vérifier les informations d'un stagiaire spécifique
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import Stagiaire

def check_stagiaire(stagiaire_id):
    """Vérifier les informations d'un stagiaire"""
    
    print(f"🔍 VÉRIFICATION DU STAGIAIRE ID: {stagiaire_id}")
    print("=" * 50)
    
    try:
        stagiaire = Stagiaire.objects.get(id=stagiaire_id)
        
        print(f"✅ Stagiaire trouvé :")
        print(f"   • ID : {stagiaire.id}")
        print(f"   • Nom complet : {stagiaire.nom_complet}")
        print(f"   • Email : {stagiaire.email}")
        print(f"   • Service : {stagiaire.service.nom if stagiaire.service else 'Aucun'}")
        print(f"   • Encadrant : {stagiaire.encadrant.get_full_name() if stagiaire.encadrant else 'Aucun'}")
        print(f"   • Statut : {stagiaire.get_statut_display()}")
        
        # Vérifier le CV
        if stagiaire.cv:
            print(f"   • CV : ✅ Disponible")
            print(f"     - Fichier : {stagiaire.cv.name}")
            print(f"     - URL : {stagiaire.cv.url}")
            
            # Vérifier si le fichier existe physiquement
            try:
                cv_path = stagiaire.cv.path
                file_exists = os.path.exists(cv_path)
                print(f"     - Fichier existe : {'✅ Oui' if file_exists else '❌ Non'}")
                if file_exists:
                    file_size = os.path.getsize(cv_path)
                    print(f"     - Taille : {file_size} octets")
            except Exception as e:
                print(f"     - Erreur accès fichier : {e}")
        else:
            print(f"   • CV : ❌ Non disponible")
        
        # URLs de test
        print(f"\n🔗 URLs de test :")
        print(f"   • Détail : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/")
        print(f"   • Rencontre : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/rencontre/")
        if stagiaire.cv:
            print(f"   • CV : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/cv/")
        
        return stagiaire
        
    except Stagiaire.DoesNotExist:
        print(f"❌ Aucun stagiaire trouvé avec l'ID {stagiaire_id}")
        
        # Afficher les stagiaires disponibles
        stagiaires = Stagiaire.objects.all()[:10]
        if stagiaires:
            print(f"\n📋 Stagiaires disponibles (10 premiers) :")
            for s in stagiaires:
                cv_status = "✅" if s.cv else "❌"
                print(f"   • ID {s.id}: {s.nom_complet} - CV: {cv_status}")
        
        return None

if __name__ == '__main__':
    # Vérifier le stagiaire ID 2
    stagiaire_id = 2
    if len(sys.argv) > 1:
        try:
            stagiaire_id = int(sys.argv[1])
        except ValueError:
            print("❌ ID de stagiaire invalide. Utilisation de l'ID 2 par défaut.")
    
    check_stagiaire(stagiaire_id)
