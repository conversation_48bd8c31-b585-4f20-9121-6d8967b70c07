#!/usr/bin/env python
"""
Démonstration complète des fonctionnalités de rencontre avec consultation CV
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service, Tache
from datetime import date, timedelta

User = get_user_model()

def demo_complete_rencontre():
    """Démonstration complète des fonctionnalités de rencontre"""
    
    print("🎯 DÉMONSTRATION COMPLÈTE - RENCONTRE ENCADRANT-STAGIAIRE AVEC CV")
    print("=" * 75)
    
    # 1. Récupérer l'encadrant de test
    try:
        encadrant = User.objects.get(username='encadrant_test')
        print(f"👤 ENCADRANT CONNECTÉ")
        print(f"   • Nom : {encadrant.get_full_name()}")
        print(f"   • Username : {encadrant.username}")
        print(f"   • Email : {encadrant.email}")
        print(f"   • Service : {encadrant.service.nom}")
        print(f"   • Rôle : {encadrant.get_role_display()}")
    except User.DoesNotExist:
        print("❌ Encadrant de test non trouvé. Exécutez d'abord create_test_user.py")
        return
    
    # 2. Récupérer le stagiaire avec CV
    stagiaires_service = Stagiaire.objects.filter(service=encadrant.service, cv__isnull=False)
    
    if not stagiaires_service.exists():
        print("\n❌ Aucun stagiaire avec CV trouvé. Exécutez d'abord test_cv_functionality.py")
        return
    
    stagiaire = stagiaires_service.first()
    
    print(f"\n👩‍🎓 STAGIAIRE SÉLECTIONNÉ")
    print(f"   • Nom complet : {stagiaire.nom_complet}")
    print(f"   • Email : {stagiaire.email}")
    print(f"   • Service : {stagiaire.service.nom}")
    print(f"   • Encadrant : {stagiaire.encadrant.get_full_name() if stagiaire.encadrant else 'Non assigné'}")
    print(f"   • Établissement : {stagiaire.etablissement}")
    print(f"   • Spécialité : {stagiaire.specialite}")
    print(f"   • Période : {stagiaire.date_debut} → {stagiaire.date_fin}")
    print(f"   • CV disponible : {'✅ Oui' if stagiaire.cv else '❌ Non'}")
    
    if stagiaire.cv:
        print(f"   • Fichier CV : {stagiaire.cv.name}")
        print(f"   • Taille : {os.path.getsize(stagiaire.cv.path)} octets")
    
    # 3. Afficher les tâches existantes
    taches_existantes = Tache.objects.filter(stagiaire=stagiaire)
    print(f"\n📝 TÂCHES EXISTANTES ({taches_existantes.count()})")
    
    if taches_existantes.exists():
        for i, tache in enumerate(taches_existantes, 1):
            priorite_icon = "🔴" if tache.priorite == "HAUTE" else "🟡" if tache.priorite == "NORMALE" else "🟢"
            statut_icon = "⏳" if tache.statut == "A_FAIRE" else "🔄" if tache.statut == "EN_COURS" else "✅"
            
            print(f"   {i}. {priorite_icon} {statut_icon} {tache.titre}")
            print(f"      📅 Échéance : {tache.date_fin_prevue.strftime('%d/%m/%Y') if tache.date_fin_prevue else 'Non définie'}")
            print(f"      👤 Créée par : {tache.creee_par.get_full_name() if tache.creee_par else 'Inconnu'}")
            print(f"      📝 {tache.description[:50]}..." if tache.description else "      📝 Pas de description")
            print()
    else:
        print("   Aucune tâche assignée pour le moment.")
    
    # 4. Fonctionnalités disponibles dans la page de rencontre
    print(f"🛠️ FONCTIONNALITÉS DISPONIBLES DANS LA PAGE DE RENCONTRE")
    print(f"   ✅ Consultation des informations du stagiaire")
    print(f"   ✅ Consultation du CV (aperçu + lien vers page complète)")
    print(f"   ✅ Ajout de nouvelles tâches")
    print(f"   ✅ Visualisation des tâches existantes")
    print(f"   ✅ Gestion du statut des tâches (démarrer/terminer)")
    print(f"   ✅ Envoi des tâches par email au stagiaire")
    
    # 5. Simulation d'une rencontre
    print(f"\n🤝 SIMULATION D'UNE RENCONTRE")
    print(f"   📅 Date : {date.today().strftime('%d/%m/%Y')}")
    print(f"   👤 Participants : {encadrant.get_full_name()} (Encadrant) + {stagiaire.nom_complet} (Stagiaire)")
    print(f"   📍 Lieu : Bureau de l'encadrant - Service {encadrant.service.nom}")
    
    print(f"\n   📋 ORDRE DU JOUR :")
    print(f"   1. 📄 Consultation du CV du stagiaire")
    print(f"   2. 📝 Révision des tâches en cours")
    print(f"   3. ➕ Attribution de nouvelles tâches")
    print(f"   4. 📧 Envoi du récapitulatif par email")
    
    # 6. Exemple de nouvelles tâches à ajouter
    nouvelles_taches = [
        {
            'titre': 'Formation aux outils internes',
            'description': 'Se former aux outils et logiciels utilisés par l\'équipe',
            'priorite': 'HAUTE',
            'echeance': 3
        },
        {
            'titre': 'Participation aux réunions d\'équipe',
            'description': 'Assister aux réunions hebdomadaires et prendre des notes',
            'priorite': 'NORMALE',
            'echeance': 7
        },
        {
            'titre': 'Rédaction du rapport de stage',
            'description': 'Commencer la rédaction du rapport de stage avec plan détaillé',
            'priorite': 'BASSE',
            'echeance': 30
        }
    ]
    
    print(f"\n   ➕ NOUVELLES TÂCHES À ASSIGNER :")
    for i, tache in enumerate(nouvelles_taches, 1):
        priorite_icon = "🔴" if tache['priorite'] == "HAUTE" else "🟡" if tache['priorite'] == "NORMALE" else "🟢"
        echeance_date = (date.today() + timedelta(days=tache['echeance'])).strftime('%d/%m/%Y')
        
        print(f"   {i}. {priorite_icon} {tache['titre']}")
        print(f"      📅 Échéance : {echeance_date}")
        print(f"      📝 {tache['description']}")
        print()
    
    # 7. URLs pour tester
    print(f"🔗 URLS POUR TESTER LES FONCTIONNALITÉS")
    print(f"   🔐 Connexion :")
    print(f"      URL : http://127.0.0.1:8000/login/")
    print(f"      Username : {encadrant.username}")
    print(f"      Password : test123")
    print()
    print(f"   🤝 Page de rencontre :")
    print(f"      URL : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/rencontre/")
    print(f"      Fonctionnalités : Infos + CV + Tâches + Email")
    print()
    print(f"   📄 Consultation CV :")
    print(f"      URL : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/cv/")
    print(f"      Fonctionnalités : Visualisation PDF + Téléchargement")
    print()
    print(f"   👤 Détail du stagiaire :")
    print(f"      URL : http://127.0.0.1:8000/stagiaires/{stagiaire.id}/")
    print(f"      Fonctionnalités : Vue complète du profil")
    print()
    print(f"   📋 Liste des stagiaires :")
    print(f"      URL : http://127.0.0.1:8000/stagiaires/")
    print(f"      Fonctionnalités : Bouton 'Rencontre' pour chaque stagiaire")
    
    # 8. Avantages de la nouvelle fonctionnalité
    print(f"\n🎯 AVANTAGES DE LA FONCTIONNALITÉ RENCONTRE + CV")
    print(f"   ✅ Interface centralisée pour les rencontres")
    print(f"   ✅ Consultation rapide du CV pendant la rencontre")
    print(f"   ✅ Gestion efficace des tâches en temps réel")
    print(f"   ✅ Communication automatisée par email")
    print(f"   ✅ Suivi personnalisé par service")
    print(f"   ✅ Permissions sécurisées par rôle")
    
    print(f"\n✅ DÉMONSTRATION TERMINÉE AVEC SUCCÈS !")
    print(f"🚀 Toutes les fonctionnalités sont opérationnelles et prêtes à l'utilisation.")
    
    return {
        'encadrant': encadrant,
        'stagiaire': stagiaire,
        'taches_count': taches_existantes.count(),
        'cv_disponible': bool(stagiaire.cv)
    }

if __name__ == '__main__':
    try:
        result = demo_complete_rencontre()
        if result:
            print(f"\n📊 STATISTIQUES FINALES :")
            print(f"   • Encadrant : {result['encadrant'].get_full_name()}")
            print(f"   • Stagiaire : {result['stagiaire'].nom_complet}")
            print(f"   • Tâches existantes : {result['taches_count']}")
            print(f"   • CV disponible : {'✅ Oui' if result['cv_disponible'] else '❌ Non'}")
        
    except Exception as e:
        print(f"❌ Erreur lors de la démonstration : {e}")
        import traceback
        traceback.print_exc()
