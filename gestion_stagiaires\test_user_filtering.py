#!/usr/bin/env python
"""
Script de test pour vérifier le filtrage des utilisateurs
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser
from django.db.models import Q

def test_user_filtering():
    """Tester le filtrage des utilisateurs"""
    print("🧪 TEST DU FILTRAGE DES UTILISATEURS")
    print("=" * 60)
    
    # Récupérer l'admin principal
    admin_user = CustomUser.objects.filter(role='ADMIN').first()
    if not admin_user:
        print("❌ Aucun administrateur trouvé")
        return False
    
    print(f"👑 Admin connecté : {admin_user.get_full_name()} ({admin_user.username})")
    print()
    
    # Afficher tous les utilisateurs
    print("📋 TOUS LES UTILISATEURS :")
    all_users = CustomUser.objects.all().order_by('id')
    for user in all_users:
        creator = user.cree_par.get_full_name() if user.cree_par else "Aucun (ancien)"
        print(f"   {user.id:2d}. {user.get_full_name():20} ({user.username:15}) - {user.role:10} - Créé par: {creator}")
    
    print()
    
    # Tester le filtrage comme dans la vue
    print("🔍 FILTRAGE SELON LA LOGIQUE DE LA VUE :")
    users_query = CustomUser.objects.filter(
        Q(cree_par=admin_user) |  # Utilisateurs créés par l'admin
        Q(cree_par__isnull=True)   # Utilisateurs sans créateur (anciens)
    ).distinct().order_by('date_creation', 'id')
    
    print(f"   Utilisateurs visibles pour {admin_user.get_full_name()} :")
    for user in users_query:
        creator = user.cree_par.get_full_name() if user.cree_par else "Aucun (ancien)"
        status = "🟢 Actif" if user.is_active else "🔴 Inactif"
        role_icon = {"ADMIN": "👑", "RH": "📋", "ENCADRANT": "👨‍🏫"}.get(user.role, "👤")
        print(f"   {role_icon} {user.get_full_name():20} ({user.username:15}) - {status} - Créé par: {creator}")
    
    print()
    
    # Statistiques
    users_crees_count = CustomUser.objects.filter(cree_par=admin_user).count()
    users_anciens_count = CustomUser.objects.filter(cree_par__isnull=True).count()
    total_visible = users_query.count()
    total_all = all_users.count()
    
    print("📊 STATISTIQUES :")
    print(f"   • Utilisateurs créés par {admin_user.get_full_name()} : {users_crees_count}")
    print(f"   • Utilisateurs anciens (sans créateur) : {users_anciens_count}")
    print(f"   • Total visible dans l'interface : {total_visible}")
    print(f"   • Total dans la base de données : {total_all}")
    
    print()
    
    # Vérification
    expected_visible = users_crees_count + users_anciens_count
    if total_visible == expected_visible:
        print("✅ FILTRAGE CORRECT : Tous les utilisateurs attendus sont visibles")
        print(f"   → L'admin voit ses créations + les anciens utilisateurs")
        return True
    else:
        print("❌ PROBLÈME DE FILTRAGE")
        print(f"   → Attendu : {expected_visible}, Obtenu : {total_visible}")
        return False

def test_user_creation_tracking():
    """Tester le suivi de création d'utilisateurs"""
    print("\n" + "=" * 60)
    print("🔧 TEST DU SUIVI DE CRÉATION")
    print("=" * 60)
    
    # Créer un utilisateur de test
    admin_user = CustomUser.objects.filter(role='ADMIN').first()
    
    test_user = CustomUser.objects.create(
        username='test_tracking',
        email='<EMAIL>',
        first_name='Test',
        last_name='Tracking',
        role='ENCADRANT',
        cree_par=admin_user
    )
    test_user.set_password('test123')
    test_user.save()
    
    print(f"✅ Utilisateur de test créé : {test_user.get_full_name()}")
    print(f"   → Créé par : {test_user.cree_par.get_full_name()}")
    print(f"   → Date de création : {test_user.date_creation}")
    
    # Vérifier qu'il apparaît dans le filtrage
    users_query = CustomUser.objects.filter(
        Q(cree_par=admin_user) |
        Q(cree_par__isnull=True)
    ).distinct()
    
    if test_user in users_query:
        print("✅ L'utilisateur de test apparaît dans le filtrage")
    else:
        print("❌ L'utilisateur de test n'apparaît PAS dans le filtrage")
    
    # Nettoyer
    test_user.delete()
    print("🧹 Utilisateur de test supprimé")
    
    return True

def main():
    """Fonction principale"""
    print("🎯 VÉRIFICATION DU FILTRAGE DES UTILISATEURS")
    print("🔍 Test de la logique : afficher seulement les utilisateurs créés + anciens")
    print("=" * 70)
    
    try:
        success1 = test_user_filtering()
        success2 = test_user_creation_tracking()
        
        print("\n" + "=" * 60)
        if success1 and success2:
            print("🎉 TOUS LES TESTS RÉUSSIS !")
            print("✅ Le filtrage des utilisateurs fonctionne correctement")
            print("✅ Vous devriez voir tous vos utilisateurs dans l'interface")
        else:
            print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
            print("⚠️ Vérifiez la configuration")
        
        print("\n📋 INSTRUCTIONS :")
        print("1. Connectez-vous à l'interface : http://127.0.0.1:8000/users/")
        print("2. Utilisez un compte admin")
        print("3. Vous devriez voir tous vos utilisateurs (RH, encadrants, etc.)")
        
        return success1 and success2
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests : {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
