#!/usr/bin/env python
"""
Script de test pour vérifier que la suppression fonctionne dans l'interface personnalisée
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser, Stagiaire, Mission, TacheStage
from datetime import date

def test_interface_user_deletion():
    """Tester la suppression d'utilisateur via l'interface personnalisée"""
    print("🧪 TEST DE SUPPRESSION D'UTILISATEUR - INTERFACE PERSONNALISÉE")
    print("=" * 70)
    
    # Créer un utilisateur de test
    test_user = CustomUser.objects.create(
        username='test_interface_deletion',
        email='<EMAIL>',
        first_name='Test',
        last_name='Interface',
        role='ENCADRANT'
    )
    test_user.set_password('test123')
    test_user.save()
    print(f"✅ Utilisateur créé : {test_user.get_full_name()}")
    
    # Créer un stagiaire encadré par cet utilisateur
    stagiaire = Stagiaire.objects.create(
        nom='TestInterface',
        prenom='Stagiaire',
        email='<EMAIL>',
        date_naissance=date(2000, 1, 1),
        telephone='0123456789',
        departement='Informatique',
        encadrant=test_user,
        date_debut=date.today(),
        date_fin=date.today(),
        etablissement='Test University',
        niveau_etude='Master',
        specialite='Test',
        cree_par=test_user
    )
    print(f"✅ Stagiaire créé : {stagiaire.nom_complet}")
    
    # Créer une mission
    mission = Mission.objects.create(
        stagiaire=stagiaire,
        titre='Mission interface test',
        description='Description de test',
        objectifs='Objectifs de test',
        livrables_attendus='Livrables de test',
        date_debut_prevue=date.today(),
        date_fin_prevue=date.today(),
        creee_par=test_user
    )
    print(f"✅ Mission créée : {mission.titre}")
    
    # Créer une tâche
    tache = TacheStage.objects.create(
        stagiaire=stagiaire,
        titre='Tâche interface test',
        description='Description de test',
        date_debut_prevue=date.today(),
        date_fin_prevue=date.today(),
        creee_par=test_user
    )
    print(f"✅ Tâche créée : {tache.titre}")
    
    print(f"\n🔍 Dépendances avant suppression :")
    print(f"   • Stagiaires créés : {test_user.stagiaires_crees.count()}")
    print(f"   • Stagiaires encadrés : {Stagiaire.objects.filter(encadrant=test_user).count()}")
    print(f"   • Missions créées : {Mission.objects.filter(creee_par=test_user).count()}")
    print(f"   • Tâches créées : {TacheStage.objects.filter(creee_par=test_user).count()}")
    
    # Simuler la suppression (comme le ferait la vue)
    print(f"\n🗑️ Simulation de la suppression via l'interface...")
    
    try:
        user_id = test_user.id
        stagiaire_id = stagiaire.id
        mission_id = mission.id
        tache_id = tache.id
        
        # Supprimer l'utilisateur
        test_user.delete()
        print("✅ Utilisateur supprimé avec succès")
        
        # Vérifier l'état des objets liés
        print("\n🔍 État des objets après suppression :")
        
        # Le stagiaire devrait exister avec encadrant=None et cree_par=None
        try:
            stagiaire_updated = Stagiaire.objects.get(id=stagiaire_id)
            print(f"   ✅ Stagiaire conservé : {stagiaire_updated.nom_complet}")
            print(f"      • Encadrant : {stagiaire_updated.encadrant}")
            print(f"      • Créé par : {stagiaire_updated.cree_par}")
        except Stagiaire.DoesNotExist:
            print("   ❌ ERREUR : Stagiaire supprimé (ne devrait pas)")
        
        # La mission devrait exister avec creee_par=None
        try:
            mission_updated = Mission.objects.get(id=mission_id)
            print(f"   ✅ Mission conservée : {mission_updated.titre}")
            print(f"      • Créée par : {mission_updated.creee_par}")
        except Mission.DoesNotExist:
            print("   ❌ ERREUR : Mission supprimée (ne devrait pas)")
        
        # La tâche devrait exister avec creee_par=None
        try:
            tache_updated = TacheStage.objects.get(id=tache_id)
            print(f"   ✅ Tâche conservée : {tache_updated.titre}")
            print(f"      • Créée par : {tache_updated.creee_par}")
        except TacheStage.DoesNotExist:
            print("   ❌ ERREUR : Tâche supprimée (ne devrait pas)")
        
        # Nettoyer
        print("\n🧹 Nettoyage...")
        TacheStage.objects.filter(id=tache_id).delete()
        Mission.objects.filter(id=mission_id).delete()
        Stagiaire.objects.filter(id=stagiaire_id).delete()
        print("✅ Objets de test nettoyés")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR : {e}")
        return False

def test_interface_stagiaire_deletion():
    """Tester la suppression de stagiaire via l'interface personnalisée"""
    print("\n" + "=" * 70)
    print("🧪 TEST DE SUPPRESSION DE STAGIAIRE - INTERFACE PERSONNALISÉE")
    print("=" * 70)
    
    # Créer un encadrant
    encadrant = CustomUser.objects.create(
        username='encadrant_interface_test',
        email='<EMAIL>',
        first_name='Encadrant',
        last_name='Interface',
        role='ENCADRANT'
    )
    encadrant.set_password('test123')
    encadrant.save()
    
    # Créer un stagiaire
    stagiaire = Stagiaire.objects.create(
        nom='StagiaireInterface',
        prenom='Test',
        email='<EMAIL>',
        date_naissance=date(2000, 1, 1),
        telephone='0123456789',
        departement='Informatique',
        encadrant=encadrant,
        date_debut=date.today(),
        date_fin=date.today(),
        etablissement='Test University',
        niveau_etude='Master',
        specialite='Test',
        cree_par=encadrant
    )
    print(f"✅ Stagiaire créé : {stagiaire.nom_complet}")
    
    # Créer des objets liés
    mission = Mission.objects.create(
        stagiaire=stagiaire,
        titre='Mission stagiaire test',
        description='Description',
        objectifs='Objectifs',
        livrables_attendus='Livrables',
        date_debut_prevue=date.today(),
        date_fin_prevue=date.today(),
        creee_par=encadrant
    )
    
    tache = TacheStage.objects.create(
        stagiaire=stagiaire,
        titre='Tâche stagiaire test',
        description='Description',
        date_debut_prevue=date.today(),
        date_fin_prevue=date.today(),
        creee_par=encadrant
    )
    
    print(f"✅ Mission et tâche créées pour le stagiaire")
    
    print(f"\n🔍 Objets liés avant suppression :")
    print(f"   • Missions : {stagiaire.missions.count()}")
    print(f"   • Tâches : {stagiaire.taches.count()}")
    
    # Simuler la suppression du stagiaire
    print(f"\n🗑️ Simulation de la suppression du stagiaire...")
    
    try:
        stagiaire_id = stagiaire.id
        mission_id = mission.id
        tache_id = tache.id
        
        # Supprimer le stagiaire (CASCADE devrait supprimer missions et tâches)
        stagiaire.delete()
        print("✅ Stagiaire supprimé avec succès")
        
        # Vérifier que les objets liés ont été supprimés
        print("\n🔍 Vérification de la suppression en cascade :")
        
        try:
            Mission.objects.get(id=mission_id)
            print("   ❌ ERREUR : Mission encore présente")
        except Mission.DoesNotExist:
            print("   ✅ Mission supprimée en cascade")
        
        try:
            TacheStage.objects.get(id=tache_id)
            print("   ❌ ERREUR : Tâche encore présente")
        except TacheStage.DoesNotExist:
            print("   ✅ Tâche supprimée en cascade")
        
        # Nettoyer l'encadrant
        encadrant.delete()
        print("✅ Encadrant nettoyé")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR : {e}")
        return False

def test_interface_urls():
    """Tester que les URLs sont correctement configurées"""
    print("\n" + "=" * 70)
    print("🌐 TEST DES URLS DE L'INTERFACE")
    print("=" * 70)
    
    from django.urls import reverse
    
    try:
        # Tester les URLs de suppression
        urls_to_test = [
            ('user_management', 'Gestion des utilisateurs'),
            ('stagiaires_list', 'Liste des stagiaires'),
        ]
        
        for url_name, description in urls_to_test:
            try:
                url = reverse(url_name)
                print(f"   ✅ {description}: {url}")
            except Exception as e:
                print(f"   ❌ {description}: Erreur - {e}")
                return False
        
        # Tester les URLs avec paramètres (simulation)
        print(f"   ✅ Suppression utilisateur: /users/[ID]/delete/")
        print(f"   ✅ Suppression stagiaire: /stagiaires/[ID]/delete/")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification des URLs : {e}")
        return False

def main():
    """Fonction principale"""
    print("🔧 TEST COMPLET DE L'INTERFACE DE SUPPRESSION PERSONNALISÉE")
    print("🎯 Vérifier que la suppression fonctionne dans votre interface")
    print("=" * 80)
    
    try:
        # Tests
        test1 = test_interface_user_deletion()
        test2 = test_interface_stagiaire_deletion()
        test3 = test_interface_urls()
        
        print("\n" + "=" * 70)
        print("📊 RÉSUMÉ DES TESTS")
        print("=" * 70)
        
        tests = [
            ("Suppression utilisateur interface", test1),
            ("Suppression stagiaire interface", test2),
            ("URLs interface", test3)
        ]
        
        all_passed = True
        for test_name, result in tests:
            status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
            print(f"   {status}: {test_name}")
            if not result:
                all_passed = False
        
        print("\n" + "=" * 70)
        if all_passed:
            print("🎉 INTERFACE DE SUPPRESSION FONCTIONNELLE !")
            print("✅ Vous pouvez maintenant supprimer depuis votre interface")
            print("\n📋 COMMENT TESTER :")
            print("   1. Allez sur : http://127.0.0.1:8000/users/ (gestion utilisateurs)")
            print("   2. Cliquez sur le bouton rouge 🗑️ pour supprimer un utilisateur")
            print("   3. Allez sur : http://127.0.0.1:8000/stagiaires/ (liste stagiaires)")
            print("   4. Cliquez sur le bouton rouge 🗑️ pour supprimer un stagiaire")
            print("   5. Les suppressions devraient maintenant fonctionner !")
        else:
            print("❌ PROBLÈMES DÉTECTÉS")
            print("⚠️ Vérifiez les erreurs ci-dessus")
        
        return all_passed
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests : {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
