from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service
from datetime import datetime, timedelta
import random

User = get_user_model()

class Command(BaseCommand):
    help = 'Crée plus de stagiaires pour tester le calendrier'

    def handle(self, *args, **options):
        self.stdout.write('Création de stagiaires supplémentaires...')
        
        # Récupérer ou créer des services
        services_data = [
            {'nom': 'Informatique', 'description': 'Service informatique'},
            {'nom': 'Ressources Humaines', 'description': 'Service RH'},
            {'nom': 'Marketing', 'description': 'Service marketing'},
            {'nom': 'Finance', 'description': 'Service financier'},
            {'nom': 'Production', 'description': 'Service production'},
        ]
        
        services = []
        for i, service_data in enumerate(services_data):
            service, created = Service.objects.get_or_create(
                nom=service_data['nom'],
                defaults={
                    'description': service_data['description'],
                    'code_service': f'SRV{i+1:03d}'
                }
            )
            services.append(service)
        
        # Créer des encadrants pour chaque service
        encadrants = []
        for i, service in enumerate(services):
            encadrant, created = User.objects.get_or_create(
                username=f'encadrant_{service.nom.lower()}',
                defaults={
                    'email': f'encadrant.{service.nom.lower()}@test.com',
                    'first_name': f'Encadrant',
                    'last_name': service.nom,
                    'role': 'ENCADRANT',
                    'service': service
                }
            )
            if created:
                encadrant.set_password('test123')
                encadrant.save()
            encadrants.append(encadrant)
        
        # Créer des stagiaires avec des périodes variées
        stagiaires_data = [
            # Stagiaires en cours
            {'nom': 'Dupont', 'prenom': 'Marie', 'debut': -20, 'fin': 40},
            {'nom': 'Martin', 'prenom': 'Pierre', 'debut': -10, 'fin': 50},
            {'nom': 'Bernard', 'prenom': 'Sophie', 'debut': -5, 'fin': 25},
            {'nom': 'Durand', 'prenom': 'Lucas', 'debut': 0, 'fin': 60},
            {'nom': 'Moreau', 'prenom': 'Emma', 'debut': 5, 'fin': 35},
            {'nom': 'Petit', 'prenom': 'Thomas', 'debut': 10, 'fin': 70},
            {'nom': 'Robert', 'prenom': 'Léa', 'debut': -15, 'fin': 15},
            {'nom': 'Richard', 'prenom': 'Hugo', 'debut': 20, 'fin': 80},
            {'nom': 'Dubois', 'prenom': 'Chloé', 'debut': -30, 'fin': 30},
            {'nom': 'Roux', 'prenom': 'Nathan', 'debut': 15, 'fin': 45},
        ]
        
        today = datetime.now().date()
        
        for i, data in enumerate(stagiaires_data):
            service = services[i % len(services)]
            encadrant = encadrants[i % len(encadrants)]
            
            stagiaire, created = Stagiaire.objects.get_or_create(
                email=f"{data['prenom'].lower()}.{data['nom'].lower()}@test.com",
                defaults={
                    'nom': data['nom'],
                    'prenom': data['prenom'],
                    'telephone': f'0123456{i:03d}',
                    'date_naissance': today - timedelta(days=365*22),
                    'date_debut': today + timedelta(days=data['debut']),
                    'date_fin': today + timedelta(days=data['fin']),
                    'service': service,
                    'encadrant': encadrant,
                    'statut': 'EN_COURS'
                }
            )
            
            if created:
                self.stdout.write(f'Stagiaire créé: {stagiaire.nom_complet} ({service.nom})')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Stagiaires supplémentaires créés avec succès!\n'
                f'- {len(services)} services\n'
                f'- {len(encadrants)} encadrants\n'
                f'- {len(stagiaires_data)} nouveaux stagiaires\n'
                f'Calendrier disponible à: http://127.0.0.1:8000/calendrier/'
            )
        )
