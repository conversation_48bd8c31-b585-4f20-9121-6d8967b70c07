{% extends 'stagiaires/base.html' %}

{% block title %}Calendrier Simple - {{ month_name }} {{ year }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <!-- En-tête -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <h3 class="mb-0">📅 Calendrier des Stages</h3>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="btn-group">
                                <a href="?year={{ prev_year }}&month={{ prev_month }}" class="btn btn-outline-light">
                                    ← Précédent
                                </a>
                                <span class="btn btn-light">
                                    {{ month_name }} {{ year }}
                                </span>
                                <a href="?year={{ next_year }}&month={{ next_month }}" class="btn btn-outline-light">
                                    Suivant →
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{% url 'stagiaires_list' %}" class="btn btn-outline-light">
                                📋 Liste Stagiaires
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Calendrier type Gantt -->
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered mb-0">
                            <!-- En-tête avec les dates -->
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 200px;" class="text-center">Stagiaire</th>
                                    {% for semaine in semaines %}
                                        {% for jour in semaine %}
                                            {% if jour %}
                                            <th class="text-center p-2 {% if jour.is_weekend %}bg-secondary{% endif %}" style="min-width: 40px;">
                                                <div class="fw-bold">{{ jour.day }}</div>
                                                <small>{{ jour.date|date:"D" }}</small>
                                            </th>
                                            {% else %}
                                            <th class="bg-light"></th>
                                            {% endif %}
                                        {% endfor %}
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for data in stagiaires_data %}
                                <tr>
                                    <!-- Colonne du stagiaire -->
                                    <td class="align-middle p-3" style="background-color: {{ data.color }}15; border-left: 4px solid {{ data.color }};">
                                        <div class="fw-bold">{{ data.stagiaire.nom_complet }}</div>
                                        <small class="text-muted d-block">
                                            📅 {{ data.stagiaire.date_debut|date:"d/m" }} - {{ data.stagiaire.date_fin|date:"d/m/Y" }}
                                        </small>
                                        <small class="text-muted d-block">
                                            ⏱️ {{ data.duree_semaines }} semaines
                                        </small>
                                        {% if data.stagiaire.sujet %}
                                        <small class="text-primary d-block">
                                            💡 {{ data.stagiaire.sujet.titre|truncatechars:30 }}
                                        </small>
                                        {% endif %}
                                        {% if data.stagiaire.service %}
                                        <small class="text-success d-block">
                                            🏢 {{ data.stagiaire.service.nom }}
                                        </small>
                                        {% endif %}
                                    </td>
                                    
                                    <!-- Colonnes des jours -->
                                    {% for semaine in semaines %}
                                        {% for jour in semaine %}
                                        <td class="text-center p-1 {% if jour.is_weekend %}bg-light{% endif %} {% if jour.is_today %}bg-warning{% endif %}" style="height: 80px; width: 40px;">
                                            {% if jour %}
                                                <!-- Vérifier si ce jour fait partie de la période du stagiaire -->
                                                {% for periode in data.periodes %}
                                                    {% if periode.week == forloop.parentloop.counter0 and periode.day == forloop.counter0 %}
                                                        <div class="w-100 h-100 d-flex align-items-center justify-content-center" 
                                                             style="background-color: {{ data.color }}; color: white; border-radius: 3px; font-weight: bold; position: relative;">
                                                            
                                                            <!-- Indicateurs spéciaux -->
                                                            {% if periode.date == data.stagiaire.date_debut %}
                                                                <span style="position: absolute; top: 2px; left: 2px; font-size: 10px;">🟢</span>
                                                            {% endif %}
                                                            {% if periode.date == data.stagiaire.date_fin %}
                                                                <span style="position: absolute; top: 2px; right: 2px; font-size: 10px;">🔴</span>
                                                            {% endif %}
                                                            
                                                            {{ jour.day }}
                                                        </div>
                                                    {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                        </td>
                                        {% endfor %}
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Légende -->
            <div class="row mt-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">🎨 Légende</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <span class="me-2">🟢</span>
                                        <span>Début de stage</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <span class="me-2">🔴</span>
                                        <span>Fin de stage</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="me-2" style="width: 20px; height: 15px; background-color: #fff3cd;"></div>
                                        <span>Aujourd'hui</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="me-2" style="width: 20px; height: 15px; background-color: #f8f9fa;"></div>
                                        <span>Weekend</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">📊 Statistiques</h6>
                        </div>
                        <div class="card-body text-center">
                            <div class="row">
                                <div class="col-6">
                                    <div class="fw-bold text-primary fs-4">{{ stagiaires_data|length }}</div>
                                    <small class="text-muted">Stagiaires</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold text-success fs-4">{{ month_name }}</div>
                                    <small class="text-muted">Mois</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message si aucun stagiaire -->
            {% if not stagiaires_data %}
            <div class="card">
                <div class="card-body text-center py-5">
                    <h4 class="text-muted">📭 Aucun stage en cours</h4>
                    <p class="text-muted">
                        Aucun stagiaire n'a de stage prévu pour {{ month_name }} {{ year }}.
                    </p>
                    <a href="{% url 'add_stagiaire' %}" class="btn btn-primary">
                        ➕ Ajouter un Stagiaire
                    </a>
                </div>
            </div>
            {% endif %}

            <!-- Navigation rapide -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">🚀 Navigation Rapide</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Mois</h6>
                            <div class="btn-group-vertical w-100">
                                <a href="?year={{ year }}&month=1" class="btn btn-outline-primary btn-sm">Janvier</a>
                                <a href="?year={{ year }}&month=4" class="btn btn-outline-primary btn-sm">Avril</a>
                                <a href="?year={{ year }}&month=7" class="btn btn-outline-primary btn-sm">Juillet</a>
                                <a href="?year={{ year }}&month=10" class="btn btn-outline-primary btn-sm">Octobre</a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Années</h6>
                            <div class="btn-group-vertical w-100">
                                <a href="?year={{ year|add:-1 }}&month={{ month }}" class="btn btn-outline-secondary btn-sm">{{ year|add:-1 }}</a>
                                <a href="?year={{ year }}&month={{ month }}" class="btn btn-primary btn-sm">{{ year }} (actuel)</a>
                                <a href="?year={{ year|add:1 }}&month={{ month }}" class="btn btn-outline-secondary btn-sm">{{ year|add:1 }}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Styles pour le calendrier simple */
.table {
    font-size: 0.9rem;
}

.table td, .table th {
    vertical-align: middle;
    border: 1px solid #dee2e6;
}

.table-responsive {
    overflow-x: auto;
}

/* Cellules de stage */
.table td div[style*="background-color"] {
    border-radius: 3px;
    font-weight: bold;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.table td div[style*="background-color"]:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Responsive */
@media (max-width: 768px) {
    .table {
        font-size: 0.8rem;
    }
    
    .table td:first-child {
        min-width: 150px;
    }
    
    .table th, .table td {
        padding: 4px;
    }
}

@media (max-width: 576px) {
    .table td:first-child {
        min-width: 120px;
        font-size: 0.7rem;
    }
    
    .table th {
        min-width: 30px;
        font-size: 0.7rem;
    }
}
</style>
{% endblock %}
