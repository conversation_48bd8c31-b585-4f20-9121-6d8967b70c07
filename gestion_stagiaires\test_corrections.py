#!/usr/bin/env python
"""
Test des corrections pour l'ajout et l'édition de stagiaires
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service
from datetime import date, timedelta

User = get_user_model()

def test_corrections():
    """Test des corrections"""
    
    print("=== TEST DES CORRECTIONS ===")
    
    # 1. Préparation
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not admin:
        print("❌ Aucun admin trouvé")
        return
    
    print(f"✅ Admin: {admin.username}")
    print(f"✅ Encadrant: {encadrant.get_full_name() if encadrant else 'Aucun'}")
    
    client = Client()
    client.force_login(admin)
    
    # 2. Test du template d'édition
    print("\n📝 Test du template d'édition:")
    
    # Récupérer un stagiaire existant
    stagiaire_existant = Stagiaire.objects.first()
    if stagiaire_existant:
        print(f"   Stagiaire de test: {stagiaire_existant.nom_complet} (ID: {stagiaire_existant.id})")
        
        # Test d'accès à la page d'édition
        response = client.get(f'/stagiaires/{stagiaire_existant.id}/edit/')
        print(f"   Status page édition: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Template d'édition accessible")
            
            content = response.content.decode('utf-8')
            checks = [
                ('Modifier le Stagiaire', 'Titre de modification'),
                ('form', 'Formulaire présent'),
                ('Enregistrer les modifications', 'Bouton de sauvegarde'),
                (stagiaire_existant.nom, 'Nom du stagiaire'),
                (stagiaire_existant.prenom, 'Prénom du stagiaire'),
            ]
            
            for check, desc in checks:
                if check in content:
                    print(f"      ✅ {desc}")
                else:
                    print(f"      ❌ {desc} manquant")
        else:
            print(f"   ❌ Erreur d'accès: {response.status_code}")
    else:
        print("   ⚠️ Aucun stagiaire existant pour tester l'édition")
    
    # 3. Test d'ajout avec gestion d'erreurs améliorée
    print("\n➕ Test d'ajout avec gestion d'erreurs:")
    
    # Données de test
    test_data = {
        'nom': 'CORRECTION',
        'prenom': 'TEST',
        'email': '<EMAIL>',
        'telephone': '0123456789',
        'date_naissance': '2000-01-01',
        'departement': 'IT',
        'date_debut': date.today().strftime('%Y-%m-%d'),
        'date_fin': (date.today() + timedelta(days=60)).strftime('%Y-%m-%d'),
        'etablissement': 'Test Correction',
        'niveau_etude': 'Master',
        'specialite': 'Correction',
        'statut': 'EN_COURS',
    }
    
    if encadrant:
        test_data['encadrant'] = encadrant.id
    
    service = Service.objects.filter(actif=True).first()
    if service:
        test_data['service'] = service.id
    
    # Supprimer le stagiaire de test s'il existe
    Stagiaire.objects.filter(email='<EMAIL>').delete()
    
    print(f"   Données: {test_data}")
    
    # Test d'ajout
    count_avant = Stagiaire.objects.count()
    print(f"   Stagiaires avant: {count_avant}")
    
    response = client.post('/stagiaires/add/', test_data, follow=True)
    print(f"   Status: {response.status_code}")
    
    count_apres = Stagiaire.objects.count()
    print(f"   Stagiaires après: {count_apres}")
    
    # Vérification
    stagiaire_cree = Stagiaire.objects.filter(email='<EMAIL>').first()
    
    if stagiaire_cree:
        print(f"   ✅ SUCCÈS: Stagiaire créé avec ID {stagiaire_cree.id}")
        print(f"      Nom: {stagiaire_cree.nom_complet}")
        print(f"      Créé par: {stagiaire_cree.cree_par}")
        
        # Test d'édition du nouveau stagiaire
        print(f"\n✏️ Test d'édition du nouveau stagiaire:")
        
        response_edit = client.get(f'/stagiaires/{stagiaire_cree.id}/edit/')
        print(f"   Status édition: {response_edit.status_code}")
        
        if response_edit.status_code == 200:
            print("   ✅ Page d'édition accessible")
            
            # Test de modification
            edit_data = test_data.copy()
            edit_data['nom'] = 'CORRECTION_MODIFIE'
            edit_data['prenom'] = 'TEST_MODIFIE'
            
            response_edit_post = client.post(f'/stagiaires/{stagiaire_cree.id}/edit/', edit_data)
            print(f"   Status modification: {response_edit_post.status_code}")
            
            # Vérifier la modification
            stagiaire_cree.refresh_from_db()
            if stagiaire_cree.nom == 'CORRECTION_MODIFIE':
                print("   ✅ Modification réussie")
            else:
                print("   ❌ Modification échouée")
        else:
            print(f"   ❌ Erreur d'accès à l'édition: {response_edit.status_code}")
        
        # Nettoyer
        stagiaire_cree.delete()
        print("   🧹 Stagiaire de test supprimé")
        
    else:
        print("   ❌ ÉCHEC: Stagiaire non créé")
        
        # Analyser les erreurs
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            if 'error' in content.lower() or 'erreur' in content.lower():
                print("   ❌ Erreurs détectées")
                
                import re
                errors = re.findall(r'<div[^>]*alert-danger[^>]*>(.*?)</div>', content, re.DOTALL)
                for error in errors:
                    clean_error = re.sub(r'<[^>]+>', '', error).strip()
                    if clean_error:
                        print(f"      • {clean_error}")
    
    # 4. Test avec données invalides
    print("\n❌ Test avec données invalides:")
    
    invalid_data = {
        'nom': '',  # Nom vide
        'prenom': 'Test',
        'email': 'email_invalide',  # Email invalide
        'date_debut': 'date_invalide',  # Date invalide
    }
    
    response_invalid = client.post('/stagiaires/add/', invalid_data)
    print(f"   Status données invalides: {response_invalid.status_code}")
    
    if response_invalid.status_code == 200:
        print("   ✅ Formulaire retourné avec erreurs (correct)")
        
        content = response_invalid.content.decode('utf-8')
        if 'error' in content.lower() or 'erreur' in content.lower():
            print("   ✅ Messages d'erreur affichés")
        else:
            print("   ⚠️ Messages d'erreur non visibles")
    else:
        print(f"   ⚠️ Status inattendu: {response_invalid.status_code}")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DES CORRECTIONS:")
    print("✅ Template d'édition créé (edit_stagiaire.html)")
    print("✅ Gestion d'erreurs améliorée dans la vue d'ajout")
    print("✅ Messages de debug ajoutés")
    print("✅ Validation des données renforcée")
    print("✅ Try/catch pour capturer les erreurs")
    print("")
    print("🎯 PROCHAINES ÉTAPES:")
    print("1. Testez l'ajout d'un stagiaire via l'interface web")
    print("2. Vérifiez les messages dans la console du serveur")
    print("3. Testez l'édition d'un stagiaire existant")
    print("4. Vérifiez que les erreurs sont bien affichées")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_corrections()
