#!/usr/bin/env python
"""
Script de test pour simuler l'ajout d'un stagiaire via l'interface d'administration Django
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire, Service
from stagiaires.admin import StagiaireAdmin
from django.contrib import admin
from django.test import RequestFactory
from django.http import QueryDict
from datetime import date, timedelta

User = get_user_model()

def test_admin_stagiaire_form():
    """Test de l'ajout d'un stagiaire via l'interface d'administration"""
    
    print("=== Test de l'interface d'administration pour les stagiaires ===")
    
    # Récupérer un admin et un encadrant
    admin_user = User.objects.filter(is_superuser=True).first()
    encadrant = User.objects.filter(role='ENCADRANT').first()
    
    if not admin_user:
        print("❌ Aucun administrateur trouvé")
        return
    
    if not encadrant:
        print("❌ Aucun encadrant trouvé")
        return
    
    print(f"✅ Admin: {admin_user.username}")
    print(f"✅ Encadrant: {encadrant.username}")
    
    # Créer une instance de StagiaireAdmin
    stagiaire_admin = StagiaireAdmin(Stagiaire, admin.site)
    
    # Simuler une requête POST pour ajouter un stagiaire
    factory = RequestFactory()
    
    # Données du formulaire (comme envoyées par l'interface d'administration)
    form_data = {
        'nom': 'TestAdmin',
        'prenom': 'Stagiaire',
        'email': '<EMAIL>',
        'telephone': '0123456789',
        'date_naissance': '2000-01-01',
        'departement': 'IT',
        'service': encadrant.service.id if encadrant.service else '',
        'encadrant': encadrant.id,
        'date_debut': date.today().strftime('%Y-%m-%d'),
        'date_fin': (date.today() + timedelta(days=90)).strftime('%Y-%m-%d'),
        'statut': 'EN_COURS',
        'etablissement': 'Université Test Admin',
        'niveau_etude': 'Master 2',
        'specialite': 'Informatique',
        'technologies': 'Python, Django, JavaScript',
        'thematique': '',
        'sujet': '',
        'duree_estimee': '90',
        'description_taches': 'Tâches de test',
        'statut_taches': 'NON_COMMENCEES',
        'statut_convention': 'EN_ATTENTE',
        'commentaire_convention': '',
        # Champs de métadonnées (seront remplis automatiquement)
        'cree_par': admin_user.id,
    }
    
    # Créer la requête POST
    request = factory.post('/admin/stagiaires/stagiaire/add/', data=form_data)
    request.user = admin_user
    
    print(f"\n📝 Test du formulaire d'administration:")
    
    # Tester la récupération du formulaire
    try:
        form_class = stagiaire_admin.get_form(request)
        print(f"   ✅ Classe de formulaire récupérée: {form_class.__name__}")
        
        # Créer une instance du formulaire avec les données
        form = form_class(data=form_data)
        
        print(f"   📋 Validation du formulaire:")
        if form.is_valid():
            print(f"   ✅ Formulaire valide")
            
            # Tester la sauvegarde
            try:
                # Supprimer le stagiaire de test s'il existe
                Stagiaire.objects.filter(email='<EMAIL>').delete()
                
                # Créer le stagiaire via le formulaire
                stagiaire = form.save(commit=False)
                
                # Simuler la méthode save_model de l'admin
                stagiaire_admin.save_model(request, stagiaire, form, change=False)
                
                print(f"   ✅ Stagiaire créé via l'admin: {stagiaire.nom_complet}")
                print(f"      ID: {stagiaire.id}")
                print(f"      Email: {stagiaire.email}")
                print(f"      Créé par: {stagiaire.cree_par}")
                print(f"      Service: {stagiaire.service}")
                print(f"      Encadrant: {stagiaire.encadrant}")
                
                # Nettoyer
                stagiaire.delete()
                print(f"   🧹 Stagiaire de test supprimé")
                
            except Exception as e:
                print(f"   ❌ Erreur lors de la sauvegarde: {e}")
                print(f"      Type d'erreur: {type(e).__name__}")
                import traceback
                traceback.print_exc()
        else:
            print(f"   ❌ Formulaire invalide:")
            for field, errors in form.errors.items():
                print(f"      • {field}: {errors}")
    
    except Exception as e:
        print(f"   ❌ Erreur lors de la récupération du formulaire: {e}")
        import traceback
        traceback.print_exc()
    
    # Test des fieldsets
    print(f"\n🎨 Test des fieldsets:")
    try:
        fieldsets = stagiaire_admin.get_fieldsets(request)
        print(f"   ✅ Nombre de fieldsets: {len(fieldsets)}")
        
        all_fields = []
        for name, options in fieldsets:
            fields = options.get('fields', [])
            all_fields.extend(fields)
            print(f"   • {name}: {len(fields)} champs")
        
        print(f"   📊 Total des champs dans les fieldsets: {len(all_fields)}")
        
        # Vérifier si tous les champs du modèle sont inclus
        model_fields = [f.name for f in Stagiaire._meta.fields if not f.name == 'id']
        missing_fields = set(model_fields) - set(all_fields)
        
        if missing_fields:
            print(f"   ⚠️ Champs manquants dans les fieldsets: {missing_fields}")
        else:
            print(f"   ✅ Tous les champs du modèle sont inclus")
    
    except Exception as e:
        print(f"   ❌ Erreur lors de la récupération des fieldsets: {e}")
    
    # Test des permissions
    print(f"\n🔐 Test des permissions:")
    has_add = stagiaire_admin.has_add_permission(request)
    has_change = stagiaire_admin.has_change_permission(request)
    has_delete = stagiaire_admin.has_delete_permission(request)
    
    print(f"   Ajouter: {'✅' if has_add else '❌'}")
    print(f"   Modifier: {'✅' if has_change else '❌'}")
    print(f"   Supprimer: {'✅' if has_delete else '❌'}")
    
    # Test des inlines
    print(f"\n📎 Test des inlines:")
    inlines = stagiaire_admin.get_inline_instances(request)
    print(f"   ✅ Nombre d'inlines: {len(inlines)}")
    for inline in inlines:
        print(f"   • {inline.__class__.__name__}")
    
    print(f"\n=== Test terminé ===")

if __name__ == '__main__':
    test_admin_stagiaire_form()
