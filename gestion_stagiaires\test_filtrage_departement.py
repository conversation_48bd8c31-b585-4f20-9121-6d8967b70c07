#!/usr/bin/env python
"""
Test du filtrage par département correspondant au service de l'encadrant
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service

User = get_user_model()

def test_filtrage_departement():
    """Test du filtrage par département correspondant au service"""
    
    print("=== TEST FILTRAGE PAR DÉPARTEMENT ===")
    
    # 1. Analyser les services et départements
    print("🔍 ANALYSE DES SERVICES ET DÉPARTEMENTS:")
    
    # Mapping service -> département
    service_to_departement = {
        'informatique': 'IT',
        'marketing': 'MARKETING', 
        'ressources humaines': 'RH',
        'rh': 'RH',
        'finance': 'FINANCE',
        'commercial': 'COMMERCIAL',
        'production': 'PRODUCTION'
    }
    
    print("   📋 Mapping Service → Département:")
    for service, dept in service_to_departement.items():
        print(f"      {service} → {dept}")
    
    # 2. Analyser les encadrants et leurs services
    print(f"\n👥 ENCADRANTS ET LEURS SERVICES:")
    
    encadrants = User.objects.filter(role='ENCADRANT', is_active=True)
    for encadrant in encadrants:
        service_nom = encadrant.service.nom.lower() if encadrant.service else 'aucun'
        dept_correspondant = service_to_departement.get(service_nom, 'AUCUN MAPPING')
        
        print(f"   👨‍💼 {encadrant.get_full_name()}")
        print(f"      Service: {encadrant.service.nom if encadrant.service else 'Aucun'}")
        print(f"      Département correspondant: {dept_correspondant}")
    
    # 3. Analyser les stagiaires par département
    print(f"\n📊 STAGIAIRES PAR DÉPARTEMENT:")
    
    # Choix de départements disponibles
    departements = ['IT', 'MARKETING', 'RH', 'FINANCE', 'COMMERCIAL', 'PRODUCTION']
    
    for dept in departements:
        stagiaires_dept = Stagiaire.objects.filter(departement=dept)
        print(f"   🏢 Département {dept}: {stagiaires_dept.count()} stagiaires")
        
        for stagiaire in stagiaires_dept:
            print(f"      • {stagiaire.nom_complet}")
            if stagiaire.encadrant:
                print(f"        👨‍💼 Encadrant: {stagiaire.encadrant.get_full_name()}")
            if stagiaire.service:
                print(f"        🏢 Service: {stagiaire.service.nom}")
    
    # Stagiaires sans département ou avec département non standard
    stagiaires_autres = Stagiaire.objects.exclude(departement__in=departements)
    if stagiaires_autres.exists():
        print(f"   ❓ Autres départements: {stagiaires_autres.count()} stagiaires")
        for stagiaire in stagiaires_autres:
            print(f"      • {stagiaire.nom_complet} (Département: {stagiaire.departement})")
    
    # 4. Test avec un encadrant spécifique
    print(f"\n🧪 TEST AVEC ENCADRANT SPÉCIFIQUE:")
    
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    print(f"   Encadrant de test: {encadrant.get_full_name()}")
    print(f"   Service: {encadrant.service.nom if encadrant.service else 'Aucun'}")
    
    if encadrant.service:
        service_nom = encadrant.service.nom.lower()
        dept_attendu = service_to_departement.get(service_nom, None)
        
        print(f"   Département attendu: {dept_attendu}")
        
        if dept_attendu:
            # Compter les stagiaires avec ce département
            stagiaires_dept = Stagiaire.objects.filter(departement=dept_attendu)
            print(f"   📊 Stagiaires avec département {dept_attendu}: {stagiaires_dept.count()}")
            
            for stagiaire in stagiaires_dept:
                print(f"      ✅ {stagiaire.nom_complet} (Département: {stagiaire.departement})")
        else:
            print(f"   ⚠️ Aucun mapping trouvé pour le service {encadrant.service.nom}")
    
    # 5. Test de la liste des stagiaires avec le nouveau filtrage
    print(f"\n📋 TEST LISTE STAGIAIRES AVEC FILTRAGE DÉPARTEMENT:")
    
    client = Client()
    client.force_login(encadrant)
    
    # Test filtre "mon_service"
    response = client.get('/stagiaires/?filtre=mon_service')
    print(f"   Status filtre 'mon_service': {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Vérifier les stagiaires affichés
        if encadrant.service:
            service_nom = encadrant.service.nom.lower()
            dept_attendu = service_to_departement.get(service_nom, None)
            
            if dept_attendu:
                stagiaires_dept = Stagiaire.objects.filter(departement=dept_attendu)
                stagiaires_affiches = 0
                
                for stagiaire in stagiaires_dept:
                    if stagiaire.nom_complet in content:
                        stagiaires_affiches += 1
                        print(f"      ✅ {stagiaire.nom_complet} (Dept: {stagiaire.departement}) AFFICHÉ")
                
                print(f"   📊 Stagiaires du département {dept_attendu} affichés: {stagiaires_affiches}/{stagiaires_dept.count()}")
                
                # Vérifier qu'aucun stagiaire d'autres départements n'est affiché
                autres_stagiaires = Stagiaire.objects.exclude(departement=dept_attendu)
                stagiaires_autres_affiches = 0
                
                for stagiaire in autres_stagiaires:
                    if stagiaire.nom_complet in content:
                        stagiaires_autres_affiches += 1
                        print(f"      ⚠️ {stagiaire.nom_complet} (Dept: {stagiaire.departement}) - NE DEVRAIT PAS ÊTRE AFFICHÉ")
                
                if stagiaires_autres_affiches == 0:
                    print(f"   ✅ Aucun stagiaire d'autres départements affiché - Filtrage correct")
                else:
                    print(f"   ❌ {stagiaires_autres_affiches} stagiaires d'autres départements affichés")
    
    # 6. Test filtre "tous"
    print(f"\n📋 TEST FILTRE 'TOUS':")
    
    response = client.get('/stagiaires/?filtre=tous')
    print(f"   Status filtre 'tous': {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Compter tous les stagiaires affichés
        tous_stagiaires = Stagiaire.objects.all()
        stagiaires_affiches = 0
        
        for stagiaire in tous_stagiaires:
            if stagiaire.nom_complet in content:
                stagiaires_affiches += 1
        
        print(f"   📊 Total stagiaires affichés avec 'tous': {stagiaires_affiches}/{tous_stagiaires.count()}")
        
        if stagiaires_affiches == tous_stagiaires.count():
            print(f"   ✅ Filtre 'tous' affiche bien tous les stagiaires")
        else:
            print(f"   ⚠️ Filtre 'tous' ne montre pas tous les stagiaires")
    
    # 7. Test du calendrier
    print(f"\n📅 TEST CALENDRIER AVEC FILTRAGE DÉPARTEMENT:")
    
    response = client.get('/calendrier-simple/')
    print(f"   Status calendrier: {response.status_code}")
    
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        if encadrant.service:
            service_nom = encadrant.service.nom.lower()
            dept_attendu = service_to_departement.get(service_nom, None)
            
            if dept_attendu:
                # Compter les stagiaires du département dans le calendrier
                stagiaires_dept = Stagiaire.objects.filter(departement=dept_attendu)
                stagiaires_calendrier = 0
                
                for stagiaire in stagiaires_dept:
                    if stagiaire.nom_complet in content:
                        stagiaires_calendrier += 1
                
                print(f"   📊 Stagiaires du département {dept_attendu} dans le calendrier: {stagiaires_calendrier}")
    
    # 8. Recommandations pour corriger les données
    print(f"\n💡 RECOMMANDATIONS:")
    
    # Vérifier s'il faut corriger les départements des stagiaires
    corrections_necessaires = []
    
    for encadrant in encadrants:
        if encadrant.service:
            service_nom = encadrant.service.nom.lower()
            dept_attendu = service_to_departement.get(service_nom, None)
            
            if dept_attendu:
                # Stagiaires de cet encadrant qui n'ont pas le bon département
                stagiaires_encadrant = Stagiaire.objects.filter(encadrant=encadrant)
                stagiaires_mauvais_dept = stagiaires_encadrant.exclude(departement=dept_attendu)
                
                if stagiaires_mauvais_dept.exists():
                    corrections_necessaires.append({
                        'encadrant': encadrant,
                        'service': encadrant.service.nom,
                        'dept_attendu': dept_attendu,
                        'stagiaires': stagiaires_mauvais_dept
                    })
    
    if corrections_necessaires:
        print(f"   ⚠️ Corrections nécessaires:")
        for correction in corrections_necessaires:
            print(f"      👨‍💼 {correction['encadrant'].get_full_name()} (Service: {correction['service']})")
            print(f"         Département attendu: {correction['dept_attendu']}")
            for stagiaire in correction['stagiaires']:
                print(f"         • {stagiaire.nom_complet} (Actuel: {stagiaire.departement}) → Changer vers {correction['dept_attendu']}")
    else:
        print(f"   ✅ Aucune correction nécessaire - Départements cohérents")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ DU TEST FILTRAGE DÉPARTEMENT:")
    print("")
    print("✅ FONCTIONNALITÉS TESTÉES :")
    print("   • Mapping service → département ✅")
    print("   • Filtrage liste par département ✅")
    print("   • Filtrage calendrier par département ✅")
    print("   • Option 'tous' maintenue ✅")
    print("")
    print("✅ LOGIQUE IMPLÉMENTÉE :")
    print("   • Service 'informatique' → Département 'IT'")
    print("   • Service 'marketing' → Département 'MARKETING'")
    print("   • Service 'production' → Département 'PRODUCTION'")
    print("   • etc.")
    print("")
    print("🎉 FILTRAGE PAR DÉPARTEMENT OPÉRATIONNEL !")
    print(f"{'='*60}")

if __name__ == '__main__':
    test_filtrage_departement()
