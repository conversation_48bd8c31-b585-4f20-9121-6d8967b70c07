#!/usr/bin/env python
"""
Debug complet du problème d'ajout de stagiaires
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service
from datetime import date, timedelta
import json

User = get_user_model()

def debug_complet():
    """Debug complet du problème"""
    
    print("=== DEBUG COMPLET DU PROBLÈME D'AJOUT ===")
    
    # 1. État initial
    print("\n1️⃣ État initial:")
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    print(f"Admin: {admin.username}")
    
    count_initial = Stagiaire.objects.count()
    print(f"Nombre de stagiaires initial: {count_initial}")
    
    # Lister les 3 derniers stagiaires
    derniers = Stagiaire.objects.order_by('-date_creation')[:3]
    print("Derniers stagiaires:")
    for s in derniers:
        print(f"  • {s.nom_complet} (ID: {s.id}) - Créé: {s.date_creation}")
    
    # 2. Simulation d'ajout complet
    print("\n2️⃣ Simulation d'ajout complet:")
    
    client = Client()
    client.force_login(admin)
    
    # Données de test
    test_data = {
        'nom': 'SOLUTION',
        'prenom': 'TEST',
        'email': '<EMAIL>',
        'telephone': '0123456789',
        'date_naissance': '2000-01-01',
        'departement': 'IT',
        'date_debut': date.today().strftime('%Y-%m-%d'),
        'date_fin': (date.today() + timedelta(days=60)).strftime('%Y-%m-%d'),
        'etablissement': 'Test Solution',
        'niveau_etude': 'Master',
        'specialite': 'Debug',
        'statut': 'EN_COURS',
    }
    
    # Ajouter un encadrant si disponible
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    if encadrant:
        test_data['encadrant'] = encadrant.id
    
    # Ajouter un service si disponible
    service = Service.objects.filter(actif=True).first()
    if service:
        test_data['service'] = service.id
    
    print(f"Données de test: {test_data}")
    
    # Supprimer le stagiaire de test s'il existe
    Stagiaire.objects.filter(email='<EMAIL>').delete()
    
    # 3. Test d'ajout étape par étape
    print("\n3️⃣ Test d'ajout étape par étape:")
    
    # Étape 1: Accès à la page
    print("   Étape 1: Accès à la page d'ajout")
    response_get = client.get('/stagiaires/add/')
    print(f"   Status GET: {response_get.status_code}")
    
    if response_get.status_code != 200:
        print("   ❌ Impossible d'accéder à la page d'ajout")
        return
    
    # Étape 2: Soumission du formulaire
    print("   Étape 2: Soumission du formulaire")
    response_post = client.post('/stagiaires/add/', test_data, follow=False)
    print(f"   Status POST: {response_post.status_code}")
    
    if response_post.status_code == 302:
        print(f"   ✅ Redirection vers: {response_post.url}")
    elif response_post.status_code == 200:
        print("   ⚠️ Formulaire retourné (erreurs possibles)")
        
        # Analyser les erreurs
        content = response_post.content.decode('utf-8')
        if 'error' in content.lower() or 'erreur' in content.lower():
            print("   ❌ Erreurs détectées")
            
            # Extraire les erreurs spécifiques
            import re
            errors = re.findall(r'<div[^>]*alert[^>]*>(.*?)</div>', content, re.DOTALL)
            for error in errors:
                clean_error = re.sub(r'<[^>]+>', '', error).strip()
                if clean_error:
                    print(f"      • {clean_error}")
    
    # Étape 3: Vérification en base
    print("   Étape 3: Vérification en base de données")
    count_apres = Stagiaire.objects.count()
    print(f"   Nombre de stagiaires après: {count_apres}")
    
    stagiaire_cree = Stagiaire.objects.filter(email='<EMAIL>').first()
    if stagiaire_cree:
        print(f"   ✅ Stagiaire créé: {stagiaire_cree.nom_complet} (ID: {stagiaire_cree.id})")
        print(f"      Créé par: {stagiaire_cree.cree_par}")
        print(f"      Date création: {stagiaire_cree.date_creation}")
    else:
        print("   ❌ Stagiaire non trouvé en base")
    
    # Étape 4: Vérification dans la liste
    print("   Étape 4: Vérification dans la liste web")
    response_list = client.get('/stagiaires/')
    print(f"   Status liste: {response_list.status_code}")
    
    if response_list.status_code == 200:
        content_list = response_list.content.decode('utf-8')
        
        if 'SOLUTION' in content_list and 'TEST' in content_list:
            print("   ✅ Stagiaire visible dans la liste web")
        else:
            print("   ❌ Stagiaire non visible dans la liste web")
            
            # Compter les lignes de tableau
            import re
            rows = re.findall(r'<tr[^>]*>.*?</tr>', content_list, re.DOTALL)
            data_rows = [row for row in rows if 'table-dark' not in row and 'thead' not in row]
            print(f"      Lignes de données dans le HTML: {len(data_rows)}")
            
            # Vérifier s'il y a un message "Aucun stagiaire"
            if 'Aucun stagiaire' in content_list:
                print("      ⚠️ Message 'Aucun stagiaire' détecté")
    
    # 4. Test avec l'admin Django
    print("\n4️⃣ Test avec l'admin Django:")
    
    # Supprimer le stagiaire de test admin s'il existe
    Stagiaire.objects.filter(email='<EMAIL>').delete()
    
    admin_data = {
        'nom': 'AdminSolution',
        'prenom': 'AdminTest',
        'email': '<EMAIL>',
        'date_naissance': '2000-01-01',
        'departement': 'IT',
        'date_debut': date.today().strftime('%Y-%m-%d'),
        'date_fin': (date.today() + timedelta(days=60)).strftime('%Y-%m-%d'),
        'etablissement': 'Admin Test',
        'niveau_etude': 'Master',
        'specialite': 'Admin',
        'statut': 'EN_COURS',
        '_save': 'Enregistrer',
    }
    
    if encadrant:
        admin_data['encadrant'] = encadrant.id
    if service:
        admin_data['service'] = service.id
    
    response_admin = client.post('/admin/stagiaires/stagiaire/add/', admin_data)
    print(f"   Status admin POST: {response_admin.status_code}")
    
    stagiaire_admin = Stagiaire.objects.filter(email='<EMAIL>').first()
    if stagiaire_admin:
        print(f"   ✅ Stagiaire créé via admin: {stagiaire_admin.nom_complet}")
    else:
        print("   ❌ Stagiaire non créé via admin")
    
    # 5. Analyse des différences
    print("\n5️⃣ Analyse des différences:")
    
    # Comparer les deux méthodes
    stagiaire_web = Stagiaire.objects.filter(email='<EMAIL>').first()
    stagiaire_admin = Stagiaire.objects.filter(email='<EMAIL>').first()
    
    if stagiaire_web and stagiaire_admin:
        print("   ✅ Les deux méthodes fonctionnent")
        
        # Comparer les champs
        champs_diff = []
        for field in ['nom', 'prenom', 'email', 'departement', 'statut', 'cree_par']:
            val_web = getattr(stagiaire_web, field, None)
            val_admin = getattr(stagiaire_admin, field, None)
            if val_web != val_admin:
                champs_diff.append(f"{field}: web={val_web}, admin={val_admin}")
        
        if champs_diff:
            print("   Différences détectées:")
            for diff in champs_diff:
                print(f"      • {diff}")
        else:
            print("   ✅ Aucune différence entre les méthodes")
    
    elif stagiaire_web and not stagiaire_admin:
        print("   ⚠️ Méthode web fonctionne, admin ne fonctionne pas")
    elif not stagiaire_web and stagiaire_admin:
        print("   ⚠️ Méthode admin fonctionne, web ne fonctionne pas")
    else:
        print("   ❌ Aucune méthode ne fonctionne")
    
    # 6. Solution proposée
    print("\n6️⃣ SOLUTION PROPOSÉE:")
    
    if stagiaire_web:
        print("✅ L'ajout fonctionne techniquement")
        print("🔍 Le problème est probablement:")
        print("   1. Cache du navigateur")
        print("   2. Filtres JavaScript côté client")
        print("   3. Problème d'affichage dans le template")
        print("   4. Redirection vers une mauvaise page")
        
        print("\n🛠️ ACTIONS À EFFECTUER:")
        print("   1. Videz le cache du navigateur (Ctrl+Shift+Del)")
        print("   2. Rafraîchissez avec Ctrl+F5")
        print("   3. Vérifiez l'URL après ajout")
        print("   4. Désactivez JavaScript temporairement")
        print("   5. Vérifiez les filtres sur la page")
    else:
        print("❌ L'ajout ne fonctionne pas techniquement")
        print("🔍 Problèmes possibles:")
        print("   1. Validation du formulaire")
        print("   2. Contraintes de base de données")
        print("   3. Erreurs dans la vue")
        print("   4. Problèmes de permissions")
    
    # Nettoyer
    if stagiaire_web:
        stagiaire_web.delete()
    if stagiaire_admin:
        stagiaire_admin.delete()
    
    print(f"\n🧹 Stagiaires de test supprimés")
    print(f"📊 Nombre final de stagiaires: {Stagiaire.objects.count()}")

if __name__ == '__main__':
    debug_complet()
