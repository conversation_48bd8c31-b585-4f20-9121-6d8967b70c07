# Generated by Django 5.1.6 on 2025-07-15 10:54

import django.db.models.deletion
import stagiaires.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stagiaires', '0006_customuser_date_creation'),
    ]

    operations = [
        migrations.AddField(
            model_name='stagiaire',
            name='commentaire_rapport',
            field=models.TextField(blank=True, verbose_name='Commentaire sur le rapport'),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='date_upload_rapport',
            field=models.DateTimeField(blank=True, null=True, verbose_name="Date d'upload du rapport"),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='rapport_stage',
            field=models.FileField(blank=True, null=True, upload_to=stagiaires.models.rapport_upload_path, verbose_name='Rapport de stage'),
        ),
        migrations.AddField(
            model_name='stagiaire',
            name='rapport_uploade_par',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rapports_uploades', to=settings.AUTH_USER_MODEL, verbose_name='Rapport uploadé par'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='role',
            field=models.CharField(choices=[('ADMIN', 'Administrateur'), ('RH', 'Gestionnaire RH'), ('ENCADRANT', 'Encadrant'), ('STAGIAIRE', 'Stagiaire')], default='STAGIAIRE', max_length=10, verbose_name='Rôle'),
        ),
    ]
