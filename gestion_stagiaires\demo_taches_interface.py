#!/usr/bin/env python
"""
Script de démonstration pour l'interface de gestion des tâches
"""

import os
import sys
import django
from datetime import date, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser, Stagiaire, TacheStage

def create_demo_data():
    """Créer des données de démonstration pour tester l'interface"""
    print("🎬 Création des données de démonstration")
    print("=" * 50)
    
    # Créer un encadrant de démonstration
    encadrant, created = CustomUser.objects.get_or_create(
        username='demo_encadrant',
        defaults={
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>ois',
            'role': 'ENCADRANT',
            'is_active': True
        }
    )
    if created:
        encadrant.set_password('demo123')
        encadrant.save()
        print(f"✓ Encadrant créé : {encadrant.get_full_name()}")
        print(f"  - Login : {encadrant.username}")
        print(f"  - Mot de passe : demo123")
    else:
        print(f"✓ Encadrant existant : {encadrant.get_full_name()}")
    
    # Créer un stagiaire de démonstration
    stagiaire, created = Stagiaire.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'nom': 'Durand',
            'prenom': 'Thomas',
            'date_naissance': date(2001, 3, 20),
            'telephone': '0654321098',
            'departement': 'Informatique',
            'encadrant': encadrant,
            'date_debut': date.today(),
            'date_fin': date.today() + timedelta(days=60),
            'etablissement': 'Université de Technologie',
            'niveau_etude': 'Master 1',
            'specialite': 'Intelligence Artificielle',
            'cree_par': encadrant
        }
    )
    if created:
        print(f"✓ Stagiaire créé : {stagiaire.nom_complet}")
    else:
        print(f"✓ Stagiaire existant : {stagiaire.nom_complet}")
    
    # Créer des tâches de démonstration avec différents statuts
    taches_demo = [
        {
            'titre': 'Analyse des besoins utilisateurs',
            'description': 'Étudier les besoins des utilisateurs finaux et rédiger un cahier des charges détaillé.',
            'priorite': 1,
            'statut': 'NON_COMMENCEE',
            'jours_debut': 1,
            'jours_duree': 7
        },
        {
            'titre': 'Conception de la base de données',
            'description': 'Modéliser la structure de données et créer le schéma de base de données.',
            'priorite': 1,
            'statut': 'EN_COURS',
            'jours_debut': 3,
            'jours_duree': 10
        },
        {
            'titre': 'Développement du backend API',
            'description': 'Implémenter les services web REST pour l\'application.',
            'priorite': 2,
            'statut': 'NON_COMMENCEE',
            'jours_debut': 10,
            'jours_duree': 15
        },
        {
            'titre': 'Interface utilisateur responsive',
            'description': 'Créer une interface moderne et responsive avec Bootstrap.',
            'priorite': 2,
            'statut': 'TERMINEE',
            'jours_debut': 20,
            'jours_duree': 12
        },
        {
            'titre': 'Tests unitaires et intégration',
            'description': 'Écrire et exécuter les tests pour valider le fonctionnement.',
            'priorite': 3,
            'statut': 'NON_COMMENCEE',
            'jours_debut': 35,
            'jours_duree': 8
        }
    ]
    
    print(f"\n📋 Création de {len(taches_demo)} tâches de démonstration :")
    
    for i, tache_data in enumerate(taches_demo, 1):
        tache, created = TacheStage.objects.get_or_create(
            titre=tache_data['titre'],
            stagiaire=stagiaire,
            defaults={
                'description': tache_data['description'],
                'date_debut_prevue': date.today() + timedelta(days=tache_data['jours_debut']),
                'date_fin_prevue': date.today() + timedelta(days=tache_data['jours_debut'] + tache_data['jours_duree']),
                'priorite': tache_data['priorite'],
                'statut': tache_data['statut'],
                'creee_par': encadrant
            }
        )
        
        # Mettre à jour les dates réelles pour les tâches en cours ou terminées
        if tache_data['statut'] in ['EN_COURS', 'TERMINEE']:
            tache.date_debut_reelle = date.today() - timedelta(days=2)
            if tache_data['statut'] == 'TERMINEE':
                tache.date_fin_reelle = date.today() - timedelta(days=1)
                tache.note = 16.5
                tache.commentaire_encadrant = "Travail de qualité, conforme aux attentes."
            tache.save()
        
        status_icon = {
            'NON_COMMENCEE': '⚪',
            'EN_COURS': '🔵',
            'TERMINEE': '🟢',
            'VALIDEE': '🟣'
        }
        
        print(f"  {i}. {status_icon.get(tache_data['statut'], '⚪')} {tache.titre}")
        print(f"     Statut : {tache.get_statut_display()}")
        print(f"     Priorité : {tache.priorite}")
        print(f"     Période : {tache.date_debut_prevue} → {tache.date_fin_prevue}")
    
    return encadrant, stagiaire

def print_demo_instructions(encadrant, stagiaire):
    """Afficher les instructions pour tester la démonstration"""
    print("\n" + "=" * 50)
    print("🎯 INSTRUCTIONS POUR TESTER L'INTERFACE")
    print("=" * 50)
    
    print("\n1. 🌐 ACCÈS À L'APPLICATION :")
    print("   URL : http://127.0.0.1:8000")
    print("   Assurez-vous que le serveur Django est démarré")
    
    print("\n2. 🔐 CONNEXION :")
    print(f"   Nom d'utilisateur : {encadrant.username}")
    print("   Mot de passe : demo123")
    print("   Rôle : Encadrant")
    
    print("\n3. 📋 NAVIGATION :")
    print("   → Cliquez sur 'Gestion des Stagiaires'")
    print("   → Cliquez sur 'Voir la liste'")
    print(f"   → Trouvez le stagiaire '{stagiaire.nom_complet}'")
    print("   → Cliquez sur le bouton 'Tâches'")
    
    print("\n4. ⚡ TESTS À EFFECTUER :")
    print("   ✅ Cliquer sur 'Démarrer' pour une tâche non commencée")
    print("      → Vérifier que le statut passe à 'En cours'")
    print("      → Observer le changement de couleur du badge")
    print("      → Vérifier le message de confirmation")
    
    print("   ✅ Cliquer sur 'Terminer' pour une tâche en cours")
    print("      → Vérifier que le statut passe à 'Terminée'")
    print("      → Observer la mise à jour visuelle")
    print("      → Vérifier l'enregistrement des dates")
    
    print("   ✅ Cliquer sur 'Détails' pour voir les informations")
    print("      → Vérifier l'affichage du modal")
    print("      → Observer les détails de la tâche")
    
    print("\n5. 🔍 POINTS À VÉRIFIER :")
    print("   • Les boutons sont-ils réactifs ?")
    print("   • Les messages de confirmation apparaissent-ils ?")
    print("   • Les couleurs des badges changent-elles ?")
    print("   • La page se recharge-t-elle automatiquement ?")
    print("   • Les dates sont-elles mises à jour ?")
    
    print("\n6. 🐛 EN CAS DE PROBLÈME :")
    print("   • Vérifiez la console du navigateur (F12)")
    print("   • Vérifiez les logs du serveur Django")
    print("   • Actualisez la page (F5)")
    print("   • Vérifiez votre connexion internet")

def print_technical_details():
    """Afficher les détails techniques de l'implémentation"""
    print("\n" + "=" * 50)
    print("🔧 DÉTAILS TECHNIQUES")
    print("=" * 50)
    
    print("\n📡 COMMUNICATION AJAX :")
    print("   • Endpoint : /taches/<id>/update-status/")
    print("   • Méthode : POST")
    print("   • Format : JSON")
    print("   • CSRF : Protection activée")
    
    print("\n🔒 SÉCURITÉ :")
    print("   • Vérification des permissions utilisateur")
    print("   • Validation des statuts côté serveur")
    print("   • Protection CSRF automatique")
    print("   • Logs d'audit des modifications")
    
    print("\n💾 BASE DE DONNÉES :")
    print("   • Modèle : TacheStage")
    print("   • Champs mis à jour : statut, date_debut_reelle, date_fin_reelle")
    print("   • Relations : Stagiaire → Encadrant")
    print("   • Contraintes : Permissions par rôle")
    
    print("\n🎨 INTERFACE :")
    print("   • Framework : Bootstrap 5")
    print("   • JavaScript : Vanilla JS (pas de jQuery)")
    print("   • Feedback : Alertes dynamiques")
    print("   • UX : Indicateurs de chargement")

def main():
    """Fonction principale de démonstration"""
    print("🎬 DÉMONSTRATION - GESTION DES TÂCHES")
    print("🚀 Système de mise à jour des statuts en temps réel")
    print("=" * 60)
    
    try:
        # Créer les données de démonstration
        encadrant, stagiaire = create_demo_data()
        
        # Afficher les instructions
        print_demo_instructions(encadrant, stagiaire)
        
        # Afficher les détails techniques
        print_technical_details()
        
        print("\n" + "=" * 60)
        print("✅ DÉMONSTRATION PRÊTE !")
        print("🎉 Vous pouvez maintenant tester l'interface web.")
        print("📖 Consultez le guide GUIDE_TACHES_ENCADRANT.md pour plus de détails.")
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la préparation : {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
