#!/usr/bin/env python
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Stagiaire
from datetime import date, timedelta

User = get_user_model()

print("=== Test simple d'ajout de stagiaire ===")

# Récupérer un admin
admin = User.objects.filter(is_superuser=True).first()
print(f"Admin: {admin.username if admin else 'Aucun'}")

# Récupérer un encadrant
encadrant = User.objects.filter(role='ENCADRANT').first()
print(f"Encadrant: {encadrant.username if encadrant else 'Aucun'}")

# Test de création simple
try:
    # Supprimer le stagiaire de test s'il existe
    Stagiaire.objects.filter(email='<EMAIL>').delete()

    # Créer un stagiaire avec les champs obligatoires
    stagiaire = Stagiaire.objects.create(
        nom='TestSimple',
        prenom='Stagiaire',
        email='<EMAIL>',
        date_naissance=date(2000, 1, 1),
        departement='IT',
        date_debut=date.today(),
        date_fin=date.today() + timedelta(days=90),
        etablissement='Université Test',
        niveau_etude='Master',
        specialite='Informatique',
        cree_par=admin,
        encadrant=encadrant
    )

    print(f"✅ Stagiaire créé: {stagiaire.nom_complet} (ID: {stagiaire.id})")
    print(f"   Créé par: {stagiaire.cree_par}")
    print(f"   Encadrant: {stagiaire.encadrant}")

    # Nettoyer
    stagiaire.delete()
    print("🧹 Stagiaire supprimé")

except Exception as e:
    print(f"❌ Erreur: {e}")
    import traceback
    traceback.print_exc()

print("=== Test terminé ===")
