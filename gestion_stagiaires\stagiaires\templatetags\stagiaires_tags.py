from django import template

register = template.Library()

@register.filter
def split(value, arg):
    """Divise une chaîne selon le séparateur spécifié"""
    return value.split(arg)

@register.filter
def trim(value):
    """Supprime les espaces en début et fin de chaîne"""
    return value.strip()

@register.filter
def subtract(value, arg):
    """Soustrait arg de value"""
    try:
        return int(value) - int(arg)
    except (ValueError, TypeError):
        return 0
