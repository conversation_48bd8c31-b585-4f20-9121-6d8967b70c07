{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- En-tête -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-file-pdf me-2"></i>{{ title }}</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Tableau de bord</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'stagiaires_list' %}">Stagiaires</a></li>
                            <li class="breadcrumb-item"><a href="{% url 'stagiaire_detail' stagiaire.id %}">{{ stagiaire.nom_complet }}</a></li>
                            <li class="breadcrumb-item active">CV</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{% url 'stagiaire_detail' stagiaire.id %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                    <a href="{{ stagiaire.cv.url }}" target="_blank" class="btn btn-primary">
                        <i class="fas fa-download me-1"></i>Télécharger
                    </a>
                </div>
            </div>

            <!-- Informations du stagiaire -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Informations du stagiaire</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Nom complet :</strong><br>
                            {{ stagiaire.nom_complet }}
                        </div>
                        <div class="col-md-3">
                            <strong>Email :</strong><br>
                            {{ stagiaire.email }}
                        </div>
                        <div class="col-md-3">
                            <strong>Service :</strong><br>
                            {{ stagiaire.service.nom|default:"Non défini" }}
                        </div>
                        <div class="col-md-3">
                            <strong>Encadrant :</strong><br>
                            {{ stagiaire.encadrant.get_full_name|default:"Non assigné" }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Visualisation du CV -->
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-file-pdf me-2"></i>CV - {{ stagiaire.cv.name|default:"Document" }}</h5>
                </div>
                <div class="card-body p-0">
                    {% if stagiaire.cv %}
                        {% if stagiaire.cv.name|slice:"-4:" == ".pdf" %}
                            <!-- Affichage PDF -->
                            <div class="text-center p-3">
                                <embed src="{{ stagiaire.cv.url }}" type="application/pdf" width="100%" height="800px" />
                                <div class="mt-3">
                                    <p class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Si le PDF ne s'affiche pas correctement, 
                                        <a href="{{ stagiaire.cv.url }}" target="_blank" class="text-primary">cliquez ici pour l'ouvrir dans un nouvel onglet</a>
                                    </p>
                                </div>
                            </div>
                        {% else %}
                            <!-- Autres types de fichiers -->
                            <div class="text-center p-5">
                                <i class="fas fa-file fa-5x text-muted mb-3"></i>
                                <h4>Aperçu non disponible</h4>
                                <p class="text-muted">Ce type de fichier ne peut pas être affiché directement.</p>
                                <a href="{{ stagiaire.cv.url }}" target="_blank" class="btn btn-primary">
                                    <i class="fas fa-download me-1"></i>Télécharger le fichier
                                </a>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center p-5">
                            <i class="fas fa-file-excel fa-5x text-muted mb-3"></i>
                            <h4>Aucun CV disponible</h4>
                            <p class="text-muted">Ce stagiaire n'a pas encore téléversé son CV.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}