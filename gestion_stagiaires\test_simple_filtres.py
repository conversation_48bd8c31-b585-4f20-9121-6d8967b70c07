#!/usr/bin/env python
"""
Test simple des filtres pour les encadrants
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire

User = get_user_model()

def test_simple_filtres():
    """Test simple des filtres"""
    
    print("=== TEST SIMPLE DES FILTRES ===")
    
    # 1. Récupérer un encadrant
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    
    if not encadrant:
        print("❌ Aucun encadrant trouvé")
        return
    
    print(f"✅ Encadrant: {encadrant.get_full_name()}")
    
    # 2. Test d'accès aux pages
    client = Client()
    client.force_login(encadrant)
    
    tests = [
        ('/stagiaires/', 'Page normale'),
        ('/stagiaires/?filtre=tous', 'Filtre tous'),
        ('/stagiaires/?filtre=mon_service', 'Filtre mon service'),
    ]
    
    for url, description in tests:
        try:
            response = client.get(url)
            print(f"   {description}: Status {response.status_code}")
            
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                
                # Vérifier la présence des éléments clés
                if 'Filtres d\'affichage' in content:
                    print(f"      ✅ Section filtres présente")
                else:
                    print(f"      ⚠️ Section filtres absente")
                
                if 'Tous les stagiaires' in content:
                    print(f"      ✅ Bouton 'Tous' présent")
                else:
                    print(f"      ⚠️ Bouton 'Tous' absent")
                
                if 'Mon service' in content:
                    print(f"      ✅ Bouton 'Mon service' présent")
                else:
                    print(f"      ⚠️ Bouton 'Mon service' absent")
            
        except Exception as e:
            print(f"   {description}: Erreur - {e}")
    
    # 3. Test avec admin
    print(f"\n👑 Test avec admin:")
    
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    if admin:
        client.force_login(admin)
        response = client.get('/stagiaires/')
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            if 'Filtres d\'affichage' in content:
                print(f"   ⚠️ Filtres visibles pour admin (ne devrait pas)")
            else:
                print(f"   ✅ Filtres cachés pour admin (correct)")
    
    # 4. Statistiques
    print(f"\n📊 Statistiques:")
    
    total_stagiaires = Stagiaire.objects.count()
    print(f"   Total stagiaires en base: {total_stagiaires}")
    
    # Compter par département
    departements = {}
    for stagiaire in Stagiaire.objects.all():
        dept = stagiaire.departement
        if dept not in departements:
            departements[dept] = 0
        departements[dept] += 1
    
    print(f"   Répartition par département:")
    for dept, count in departements.items():
        print(f"     • {dept}: {count}")
    
    print(f"\n{'='*50}")
    print("🎯 FONCTIONNALITÉ IMPLÉMENTÉE:")
    print("")
    print("✅ Pour les ENCADRANTS :")
    print("   • Interface avec 2 boutons de filtre")
    print("   • 'Tous les stagiaires' - Voir tous")
    print("   • 'Mon service' - Voir uniquement ses stagiaires")
    print("   • Statistiques affichées en temps réel")
    print("   • Recherche en temps réel")
    print("")
    print("✅ Pour les AUTRES RÔLES :")
    print("   • Admin/RH : Pas de filtres")
    print("   • Comportement normal maintenu")
    print("")
    print("🚀 TESTEZ MAINTENANT :")
    print("   1. Connectez-vous en tant qu'encadrant")
    print("   2. Allez sur /stagiaires/")
    print("   3. Vous verrez les boutons de filtre")
    print("   4. Cliquez pour basculer entre les vues")
    print(f"{'='*50}")

if __name__ == '__main__':
    test_simple_filtres()
