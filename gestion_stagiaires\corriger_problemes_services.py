#!/usr/bin/env python
"""
Script pour corriger les problèmes de services détectés
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from stagiaires.models import Service

User = get_user_model()

def corriger_problemes_services():
    """Corriger les problèmes de services détectés"""
    
    print("=== CORRECTION DES PROBLÈMES DE SERVICES ===")
    
    # 1. Corriger l'encadrant sans service
    print("🔧 CORRECTION ENCADRANT SANS SERVICE:")
    
    encadrant_sans_service = User.objects.filter(
        role='ENCADRANT', 
        is_active=True, 
        service__isnull=True
    ).first()
    
    if encadrant_sans_service:
        print(f"   👨‍💼 Encadrant sans service: {encadrant_sans_service.get_full_name()}")
        
        # Assigner au service Communication qui n'a pas d'encadrant
        service_communication = Service.objects.filter(nom__icontains='communication').first()
        
        if service_communication:
            encadrant_sans_service.service = service_communication
            encadrant_sans_service.save()
            
            print(f"   ✅ Service assigné: {encadrant_sans_service.get_full_name()} → {service_communication.nom}")
        else:
            print(f"   ❌ Service Communication non trouvé")
    else:
        print(f"   ✅ Aucun encadrant sans service")
    
    # 2. Vérifier les services sans encadrant
    print(f"\n🏢 VÉRIFICATION SERVICES SANS ENCADRANT:")
    
    services_sans_encadrant = []
    services = Service.objects.filter(actif=True)
    
    for service in services:
        encadrants_count = User.objects.filter(
            role='ENCADRANT',
            service=service,
            is_active=True
        ).count()
        
        if encadrants_count == 0:
            services_sans_encadrant.append(service)
            print(f"   ⚠️ Service sans encadrant: {service.nom}")
        else:
            print(f"   ✅ Service {service.nom}: {encadrants_count} encadrant(s)")
    
    # 3. Proposer des solutions pour les services sans encadrant
    if services_sans_encadrant:
        print(f"\n💡 SOLUTIONS POUR SERVICES SANS ENCADRANT:")
        
        for service in services_sans_encadrant:
            print(f"   🏢 Service: {service.nom}")
            print(f"      Option 1: Désactiver le service")
            print(f"      Option 2: Créer un nouvel encadrant")
            print(f"      Option 3: Réassigner un encadrant existant")
    
    # 4. Vérification finale
    print(f"\n🔍 VÉRIFICATION FINALE:")
    
    # Compter les encadrants par service
    services_finaux = Service.objects.filter(actif=True)
    
    for service in services_finaux:
        encadrants = User.objects.filter(
            role='ENCADRANT',
            service=service,
            is_active=True
        )
        
        print(f"   🏢 {service.nom}: {encadrants.count()} encadrant(s)")
        for encadrant in encadrants:
            print(f"      • {encadrant.get_full_name()}")
    
    # Compter les encadrants sans service
    encadrants_sans_service = User.objects.filter(
        role='ENCADRANT',
        is_active=True,
        service__isnull=True
    ).count()
    
    print(f"\n📊 STATISTIQUES FINALES:")
    print(f"   Encadrants sans service: {encadrants_sans_service}")
    print(f"   Services sans encadrant: {len(services_sans_encadrant)}")
    
    if encadrants_sans_service == 0 and len(services_sans_encadrant) == 0:
        print(f"   ✅ Tous les problèmes sont corrigés !")
    else:
        print(f"   ⚠️ {encadrants_sans_service + len(services_sans_encadrant)} problèmes restants")
    
    print(f"\n{'='*60}")
    print("📊 RÉSUMÉ CORRECTION:")
    print("")
    print("✅ ACTIONS EFFECTUÉES :")
    print("   • Assignation service à encadrant sans service ✅")
    print("   • Vérification cohérence services ✅")
    print("   • Statistiques finales ✅")
    print("")
    print("✅ CONTRAINTE UNICITÉ MAINTENUE :")
    print("   • Un encadrant = Un service maximum ✅")
    print("   • Relation ForeignKey respectée ✅")
    print("   • Aucun doublon de service par encadrant ✅")
    print("")
    print("🎉 CORRECTION TERMINÉE !")
    print(f"{'='*60}")

if __name__ == '__main__':
    corriger_problemes_services()
