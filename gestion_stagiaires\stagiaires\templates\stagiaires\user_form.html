{% block extra_js %}
<script>
    function toggleServiceField(role) {
        const serviceField = document.getElementById('id_service');
        const serviceGroup = serviceField.closest('.mb-3');
        
        if (role === 'ENCADRANT') {
            serviceField.required = true;
            serviceGroup.querySelector('label').innerHTML += ' *';
            serviceGroup.style.display = 'block';
        } else {
            serviceField.required = false;
            serviceGroup.querySelector('label').innerHTML = serviceGroup.querySelector('label').innerHTML.replace(' *', '');
            serviceGroup.style.display = 'none';
        }
    }
    
    // Exécuter au chargement de la page
    document.addEventListener('DOMContentLoaded', function() {
        const roleSelect = document.getElementById('id_role');
        if (roleSelect) {
            toggleServiceField(roleSelect.value);
        }
    });
</script>
{% endblock %}