#!/usr/bin/env python
"""
Test simple de l'ajout de stagiaires
"""

import os
import sys
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from stagiaires.models import Stagiaire, Service
from datetime import date, timedelta

User = get_user_model()

def test_ajout_simple():
    """Test simple de l'ajout de stagiaires"""
    
    print("=== Test simple de l'ajout de stagiaires ===")
    
    # Récupérer un admin
    admin = User.objects.filter(role='ADMIN', is_active=True).first()
    if not admin:
        print("❌ Aucun admin trouvé")
        return
    
    print(f"✅ Admin: {admin.username}")
    
    # Récupérer un encadrant
    encadrant = User.objects.filter(role='ENCADRANT', is_active=True).first()
    if not encadrant:
        print("❌ Aucun encadrant trouvé")
        return
    
    print(f"✅ Encadrant: {encadrant.get_full_name()}")
    
    # Test d'accès à la page
    client = Client()
    client.force_login(admin)
    
    print("\n📋 Test d'accès à la page d'ajout:")
    response = client.get('/stagiaires/add/')
    print(f"Status: {response.status_code}")
    
    if response.status_code != 200:
        print("❌ Impossible d'accéder à la page d'ajout")
        return
    
    print("✅ Page d'ajout accessible")
    
    # Données de test minimales
    print("\n📝 Test d'ajout avec données minimales:")
    
    # Supprimer le stagiaire de test s'il existe
    Stagiaire.objects.filter(email='<EMAIL>').delete()
    
    data = {
        'nom': 'TestSimple',
        'prenom': 'TestSimple',
        'email': '<EMAIL>',
        'date_naissance': '2000-01-01',
        'departement': 'IT',
        'encadrant': encadrant.id,
        'date_debut': date.today().strftime('%Y-%m-%d'),
        'date_fin': (date.today() + timedelta(days=30)).strftime('%Y-%m-%d'),
        'etablissement': 'Test',
        'niveau_etude': 'Master',
        'specialite': 'Test',
        'statut': 'EN_COURS',
    }
    
    print(f"Données: {data}")
    
    try:
        response = client.post('/stagiaires/add/', data, follow=True)
        print(f"Status POST: {response.status_code}")
        
        # Vérifier si le stagiaire a été créé
        stagiaire_cree = Stagiaire.objects.filter(email='<EMAIL>').first()
        
        if stagiaire_cree:
            print(f"✅ Stagiaire créé: {stagiaire_cree.nom_complet}")
            print(f"   ID: {stagiaire_cree.id}")
            print(f"   Créé par: {stagiaire_cree.cree_par}")
            
            # Nettoyer
            stagiaire_cree.delete()
            print("🧹 Stagiaire supprimé")
        else:
            print("❌ Stagiaire non créé")
            
            # Afficher les erreurs de la réponse
            if response.status_code == 200:
                content = response.content.decode('utf-8')
                if 'error' in content.lower() or 'erreur' in content.lower():
                    print("⚠️ Erreurs détectées dans la réponse")
                    
                    # Chercher les messages d'erreur
                    import re
                    errors = re.findall(r'<div[^>]*alert-danger[^>]*>(.*?)</div>', content, re.DOTALL)
                    for error in errors:
                        print(f"   Erreur: {error.strip()}")
    
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🔍 Vérification des contraintes:")
    
    # Vérifier les contraintes du modèle
    from django.core.exceptions import ValidationError
    
    try:
        # Créer un stagiaire directement
        stagiaire_direct = Stagiaire(
            nom='DirectTest',
            prenom='DirectTest',
            email='<EMAIL>',
            date_naissance=date(2000, 1, 1),
            departement='IT',
            encadrant=encadrant,
            date_debut=date.today(),
            date_fin=date.today() + timedelta(days=30),
            etablissement='Test Direct',
            niveau_etude='Master',
            specialite='Test',
            statut='EN_COURS',
            cree_par=admin
        )
        
        stagiaire_direct.full_clean()  # Validation
        stagiaire_direct.save()
        
        print("✅ Création directe réussie")
        print(f"   Stagiaire: {stagiaire_direct.nom_complet}")
        
        # Nettoyer
        stagiaire_direct.delete()
        print("🧹 Stagiaire direct supprimé")
        
    except ValidationError as e:
        print(f"❌ Erreur de validation: {e}")
    except Exception as e:
        print(f"❌ Erreur de création directe: {e}")
    
    print("\n📊 RÉSUMÉ:")
    print("Si l'ajout ne fonctionne pas via le formulaire web,")
    print("mais fonctionne en création directe, le problème est dans:")
    print("1. Le formulaire Django (validation)")
    print("2. La vue (traitement POST)")
    print("3. Le template (JavaScript/HTML)")

if __name__ == '__main__':
    test_ajout_simple()
