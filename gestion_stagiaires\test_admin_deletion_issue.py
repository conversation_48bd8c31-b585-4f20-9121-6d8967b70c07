#!/usr/bin/env python
"""
Script de diagnostic pour identifier pourquoi les suppressions ne fonctionnent pas dans l'admin Django
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from django.contrib import admin
from django.test import RequestFactory, Client
from django.contrib.auth import get_user_model
from stagiaires.models import CustomUser, Stagiaire
from stagiaires.admin import CustomUserAdmin, StagiaireAdmin

def test_admin_configuration():
    """Tester la configuration de l'admin Django"""
    print("🔧 DIAGNOSTIC DE L'ADMIN DJANGO")
    print("=" * 60)
    
    # Vérifier l'enregistrement des modèles
    print("📋 Modèles enregistrés dans l'admin :")
    for model, admin_class in admin.site._registry.items():
        print(f"   ✅ {model.__name__} → {admin_class.__class__.__name__}")
    
    print()
    
    # Vérifier les actions disponibles pour CustomUser
    print("🔍 Actions disponibles pour CustomUser :")
    user_admin = CustomUserAdmin(CustomUser, admin.site)
    actions = user_admin.get_actions(None)
    
    for action_name, (action_func, name, description) in actions.items():
        print(f"   • {action_name}: {description}")
    
    # Vérifier spécifiquement delete_selected
    has_delete = 'delete_selected' in actions
    print(f"\n🗑️ Action de suppression disponible : {'✅ Oui' if has_delete else '❌ Non'}")
    
    print()
    
    # Vérifier les actions pour Stagiaire
    print("🔍 Actions disponibles pour Stagiaire :")
    stagiaire_admin = StagiaireAdmin(Stagiaire, admin.site)
    stagiaire_actions = stagiaire_admin.get_actions(None)
    
    for action_name, (action_func, name, description) in stagiaire_actions.items():
        print(f"   • {action_name}: {description}")
    
    has_stagiaire_delete = 'delete_selected' in stagiaire_actions
    print(f"\n🗑️ Action de suppression pour Stagiaire : {'✅ Oui' if has_stagiaire_delete else '❌ Non'}")
    
    return has_delete and has_stagiaire_delete

def test_model_deletion():
    """Tester la suppression directe des modèles"""
    print("\n" + "=" * 60)
    print("🧪 TEST DE SUPPRESSION DIRECTE")
    print("=" * 60)
    
    # Créer un utilisateur de test
    test_user = CustomUser.objects.create(
        username='test_admin_deletion',
        email='<EMAIL>',
        first_name='Test',
        last_name='AdminDeletion',
        role='ENCADRANT'
    )
    test_user.set_password('test123')
    test_user.save()
    
    print(f"✅ Utilisateur de test créé : {test_user.get_full_name()} (ID: {test_user.id})")
    
    # Tester la suppression directe
    try:
        user_id = test_user.id
        test_user.delete()
        print(f"✅ Suppression directe réussie (ID: {user_id})")
        
        # Vérifier que l'utilisateur n'existe plus
        try:
            CustomUser.objects.get(id=user_id)
            print("❌ ERREUR : L'utilisateur existe encore après suppression")
            return False
        except CustomUser.DoesNotExist:
            print("✅ Confirmation : L'utilisateur a bien été supprimé")
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors de la suppression directe : {e}")
        return False

def test_stagiaire_deletion():
    """Tester la suppression d'un stagiaire"""
    print("\n" + "=" * 60)
    print("🧪 TEST DE SUPPRESSION DE STAGIAIRE")
    print("=" * 60)
    
    # Créer un encadrant de test
    encadrant = CustomUser.objects.create(
        username='encadrant_test_deletion',
        email='<EMAIL>',
        first_name='Encadrant',
        last_name='Test',
        role='ENCADRANT'
    )
    encadrant.set_password('test123')
    encadrant.save()
    
    # Créer un stagiaire de test
    from datetime import date
    stagiaire = Stagiaire.objects.create(
        nom='TestSuppression',
        prenom='Stagiaire',
        email='<EMAIL>',
        date_naissance=date(2000, 1, 1),
        telephone='0123456789',
        departement='Test',
        encadrant=encadrant,
        date_debut=date.today(),
        date_fin=date.today(),
        etablissement='Test University',
        niveau_etude='Master',
        specialite='Test',
        cree_par=encadrant
    )
    
    print(f"✅ Stagiaire de test créé : {stagiaire.nom_complet} (ID: {stagiaire.id})")
    
    # Tester la suppression
    try:
        stagiaire_id = stagiaire.id
        stagiaire.delete()
        print(f"✅ Suppression de stagiaire réussie (ID: {stagiaire_id})")
        
        # Vérifier que le stagiaire n'existe plus
        try:
            Stagiaire.objects.get(id=stagiaire_id)
            print("❌ ERREUR : Le stagiaire existe encore après suppression")
            success = False
        except Stagiaire.DoesNotExist:
            print("✅ Confirmation : Le stagiaire a bien été supprimé")
            success = True
            
    except Exception as e:
        print(f"❌ Erreur lors de la suppression du stagiaire : {e}")
        success = False
    
    # Nettoyer l'encadrant
    try:
        encadrant.delete()
        print("✅ Encadrant de test nettoyé")
    except Exception as e:
        print(f"⚠️ Erreur lors du nettoyage de l'encadrant : {e}")
    
    return success

def test_admin_permissions():
    """Tester les permissions dans l'admin"""
    print("\n" + "=" * 60)
    print("🔒 TEST DES PERMISSIONS ADMIN")
    print("=" * 60)
    
    # Créer un superuser pour les tests
    admin_user = CustomUser.objects.filter(is_superuser=True).first()
    if not admin_user:
        admin_user = CustomUser.objects.create_superuser(
            username='test_superuser',
            email='<EMAIL>',
            password='test123',
            role='ADMIN'
        )
        print("✅ Superuser de test créé")
    else:
        print(f"✅ Superuser existant : {admin_user.username}")
    
    # Vérifier les permissions
    print(f"   • is_superuser: {admin_user.is_superuser}")
    print(f"   • is_staff: {admin_user.is_staff}")
    print(f"   • is_active: {admin_user.is_active}")
    print(f"   • role: {admin_user.role}")
    
    # Vérifier les permissions spécifiques
    perms = [
        'stagiaires.add_customuser',
        'stagiaires.change_customuser', 
        'stagiaires.delete_customuser',
        'stagiaires.view_customuser',
        'stagiaires.add_stagiaire',
        'stagiaires.change_stagiaire',
        'stagiaires.delete_stagiaire',
        'stagiaires.view_stagiaire'
    ]
    
    print("\n📋 Permissions spécifiques :")
    for perm in perms:
        has_perm = admin_user.has_perm(perm)
        status = "✅" if has_perm else "❌"
        print(f"   {status} {perm}")
    
    return admin_user.is_superuser and admin_user.is_staff

def check_admin_urls():
    """Vérifier l'accès aux URLs de l'admin"""
    print("\n" + "=" * 60)
    print("🌐 TEST D'ACCÈS AUX URLS ADMIN")
    print("=" * 60)
    
    from django.urls import reverse
    
    try:
        # URLs importantes de l'admin
        urls_to_test = [
            ('admin:index', 'Page d\'accueil admin'),
            ('admin:stagiaires_customuser_changelist', 'Liste des utilisateurs'),
            ('admin:stagiaires_stagiaire_changelist', 'Liste des stagiaires'),
        ]
        
        for url_name, description in urls_to_test:
            try:
                url = reverse(url_name)
                print(f"   ✅ {description}: {url}")
            except Exception as e:
                print(f"   ❌ {description}: Erreur - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification des URLs : {e}")
        return False

def main():
    """Fonction principale de diagnostic"""
    print("🔍 DIAGNOSTIC COMPLET DE L'ADMIN DJANGO")
    print("🎯 Identifier pourquoi les suppressions ne fonctionnent pas")
    print("=" * 70)
    
    try:
        # Tests de diagnostic
        test1 = test_admin_configuration()
        test2 = test_model_deletion()
        test3 = test_stagiaire_deletion()
        test4 = test_admin_permissions()
        test5 = check_admin_urls()
        
        print("\n" + "=" * 60)
        print("📊 RÉSUMÉ DU DIAGNOSTIC")
        print("=" * 60)
        
        tests = [
            ("Configuration admin", test1),
            ("Suppression directe", test2),
            ("Suppression stagiaire", test3),
            ("Permissions admin", test4),
            ("URLs admin", test5)
        ]
        
        all_passed = True
        for test_name, result in tests:
            status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
            print(f"   {status}: {test_name}")
            if not result:
                all_passed = False
        
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 TOUS LES TESTS RÉUSSIS")
            print("✅ La suppression devrait fonctionner dans l'admin")
            print("\n📋 INSTRUCTIONS :")
            print("1. Accédez à l'admin : http://127.0.0.1:8000/admin/")
            print("2. Connectez-vous avec un compte superuser")
            print("3. Essayez de supprimer un utilisateur ou stagiaire")
        else:
            print("❌ PROBLÈMES DÉTECTÉS")
            print("⚠️ Certains tests ont échoué, voir les détails ci-dessus")
            print("\n🔧 SOLUTIONS POSSIBLES :")
            print("1. Vérifier que vous êtes connecté avec un superuser")
            print("2. Vérifier les permissions dans la base de données")
            print("3. Redémarrer le serveur Django")
            print("4. Vérifier les signaux Django qui pourraient bloquer la suppression")
        
        return all_passed
        
    except Exception as e:
        print(f"\n❌ Erreur lors du diagnostic : {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
