#!/usr/bin/env python
"""
Script de test pour vérifier le système de gestion des stagiaires
"""

import os
import sys
import django
from datetime import date, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_stagiaires.settings')
django.setup()

from stagiaires.models import CustomUser, Stagiaire

def test_stagiaire_system():
    """Test complet du système de stagiaires"""
    
    print("=== TEST DU SYSTÈME DE GESTION DES STAGIAIRES ===\n")
    
    # 1. Vérifier les utilisateurs existants
    print("1. UTILISATEURS EXISTANTS:")
    print("-" * 40)
    users = CustomUser.objects.all()
    for user in users:
        print(f"   - {user.username} ({user.get_role_display()})")
        if user.first_name and user.last_name:
            print(f"     Nom complet: {user.get_full_name()}")
    print(f"   Total: {users.count()} utilisateurs\n")
    
    # 2. Vérifier les encadrants disponibles
    print("2. ENCADRANTS DISPONIBLES:")
    print("-" * 40)
    encadrants = CustomUser.objects.filter(role='ENCADRANT')
    for encadrant in encadrants:
        print(f"   - {encadrant.get_full_name() or encadrant.username} ({encadrant.email})")
    print(f"   Total: {encadrants.count()} encadrants\n")
    
    # 3. Créer un stagiaire de test
    print("3. CRÉATION D'UN STAGIAIRE DE TEST:")
    print("-" * 40)
    
    # Vérifier si le stagiaire existe déjà
    test_email = "<EMAIL>"
    existing_stagiaire = Stagiaire.objects.filter(email=test_email).first()
    
    if existing_stagiaire:
        print(f"   Stagiaire de test déjà existant: {existing_stagiaire.nom_complet}")
        stagiaire = existing_stagiaire
    else:
        # Prendre le premier encadrant disponible
        encadrant = encadrants.first() if encadrants.exists() else None
        rh_user = CustomUser.objects.filter(role='RH').first()
        
        stagiaire = Stagiaire.objects.create(
            nom="Dupont",
            prenom="Jean",
            email=test_email,
            telephone="0123456789",
            date_naissance=date(2000, 5, 15),
            departement="IT",
            encadrant=encadrant,
            date_debut=date.today(),
            date_fin=date.today() + timedelta(days=180),  # 6 mois
            etablissement="Université de Test",
            niveau_etude="Master 2",
            specialite="Informatique",
            statut="EN_COURS",
            cree_par=rh_user
        )
        print(f"   ✓ Stagiaire créé: {stagiaire.nom_complet}")
    
    print(f"   - Email: {stagiaire.email}")
    print(f"   - Département: {stagiaire.get_departement_display()}")
    print(f"   - Encadrant: {stagiaire.encadrant.get_full_name() if stagiaire.encadrant else 'Non assigné'}")
    print(f"   - Période: {stagiaire.date_debut} - {stagiaire.date_fin}")
    print(f"   - Durée: {stagiaire.duree_stage} jours")
    print(f"   - Statut: {stagiaire.get_statut_display()}")
    print(f"   - Créé par: {stagiaire.cree_par.username if stagiaire.cree_par else 'Inconnu'}\n")
    
    # 4. Lister tous les stagiaires
    print("4. TOUS LES STAGIAIRES:")
    print("-" * 40)
    stagiaires = Stagiaire.objects.all().select_related('encadrant', 'cree_par')
    
    if stagiaires.exists():
        for i, stg in enumerate(stagiaires, 1):
            print(f"   {i}. {stg.nom_complet}")
            print(f"      Email: {stg.email}")
            print(f"      Département: {stg.get_departement_display()}")
            print(f"      Encadrant: {stg.encadrant.get_full_name() if stg.encadrant else 'Non assigné'}")
            print(f"      Statut: {stg.get_statut_display()}")
            print(f"      Créé le: {stg.date_creation.strftime('%d/%m/%Y à %H:%M')}")
            print()
        print(f"   Total: {stagiaires.count()} stagiaire(s)")
    else:
        print("   Aucun stagiaire trouvé.")
    
    print("\n" + "=" * 50)
    print("TEST TERMINÉ AVEC SUCCÈS!")
    print("=" * 50)
    
    # 5. Instructions pour tester l'interface web
    print("\nPOUR TESTER L'INTERFACE WEB:")
    print("-" * 30)
    print("1. Assurez-vous que le serveur Django fonctionne:")
    print("   python manage.py runserver")
    print("\n2. Ouvrez votre navigateur et allez à:")
    print("   http://127.0.0.1:8000/")
    print("\n3. Connectez-vous avec un compte RH ou Admin:")
    print("   - Admin: admin / [mot de passe admin]")
    print("   - RH: amineamine / [mot de passe]")
    print("\n4. Testez les fonctionnalités:")
    print("   - Ajouter un stagiaire (côté RH)")
    print("   - Voir la liste des stagiaires (côté RH et Encadrant)")
    print("   - Vérifier que les données sont partagées entre les rôles")

if __name__ == "__main__":
    try:
        test_stagiaire_system()
    except Exception as e:
        print(f"ERREUR: {e}")
        import traceback
        traceback.print_exc()
